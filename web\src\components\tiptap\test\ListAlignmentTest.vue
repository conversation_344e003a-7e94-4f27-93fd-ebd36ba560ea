<template>
  <div class="list-alignment-test">
    <h2>列表对齐测试页面</h2>
    <p>这个页面用于测试列表在不同对齐方式下的显示效果</p>
    
    <div class="test-section">
      <h3>测试说明</h3>
      <ol>
        <li>创建有序列表和无序列表</li>
        <li>使用工具栏的对齐按钮测试不同对齐方式</li>
        <li>检查列表序号/符号是否跟随文本对齐</li>
        <li>验证任务列表的对齐效果</li>
      </ol>
    </div>

    <TipTapEditor
      v-model="content"
      :extensions="['bulletList', 'orderedList', 'taskList', 'align']"
      :toolbar="true"
      file-bucket="test"
      placeholder="在这里测试列表对齐功能..."
      class="test-editor"
    />

    <div class="test-instructions">
      <h3>测试步骤</h3>
      <ul>
        <li>创建一个有序列表（1. 2. 3.）</li>
        <li>创建一个无序列表（• • •）</li>
        <li>创建一个任务列表（☐ ☑ ☐）</li>
        <li>选中列表，使用对齐按钮测试：
          <ul>
            <li>左对齐（默认）</li>
            <li>居中对齐</li>
            <li>右对齐</li>
            <li>两端对齐</li>
          </ul>
        </li>
        <li>观察序号/符号是否正确跟随对齐</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TipTapEditor from '../core/TipTapEditor.vue'

const content = ref(`
<h2>列表对齐测试</h2>

<h3>有序列表测试</h3>
<ol>
  <li>第一项内容</li>
  <li>第二项内容</li>
  <li>第三项内容</li>
</ol>

<h3>无序列表测试</h3>
<ul>
  <li>第一项内容</li>
  <li>第二项内容</li>
  <li>第三项内容</li>
</ul>

<h3>任务列表测试</h3>
<ul data-type="taskList">
  <li data-checked="false">未完成任务</li>
  <li data-checked="true">已完成任务</li>
  <li data-checked="false">另一个未完成任务</li>
</ul>

<p>请选中上面的列表，然后使用工具栏的对齐按钮测试不同的对齐效果。</p>
`)


</script>

<style scoped lang="scss">
.list-alignment-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;

  h2 {
    color: var(--primary-color, #2d8cf0);
    margin-bottom: 1rem;
  }

  h3 {
    color: var(--text-color-1, #333);
    margin: 1.5rem 0 1rem 0;
  }

  .test-section {
    background-color: var(--card-color, #f8f9fa);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
  }

  .test-editor {
    margin: 2rem 0;
    min-height: 400px;
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 8px;
  }

  .test-instructions {
    background-color: var(--info-color-light, #e6f7ff);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 2rem;

    ul {
      margin: 0.5rem 0;
    }

    li {
      margin: 0.25rem 0;
    }
  }
}
</style>
