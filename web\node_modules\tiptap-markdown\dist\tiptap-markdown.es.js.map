{"version": 3, "file": "tiptap-markdown.es.js", "sources": ["../src/extensions/tiptap/tight-lists.js", "../src/util/markdown.js", "../src/serialize/state.js", "../src/extensions/marks/html.js", "../src/util/dom.js", "../src/extensions/nodes/html.js", "../src/extensions/nodes/blockquote.js", "../src/extensions/nodes/bullet-list.js", "../src/extensions/nodes/code-block.js", "../src/extensions/nodes/hard-break.js", "../src/extensions/nodes/heading.js", "../src/extensions/nodes/horizontal-rule.js", "../src/extensions/nodes/image.js", "../src/extensions/nodes/list-item.js", "../src/extensions/nodes/ordered-list.js", "../src/extensions/nodes/paragraph.js", "../src/util/prosemirror.js", "../src/extensions/nodes/table.js", "../src/extensions/nodes/task-item.js", "../src/extensions/nodes/task-list.js", "../src/extensions/nodes/text.js", "../src/extensions/marks/bold.js", "../src/extensions/marks/code.js", "../src/extensions/marks/italic.js", "../src/extensions/marks/link.js", "../src/extensions/marks/strike.js", "../src/extensions/index.js", "../src/util/extensions.js", "../src/serialize/MarkdownSerializer.js", "../src/parse/MarkdownParser.js", "../src/extensions/tiptap/clipboard.js", "../src/Markdown.js"], "sourcesContent": ["import { Extension } from \"@tiptap/core\";\n\nexport const MarkdownTightLists = Extension.create({\n    name: 'markdownTightLists',\n    addOptions: () => ({\n        tight: true,\n        tightClass: 'tight',\n        listTypes: [\n            'bulletList',\n            'orderedList',\n        ],\n    }),\n    addGlobalAttributes() {\n        return [\n            {\n                types: this.options.listTypes,\n                attributes: {\n                    tight: {\n                        default: this.options.tight,\n                        parseHTML: element =>\n                            element.getAttribute('data-tight') === 'true' || !element.querySelector('p'),\n                        renderHTML: attributes => ({\n                            class: attributes.tight ? this.options.tightClass : null,\n                            'data-tight': attributes.tight ? 'true' : null,\n                        }),\n                    },\n                },\n            },\n        ]\n    },\n    addCommands() {\n        return {\n            toggleTight: (tight = null) => ({ editor, commands }) => {\n                function toggleTight(name) {\n                    if(!editor.isActive(name)) {\n                        return false;\n                    }\n                    const attrs = editor.getAttributes(name);\n                    return commands.updateAttributes(name, {\n                        tight: tight ?? !attrs?.tight,\n                    });\n                }\n                return this.options.listTypes\n                    .some(name => toggleTight(name));\n            }\n        }\n    },\n});\n", "import markdownit from 'markdown-it';\n\nconst md = markdownit();\n\nfunction scanDelims(text, pos) {\n    md.inline.State.prototype.scanDelims.call({ src: text, posMax: text.length })\n    const state = new (md.inline.State)(text, null, null, []);\n    return state.scanDelims(pos, true);\n}\n\nexport function shiftDelim(text, delim, start, offset) {\n    let res = text.substring(0, start) + text.substring(start + delim.length);\n    res = res.substring(0, start + offset) + delim + res.substring(start + offset);\n    return res;\n}\n\nfunction trimStart(text, delim, from, to) {\n    let pos = from, res = text;\n    while(pos < to) {\n        if(scanDelims(res, pos).can_open) {\n            break;\n        }\n        res = shiftDelim(res, delim, pos, 1);\n        pos++;\n    }\n    return { text: res, from: pos, to }\n}\n\nfunction trimEnd(text, delim, from, to) {\n    let pos = to, res = text;\n    while(pos > from) {\n        if(scanDelims(res, pos).can_close) {\n            break;\n        }\n        res = shiftDelim(res, delim, pos, -1);\n        pos--;\n    }\n    return { text: res, from, to: pos }\n}\n\nexport function trimInline(text, delim, from, to) {\n    let state = {\n        text,\n        from,\n        to,\n    }\n\n    state = trimStart(state.text, delim, state.from, state.to);\n    state = trimEnd(state.text, delim, state.from, state.to);\n\n    if(state.to - state.from < delim.length + 1) {\n        state.text = state.text.substring(0, state.from) + state.text.substring(state.to + delim.length);\n    }\n\n    return state.text;\n}\n", "import { MarkdownSerializerState as BaseMarkdownSerializerState } from \"prosemirror-markdown\";\nimport { trimInline } from \"../util/markdown\";\n\n\n/**\n * Override default MarkdownSerializerState to:\n * - handle commonmark delimiters (https://spec.commonmark.org/0.29/#left-flanking-delimiter-run)\n */\nexport class MarkdownSerializerState extends BaseMarkdownSerializerState {\n\n    inTable = false;\n\n    constructor(nodes, marks, options) {\n        super(nodes, marks, options ?? {});\n        this.inlines = [];\n    }\n\n    render(node, parent, index) {\n        super.render(node, parent, index);\n        const top = this.inlines[this.inlines.length - 1];\n        if(top?.start && top?.end) {\n            const { delimiter, start, end } = this.normalizeInline(top);\n            this.out = trimInline(this.out, delimiter, start, end);\n            this.inlines.pop();\n        }\n    }\n\n    markString(mark, open, parent, index) {\n        const info = this.marks[mark.type.name]\n        if(info.expelEnclosingWhitespace) {\n            if(open) {\n                this.inlines.push({\n                    start: this.out.length,\n                    delimiter: info.open,\n                });\n            } else {\n                const top = this.inlines.pop();\n                this.inlines.push({\n                    ...top,\n                    end: this.out.length,\n                });\n            }\n        }\n        return super.markString(mark, open, parent, index);\n    }\n\n    normalizeInline(inline) {\n        let { start, end } = inline;\n        while(this.out.charAt(start).match(/\\s/)) {\n            start++;\n        }\n        return {\n            ...inline,\n            start,\n        }\n    }\n}\n", "import { Fragment } from \"@tiptap/pm/model\";\nimport { getHTMLFromFragment, Mark } from \"@tiptap/core\";\n\n\nexport default Mark.create({\n    name: 'markdownHTMLMark',\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: {\n                    open(state, mark)  {\n                        if(!this.editor.storage.markdown.options.html) {\n                            console.warn(`Tiptap Markdown: \"${mark.type.name}\" mark is only available in html mode`);\n                            return '';\n                        }\n                        return getMarkTags(mark)?.[0] ?? '';\n                    },\n                    close(state, mark) {\n                        if(!this.editor.storage.markdown.options.html) {\n                            return '';\n                        }\n                        return getMarkTags(mark)?.[1] ?? '';\n                    },\n                },\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n});\n\nfunction getMarkTags(mark) {\n    const schema = mark.type.schema;\n    const node = schema.text(' ', [mark]);\n    const html = getHTMLFromFragment(Fragment.from(node), schema);\n    const match = html.match(/^(<.*?>) (<\\/.*?>)$/);\n    return match ? [match[1], match[2]] : null;\n}\n", "\n\nexport function elementFromString(value) {\n    // add a wrapper to preserve leading and trailing whitespace\n    const wrappedValue = `<body>${value}</body>`\n\n    return new window.DOMParser().parseFromString(wrappedValue, 'text/html').body\n}\n\nexport function escapeHTML(value) {\n    return value\n        ?.replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\n\nexport function extractElement(node) {\n    const parent = node.parentElement;\n    const prepend = parent.cloneNode();\n\n    while(parent.firstChild && parent.firstChild !== node) {\n        prepend.appendChild(parent.firstChild);\n    }\n\n    if(prepend.childNodes.length > 0) {\n        parent.parentElement.insertBefore(prepend, parent);\n    }\n    parent.parentElement.insertBefore(node, parent);\n    if(parent.childNodes.length === 0) {\n        parent.remove();\n    }\n}\n\nexport function unwrapElement(node) {\n    const parent = node.parentNode;\n\n    while (node.firstChild) parent.insertBefore(node.firstChild, node);\n\n    parent.removeChild(node);\n}\n", "import { Fragment } from \"@tiptap/pm/model\";\nimport { getHTMLFromFragment, Node } from \"@tiptap/core\";\nimport { elementFromString } from \"../../util/dom\";\n\n\nexport default Node.create({\n    name: 'markdownHTMLNode',\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent) {\n                    if(this.editor.storage.markdown.options.html) {\n                        state.write(serializeHTML(node, parent));\n                    } else {\n                        console.warn(`Tiptap Markdown: \"${node.type.name}\" node is only available in html mode`);\n                        state.write(`[${node.type.name}]`);\n                    }\n                    if(node.isBlock) {\n                        state.closeBlock(node);\n                    }\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            },\n        }\n    }\n});\n\nfunction serializeHTML(node, parent) {\n    const schema = node.type.schema;\n    const html = getHTMLFromFragment(Fragment.from(node), schema);\n\n    if(node.isBlock && (parent instanceof Fragment || parent.type.name === schema.topNodeType.name)) {\n        return formatBlock(html);\n    }\n\n    return html;\n}\n\n/**\n * format html block as per the commonmark spec\n */\nfunction formatBlock(html) {\n    const dom = elementFromString(html);\n    const element = dom.firstElementChild;\n\n    element.innerHTML = element.innerHTML.trim()\n        ? `\\n${element.innerHTML}\\n`\n        : `\\n`;\n\n    return element.outerHTML;\n}\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Blockquote = Node.create({\n    name: 'blockquote',\n});\n\nexport default Blockquote.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.blockquote,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\n\n\nconst BulletList = Node.create({\n    name: 'bulletList',\n});\n\nexport default BulletList.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    return state.renderList(node, \"  \", () => (this.editor.storage.markdown.options.bulletListMarker || \"-\") + \" \");\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\n\n\nconst CodeBlock = Node.create({\n    name: 'codeBlock',\n});\n\nexport default CodeBlock.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    state.write(\"```\" + (node.attrs.language || \"\") + \"\\n\");\n                    state.text(node.textContent, false);\n                    state.ensureNewLine();\n                    state.write(\"```\");\n                    state.closeBlock(node);\n                },\n                parse: {\n                    setup(markdownit) {\n                        markdownit.set({\n                            langPrefix: this.options.languageClassPrefix ?? 'language-',\n                        });\n                    },\n                    updateDOM(element) {\n                        element.innerHTML = element.innerHTML.replace(/\\n<\\/code><\\/pre>/g, '</code></pre>')\n                    },\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport HTMLNode from './html';\n\nconst HardBreak = Node.create({\n    name: 'hardBreak',\n});\n\nexport default HardBreak.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent, index) {\n                    for (let i = index + 1; i < parent.childCount; i++)\n                        if (parent.child(i).type != node.type) {\n                            state.write(\n                                state.inTable\n                                    ? HTMLNode.storage.markdown.serialize.call(this, state, node, parent)\n                                    : \"\\\\\\n\"\n                            );\n                            return;\n                        }\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Heading = Node.create({\n    name: 'heading',\n});\n\nexport default Heading.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.heading,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst HorizontalRule = Node.create({\n    name: 'horizontalRule',\n});\n\nexport default HorizontalRule.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.horizontal_rule,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Image = Node.create({\n    name: 'image',\n});\n\nexport default Image.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.image,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst ListItem = Node.create({\n    name: 'listItem',\n});\n\nexport default ListItem.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.list_item,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\n\n\nconst OrderedList = Node.create({\n    name: 'orderedList',\n});\n\nfunction findIndexOfAdjacentNode(node, parent, index) {\n    let i = 0;\n    for (; index - i > 0; i++) {\n        if (parent.child(index - i - 1).type.name !== node.type.name) {\n            break;\n        }\n    }\n    return i;\n}\n\nexport default OrderedList.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent, index) {\n                    const start = node.attrs.start || 1\n                    const maxW = String(start + node.childCount - 1).length\n                    const space = state.repeat(\" \", maxW + 2)\n                    const adjacentIndex = findIndexOfAdjacentNode(node, parent, index);\n                    const separator = adjacentIndex % 2 ? ') ' : '. ';\n                    state.renderList(node, space, i => {\n                        const nStr = String(start + i)\n                        return state.repeat(\" \", maxW - nStr.length) + nStr + separator;\n                    })\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Paragraph = Node.create({\n    name: 'paragraph',\n});\n\nexport default Paragraph.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.paragraph,\n                parse: {\n                    // handled by markdown-it\n                },\n            },\n        }\n    }\n});\n", "\n\nexport function childNodes(node) {\n    return node?.content?.content ?? [];\n}\n", "import { Node } from \"@tiptap/core\";\nimport { childNodes } from \"../../util/prosemirror\";\nimport HTMLNode from './html';\n\nconst Table = Node.create({\n    name: 'table',\n});\n\nexport default Table.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent) {\n                    if(!isMarkdownSerializable(node)) {\n                        HTMLNode.storage.markdown.serialize.call(this, state, node, parent);\n                        return;\n                    }\n                    state.inTable = true;\n                    node.forEach((row, p, i) => {\n                        state.write('| ');\n                        row.forEach((col, p, j) => {\n                            if(j) {\n                                state.write(' | ');\n                            }\n                            const cellContent = col.firstChild;\n                            if(cellContent.textContent.trim()) {\n                                state.renderInline(cellContent);\n                            }\n                        });\n                        state.write(' |')\n                        state.ensureNewLine();\n                        if(!i) {\n                            const delimiterRow = Array.from({length: row.childCount}).map(() => '---').join(' | ');\n                            state.write(`| ${delimiterRow} |`);\n                            state.ensureNewLine();\n                        }\n                    });\n                    state.closeBlock(node);\n                    state.inTable = false;\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n})\n\n\nfunction hasSpan(node) {\n    return node.attrs.colspan > 1 || node.attrs.rowspan > 1;\n}\n\nfunction isMarkdownSerializable(node) {\n    const rows = childNodes(node);\n    const firstRow = rows[0];\n    const bodyRows = rows.slice(1);\n\n    if(childNodes(firstRow).some(cell => cell.type.name !== 'tableHeader' || hasSpan(cell) || cell.childCount > 1)) {\n        return false;\n    }\n\n    if(bodyRows.some(row =>\n        childNodes(row).some(cell => cell.type.name === 'tableHeader' || hasSpan(cell) || cell.childCount > 1)\n    )) {\n        return false;\n    }\n\n    return true;\n}\n", "import { Node } from \"@tiptap/core\";\n\n\nconst TaskItem = Node.create({\n    name: 'taskItem',\n});\n\nexport default TaskItem.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    const check = node.attrs.checked ? '[x]' : '[ ]';\n                    state.write(`${check} `);\n                    state.renderContent(node);\n                },\n                parse: {\n                    updateDOM(element) {\n                        [...element.querySelectorAll('.task-list-item')]\n                            .forEach(item => {\n                                const input = item.querySelector('input');\n                                item.setAttribute('data-type', 'taskItem');\n                                if(input) {\n                                    item.setAttribute('data-checked', input.checked);\n                                    input.remove();\n                                }\n                            });\n                    },\n                }\n            }\n        }\n    }\n});\n", "import taskListPlugin from \"markdown-it-task-lists\";\nimport { Node } from \"@tiptap/core\";\nimport BulletList from \"./bullet-list\";\n\n\nconst TaskList = Node.create({\n    name: 'taskList',\n});\n\nexport default TaskList.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: BulletList.storage.markdown.serialize,\n                parse: {\n                    setup(markdownit) {\n                        markdownit.use(taskListPlugin);\n                    },\n                    updateDOM(element) {\n                        [...element.querySelectorAll('.contains-task-list')]\n                            .forEach(list => {\n                                list.setAttribute('data-type', 'taskList');\n                            });\n                    },\n                }\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { escapeHTML } from \"../../util/dom\";\n\n\nconst Text = Node.create({\n    name: 'text',\n});\n\nexport default Text.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    state.text(escapeHTML(node.text));\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Bold = Mark.create({\n    name: 'bold',\n});\n\nexport default Bold.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.strong,\n                parse: {\n                    // handled by markdown-it\n                }\n            },\n        }\n    }\n});\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Code = Mark.create({\n    name: 'code',\n});\n\nexport default Code.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.code,\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n})\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Italic = Mark.create({\n    name: 'italic',\n});\n\nexport default Italic.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.em,\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n})\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Link = Mark.create({\n    name: 'link',\n});\n\nexport default Link.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.link,\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n})\n", "import { Mark } from \"@tiptap/core\";\n\n\nconst Strike = Mark.create({\n    name: 'strike',\n});\n\nexport default Strike.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: {open: '~~', close: '~~', expelEnclosingWhitespace: true},\n                parse: {\n                    // handled by markdown-it\n                },\n            },\n        }\n    }\n});\n", "import Blockquote from \"./nodes/blockquote\";\nimport BulletList from \"./nodes/bullet-list\";\nimport CodeB<PERSON> from \"./nodes/code-block\";\nimport HardBreak from \"./nodes/hard-break\";\nimport Heading from \"./nodes/heading\";\nimport HorizontalRule from \"./nodes/horizontal-rule\";\nimport HTMLNode from \"./nodes/html\";\nimport Image from \"./nodes/image\";\nimport ListItem from \"./nodes/list-item\";\nimport OrderedList from \"./nodes/ordered-list\";\nimport Paragraph from \"./nodes/paragraph\";\nimport Table from \"./nodes/table\";\nimport TaskItem from \"./nodes/task-item\";\nimport TaskList from \"./nodes/task-list\";\nimport Text from \"./nodes/text\";\n\nimport Bold from \"./marks/bold\";\nimport Code from \"./marks/code\";\nimport HTMLMark from \"./marks/html\";\nimport Italic from \"./marks/italic\";\nimport Link from \"./marks/link\";\nimport Strike from \"./marks/strike\";\n\n\nexport default [\n    Blockquote,\n    BulletList,\n    CodeBlock,\n    HardBreak,\n    Heading,\n    HorizontalRule,\n    HTMLNode,\n    Image,\n    ListItem,\n    OrderedList,\n    Paragraph,\n    Table,\n    TaskItem,\n    TaskList,\n    Text,\n\n    Bold,\n    Code,\n    HTMLMark,\n    Italic,\n    Link,\n    Strike,\n]\n", "import markdownExtensions from \"../extensions\";\n\n\nexport function getMarkdownSpec(extension) {\n    const markdownSpec = extension.storage?.markdown;\n    const defaultMarkdownSpec = markdownExtensions.find(e => e.name === extension.name)?.storage.markdown;\n\n    if(markdownSpec || defaultMarkdownSpec) {\n        return {\n            ...defaultMarkdownSpec,\n            ...markdownSpec,\n        };\n    }\n\n    return null;\n}\n", "import { MarkdownSerializerState } from './state';\nimport HTMLMark from \"../extensions/marks/html\";\nimport HTMLNode from \"../extensions/nodes/html\";\nimport { getMarkdownSpec } from \"../util/extensions\";\nimport HardBreak from \"../extensions/nodes/hard-break\";\n\n\nexport class MarkdownSerializer {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    editor = null;\n\n    constructor(editor) {\n        this.editor = editor;\n    }\n\n    serialize(content) {\n        const state = new MarkdownSerializerState(this.nodes, this.marks, {\n            hardBreakNodeName: HardBreak.name,\n        });\n\n        state.renderContent(content);\n\n        return state.out;\n    }\n\n    get nodes() {\n        return {\n            ...Object.fromEntries(\n                Object.keys(this.editor.schema.nodes)\n                    .map(name => [name, this.serializeNode(HTMLNode)])\n            ),\n            ...Object.fromEntries(\n                this.editor.extensionManager.extensions\n                    .filter(extension => extension.type === 'node' && this.serializeNode(extension))\n                    .map(extension => [extension.name, this.serializeNode(extension)])\n                ?? []\n            ),\n        };\n    }\n\n    get marks() {\n        return {\n            ...Object.fromEntries(\n                Object.keys(this.editor.schema.marks)\n                    .map(name => [name, this.serializeMark(HTMLMark)])\n            ),\n            ...Object.fromEntries(\n                this.editor.extensionManager.extensions\n                    .filter(extension => extension.type === 'mark' && this.serializeMark(extension))\n                    .map(extension => [extension.name, this.serializeMark(extension)])\n                ?? []\n            ),\n        };\n    }\n\n    serializeNode(node) {\n        return getMarkdownSpec(node)?.serialize?.bind({ editor: this.editor, options: node.options });\n    }\n\n    serializeMark(mark) {\n        const serialize = getMarkdownSpec(mark)?.serialize;\n        return serialize\n            ? {\n                ...serialize,\n                open: typeof serialize.open === 'function' ? serialize.open.bind({ editor: this.editor, options: mark.options }) : serialize.open,\n                close: typeof serialize.close === 'function' ? serialize.close.bind({ editor: this.editor, options: mark.options }) : serialize.close,\n            }\n            : null\n    }\n}\n\n", "import markdownit from \"markdown-it\";\nimport { elementFromString, extractElement, unwrapElement } from \"../util/dom\";\nimport { getMarkdownSpec } from \"../util/extensions\";\n\nexport class MarkdownParser {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    editor = null;\n    /**\n     * @type {markdownit}\n     */\n    md = null;\n\n    constructor(editor, { html, linkify, breaks }) {\n        this.editor = editor;\n        this.md = this.withPatchedRenderer(markdownit({\n            html,\n            linkify,\n            breaks,\n        }));\n    }\n\n    parse(content, { inline } = {}) {\n        if(typeof content === 'string') {\n            this.editor.extensionManager.extensions.forEach(extension =>\n                getMarkdownSpec(extension)?.parse?.setup?.call({ editor:this.editor, options:extension.options }, this.md)\n            );\n\n            const renderedHTML = this.md.render(content);\n            const element = elementFromString(renderedHTML);\n\n            this.editor.extensionManager.extensions.forEach(extension =>\n                getMarkdownSpec(extension)?.parse?.updateDOM?.call({ editor:this.editor, options:extension.options }, element)\n            );\n\n            this.normalizeDOM(element, { inline, content });\n\n            return element.innerHTML;\n        }\n\n        return content;\n    }\n\n    normalizeDOM(node, { inline, content }) {\n        this.normalizeBlocks(node);\n\n        // remove all \\n appended by markdown-it\n        node.querySelectorAll('*').forEach(el => {\n            if(el.nextSibling?.nodeType === Node.TEXT_NODE && !el.closest('pre')) {\n                el.nextSibling.textContent = el.nextSibling.textContent.replace(/^\\n/, '');\n            }\n        });\n\n        if(inline) {\n            this.normalizeInline(node, content);\n        }\n\n        return node;\n    }\n\n    normalizeBlocks(node) {\n        const blocks = Object.values(this.editor.schema.nodes)\n            .filter(node => node.isBlock);\n\n        const selector = blocks\n            .map(block => block.spec.parseDOM?.map(spec => spec.tag))\n            .flat()\n            .filter(Boolean)\n            .join(',');\n\n        if(!selector) {\n            return;\n        }\n\n        [...node.querySelectorAll(selector)].forEach(el => {\n            if(el.parentElement.matches('p')) {\n                extractElement(el);\n            }\n        });\n    }\n\n    normalizeInline(node, content) {\n        if(node.firstElementChild?.matches('p')) {\n            const firstParagraph = node.firstElementChild;\n            const { nextElementSibling } = firstParagraph;\n            const startSpaces = content.match(/^\\s+/)?.[0] ?? '';\n            const endSpaces = !nextElementSibling\n                ? content.match(/\\s+$/)?.[0] ?? ''\n                : '';\n\n            if(content.match(/^\\n\\n/)) {\n                firstParagraph.innerHTML = `${firstParagraph.innerHTML}${endSpaces}`;\n                return;\n            }\n\n            unwrapElement(firstParagraph);\n\n            node.innerHTML = `${startSpaces}${node.innerHTML}${endSpaces}`;\n        }\n    }\n\n    /**\n     * @param {markdownit} md\n     */\n    withPatchedRenderer(md) {\n        const withoutNewLine = (renderer) => (...args) => {\n            const rendered = renderer(...args);\n            if(rendered === '\\n') {\n                return rendered; // keep soft breaks\n            }\n            if(rendered[rendered.length - 1] === '\\n') {\n                return rendered.slice(0, -1);\n            }\n            return rendered;\n        }\n\n        md.renderer.rules.hardbreak = withoutNewLine(md.renderer.rules.hardbreak);\n        md.renderer.rules.softbreak = withoutNewLine(md.renderer.rules.softbreak);\n        md.renderer.rules.fence = withoutNewLine(md.renderer.rules.fence);\n        md.renderer.rules.code_block = withoutNewLine(md.renderer.rules.code_block);\n        md.renderer.renderToken = withoutNewLine(md.renderer.renderToken.bind(md.renderer));\n\n        return md;\n    }\n}\n\n", "import { Extension } from \"@tiptap/core\";\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';\nimport { DOMParser } from '@tiptap/pm/model';\nimport { elementFromString } from \"../../util/dom\";\n\nexport const MarkdownClipboard = Extension.create({\n    name: 'markdownClipboard',\n    addOptions() {\n        return {\n            transformPastedText: false,\n            transformCopiedText: false,\n        }\n    },\n    addProseMirrorPlugins() {\n        return [\n            new Plugin({\n                key: new PluginKey('markdownClipboard'),\n                props: {\n                    clipboardTextParser: (text, context, plainText) => {\n                        if(plainText || !this.options.transformPastedText) {\n                            return null; // pasting with shift key prevents formatting\n                        }\n                        const parsed = this.editor.storage.markdown.parser.parse(text, { inline: true });\n                        return DOMParser.fromSchema(this.editor.schema)\n                            .parseSlice(elementFromString(parsed), {\n                                preserveWhitespace: true,\n                                context,\n                            });\n                    },\n                    /**\n                     * @param {import('prosemirror-model').Slice} slice\n                     */\n                    clipboardTextSerializer: (slice) => {\n                        if(!this.options.transformCopiedText) {\n                            return null;\n                        }\n                        return this.editor.storage.markdown.serializer.serialize(slice.content);\n                    },\n                },\n            })\n        ]\n    }\n})\n", "import { Extension, extensions } from '@tiptap/core';\nimport { MarkdownTightLists } from \"./extensions/tiptap/tight-lists\";\nimport { MarkdownSerializer } from \"./serialize/MarkdownSerializer\";\nimport { MarkdownParser } from \"./parse/MarkdownParser\";\nimport { MarkdownClipboard } from \"./extensions/tiptap/clipboard\";\n\nexport const Markdown = Extension.create({\n    name: 'markdown',\n    priority: 50,\n    addOptions() {\n        return {\n            html: true,\n            tightLists: true,\n            tightListClass: 'tight',\n            bulletListMarker: '-',\n            linkify: false,\n            breaks: false,\n            transformPastedText: false,\n            transformCopiedText: false,\n        }\n    },\n    addCommands() {\n        const commands = extensions.Commands.config.addCommands();\n        return {\n            setContent: (content, emitUpdate, parseOptions) => (props) => {\n                return commands.setContent(\n                    props.editor.storage.markdown.parser.parse(content),\n                    emitUpdate,\n                    parseOptions\n                )(props);\n            },\n            insertContentAt: (range, content, options) => (props) => {\n                return commands.insertContentAt(\n                    range,\n                    props.editor.storage.markdown.parser.parse(content, { inline: true }),\n                    options\n                )(props);\n            },\n        }\n    },\n    onBeforeCreate() {\n        this.editor.storage.markdown = {\n            options: { ...this.options },\n            parser: new MarkdownParser(this.editor, this.options),\n            serializer: new MarkdownSerializer(this.editor),\n            getMarkdown: () => {\n                return this.editor.storage.markdown.serializer.serialize(this.editor.state.doc);\n            },\n        }\n        this.editor.options.initialContent = this.editor.options.content;\n        this.editor.options.content = this.editor.storage.markdown.parser.parse(this.editor.options.content);\n    },\n    onCreate() {\n        this.editor.options.content = this.editor.options.initialContent;\n        delete this.editor.options.initialContent;\n    },\n    addStorage() {\n        return {\n            /// storage will be defined in onBeforeCreate() to prevent initial object overriding\n        }\n    },\n    addExtensions() {\n        return [\n            MarkdownTightLists.configure({\n                tight: this.options.tightLists,\n                tightClass: this.options.tightListClass,\n            }),\n            MarkdownClipboard.configure({\n                transformPastedText: this.options.transformPastedText,\n                transformCopiedText: this.options.transformCopiedText,\n            }),\n        ]\n    },\n});\n"], "names": ["MarkdownTightLists", "Extension", "create", "name", "addOptions", "tight", "tightClass", "listTypes", "addGlobalAttributes", "types", "options", "attributes", "default", "parseHTML", "element", "getAttribute", "querySelector", "renderHTML", "class", "addCommands", "_this", "toggleTight", "arguments", "length", "undefined", "_ref", "editor", "commands", "isActive", "attrs", "getAttributes", "updateAttributes", "some", "md", "markdownit", "scanDelims", "text", "pos", "inline", "State", "prototype", "call", "src", "posMax", "state", "<PERSON><PERSON><PERSON><PERSON>", "delim", "start", "offset", "res", "substring", "trimStart", "from", "to", "can_open", "trimEnd", "can_close", "trimInline", "MarkdownSerializerState", "BaseMarkdownSerializerState", "constructor", "nodes", "marks", "inTable", "inlines", "render", "node", "parent", "index", "top", "end", "delimiter", "normalizeInline", "out", "pop", "markString", "mark", "open", "info", "type", "expelEnclosingWhitespace", "push", "char<PERSON>t", "match", "<PERSON>", "addStorage", "markdown", "serialize", "_getMarkTags$", "_getMarkTags", "storage", "html", "console", "warn", "getMarkTags", "close", "_getMarkTags$2", "_getMarkTags2", "parse", "schema", "getHTMLFromFragment", "Fragment", "elementFromString", "value", "wrappedValue", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "body", "escapeHTML", "replace", "extractElement", "parentElement", "prepend", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "childNodes", "insertBefore", "remove", "unwrapElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Node", "write", "serializeHTML", "isBlock", "closeBlock", "topNodeType", "formatBlock", "dom", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "innerHTML", "trim", "outerHTML", "Blockquote", "extend", "defaultMarkdownSerializer", "blockquote", "BulletList", "renderList", "bullet<PERSON>ist<PERSON><PERSON><PERSON>", "CodeBlock", "language", "textContent", "ensureNewLine", "setup", "_this$options$languag", "set", "langPrefix", "languageClassPrefix", "updateDOM", "HardBreak", "i", "childCount", "child", "HTMLNode", "Heading", "heading", "HorizontalRule", "horizontal_rule", "Image", "image", "ListItem", "list_item", "OrderedList", "findIndexOfAdjacentNode", "maxW", "String", "space", "repeat", "adjacentIndex", "separator", "nStr", "Paragraph", "paragraph", "_node$content$content", "_node$content", "content", "Table", "isMarkdownSerializable", "for<PERSON>ach", "row", "p", "col", "j", "cellContent", "renderInline", "delimiterRow", "Array", "map", "join", "hasSpan", "colspan", "rowspan", "rows", "firstRow", "bodyRows", "slice", "cell", "TaskItem", "check", "checked", "renderContent", "querySelectorAll", "item", "input", "setAttribute", "TaskList", "use", "taskListPlugin", "list", "Text", "Bold", "strong", "Code", "code", "Italic", "em", "Link", "link", "Strike", "HTMLMark", "getMarkdownSpec", "extension", "_extension$storage", "_markdownExtensions$f", "markdownSpec", "defaultMarkdownSpec", "markdownExtensions", "find", "e", "MarkdownSerializer", "hardBreakNodeName", "_this$editor$extensio", "Object", "fromEntries", "keys", "serializeNode", "extensionManager", "extensions", "filter", "_this$editor$extensio2", "serializeMark", "_getMarkdownSpec", "bind", "_getMarkdownSpec2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "linkify", "breaks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderedHTML", "normalizeDOM", "_ref2", "normalizeBlocks", "el", "_el$nextSibling", "nextS<PERSON>ling", "nodeType", "TEXT_NODE", "closest", "blocks", "values", "selector", "block", "_block$spec$parseDOM", "spec", "parseDOM", "tag", "flat", "Boolean", "matches", "_node$firstElementChi", "_content$match$", "_content$match", "_content$match$2", "_content$match2", "firstParagraph", "nextElement<PERSON><PERSON>ling", "startSpaces", "endSpaces", "withoutNewLine", "renderer", "rendered", "rules", "hardbreak", "softbreak", "fence", "code_block", "renderToken", "MarkdownClipboard", "transformPastedText", "transformCopiedText", "addProseMirrorPlugins", "Plugin", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "clipboardTextParser", "context", "plainText", "parsed", "parser", "fromSchema", "parseSlice", "preserveWhitespace", "clipboardTextSerializer", "serializer", "<PERSON><PERSON>", "priority", "tightLists", "tightListClass", "Commands", "config", "<PERSON><PERSON><PERSON><PERSON>", "emitUpdate", "parseOptions", "insertContentAt", "range", "onBeforeCreate", "getMarkdown", "doc", "initialContent", "onCreate", "addExtensions", "configure"], "mappings": ";;;;;;;;;;;;AAEO,MAAMA,qBAAqBC,UAAUC,OAAO;AAAA,EAC/CC,MAAM;AAAA,EACNC,YAAYA,OAAO;AAAA,IACfC,OAAO;AAAA,IACPC,YAAY;AAAA,IACZC,WAAW,CACP,cACA,aAAa;AAAA,EAErB;AAAA,EACAC,sBAAsB;AAClB,WAAO,CACH;AAAA,MACIC,OAAO,KAAKC,QAAQH;AAAAA,MACpBI,YAAY;AAAA,QACRN,OAAO;AAAA,UACHO,SAAS,KAAKF,QAAQL;AAAAA,UACtBQ,WAAWC,aACPA,QAAQC,aAAa,YAAY,MAAM,UAAU,CAACD,QAAQE,cAAc,GAAG;AAAA,UAC/EC,YAAYN,iBAAe;AAAA,YACvBO,OAAOP,WAAWN,QAAQ,KAAKK,QAAQJ,aAAa;AAAA,YACpD,cAAcK,WAAWN,QAAQ,SAAS;AAAA;QAElD;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EAER;AAAA,EACDc,cAAc;AAAA,QAAAC,QAAA;AACV,WAAO;AAAA,MACHC,aAAa,WAAA;AAAA,YAAChB,QAAKiB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAI,eAAKG,UAA0B;AAAA,cAAzB;AAAA,YAAEC;AAAAA,YAAQC;AAAAA,UAAU,IAAAF;AAChD,mBAASJ,YAAYlB,MAAM;AACvB,gBAAG,CAACuB,OAAOE,SAASzB,IAAI,GAAG;AACvB,qBAAO;AAAA,YACX;AACA,kBAAM0B,QAAQH,OAAOI,cAAc3B,IAAI;AACvC,mBAAOwB,SAASI,iBAAiB5B,MAAM;AAAA,cACnCE,OAAOA,UAAAA,QAAAA,mBAAAA,QAAS,EAACwB,UAAAA,QAAAA,UAAAA,UAAAA,MAAOxB;AAAAA,YAC5B,CAAC;AAAA,UACL;AACA,iBAAOe,MAAKV,QAAQH,UACfyB,KAAK7B,UAAQkB,YAAYlB,IAAI,CAAC;AAAA;MACtC;AAAA;EAET;AACJ,CAAC;AC7CD,MAAM8B,KAAKC,WAAU;AAErB,SAASC,WAAWC,MAAMC,KAAK;AAC3BJ,KAAGK,OAAOC,MAAMC,UAAUL,WAAWM,KAAK;AAAA,IAAEC,KAAKN;AAAAA,IAAMO,QAAQP,KAAKb;AAAAA,EAAO,CAAC;AAC5E,QAAMqB,QAAQ,IAAKX,GAAGK,OAAOC,MAAOH,MAAM,MAAM,MAAM,CAAA,CAAE;AACxD,SAAOQ,MAAMT,WAAWE,KAAK,IAAI;AACrC;AAEO,SAASQ,WAAWT,MAAMU,OAAOC,OAAOC,QAAQ;AACnD,MAAIC,MAAMb,KAAKc,UAAU,GAAGH,KAAK,IAAIX,KAAKc,UAAUH,QAAQD,MAAMvB,MAAM;AACxE0B,QAAMA,IAAIC,UAAU,GAAGH,QAAQC,MAAM,IAAIF,QAAQG,IAAIC,UAAUH,QAAQC,MAAM;AAC7E,SAAOC;AACX;AAEA,SAASE,UAAUf,MAAMU,OAAOM,MAAMC,IAAI;AACtC,MAAIhB,MAAMe,MAAMH,MAAMb;AACtB,SAAMC,MAAMgB,IAAI;AACZ,QAAGlB,WAAWc,KAAKZ,GAAG,EAAEiB,UAAU;AAC9B;AAAA,IACJ;AACAL,UAAMJ,WAAWI,KAAKH,OAAOT,KAAK,CAAC;AACnCA;AAAAA,EACJ;AACA,SAAO;AAAA,IAAED,MAAMa;AAAAA,IAAKG,MAAMf;AAAAA,IAAKgB;AAAAA;AACnC;AAEA,SAASE,QAAQnB,MAAMU,OAAOM,MAAMC,IAAI;AACpC,MAAIhB,MAAMgB,IAAIJ,MAAMb;AACpB,SAAMC,MAAMe,MAAM;AACd,QAAGjB,WAAWc,KAAKZ,GAAG,EAAEmB,WAAW;AAC/B;AAAA,IACJ;AACAP,UAAMJ,WAAWI,KAAKH,OAAOT,KAAK,EAAE;AACpCA;AAAAA,EACJ;AACA,SAAO;AAAA,IAAED,MAAMa;AAAAA,IAAKG;AAAAA,IAAMC,IAAIhB;AAAAA;AAClC;AAEO,SAASoB,WAAWrB,MAAMU,OAAOM,MAAMC,IAAI;AAC9C,MAAIT,QAAQ;AAAA,IACRR;AAAAA,IACAgB;AAAAA,IACAC;AAAAA;AAGJT,UAAQO,UAAUP,MAAMR,MAAMU,OAAOF,MAAMQ,MAAMR,MAAMS,EAAE;AACzDT,UAAQW,QAAQX,MAAMR,MAAMU,OAAOF,MAAMQ,MAAMR,MAAMS,EAAE;AAEvD,MAAGT,MAAMS,KAAKT,MAAMQ,OAAON,MAAMvB,SAAS,GAAG;AACzCqB,UAAMR,OAAOQ,MAAMR,KAAKc,UAAU,GAAGN,MAAMQ,IAAI,IAAIR,MAAMR,KAAKc,UAAUN,MAAMS,KAAKP,MAAMvB,MAAM;AAAA,EACnG;AAEA,SAAOqB,MAAMR;AACjB;AC/CO,MAAMsB,gCAAgCC,0BAA4B;AAAA,EAIrEC,YAAYC,OAAOC,OAAOpD,SAAS;AAC/B,UAAMmD,OAAOC,OAAOpD,YAAAA,QAAAA,YAAAA,SAAAA,UAAW,CAAA,CAAE;AAHrCqD,mCAAU;AAIN,SAAKC,UAAU;EACnB;AAAA,EAEAC,OAAOC,MAAMC,QAAQC,OAAO;AACxB,UAAMH,OAAOC,MAAMC,QAAQC,KAAK;AAChC,UAAMC,MAAM,KAAKL,QAAQ,KAAKA,QAAQzC,SAAS,CAAC;AAChD,QAAG8C,QAAG,QAAHA,QAAG,UAAHA,IAAKtB,SAASsB,gBAAAA,QAAG,UAAHA,IAAKC,KAAK;AACvB,YAAM;AAAA,QAAEC;AAAAA,QAAWxB;AAAAA,QAAOuB;AAAAA,MAAI,IAAI,KAAKE,gBAAgBH,GAAG;AAC1D,WAAKI,MAAMhB,WAAW,KAAKgB,KAAKF,WAAWxB,OAAOuB,GAAG;AACrD,WAAKN,QAAQU;IACjB;AAAA,EACJ;AAAA,EAEAC,WAAWC,MAAMC,MAAMV,QAAQC,OAAO;AAClC,UAAMU,OAAO,KAAKhB,MAAMc,KAAKG,KAAK5E,IAAI;AACtC,QAAG2E,KAAKE,0BAA0B;AAC9B,UAAGH,MAAM;AACL,aAAKb,QAAQiB,KAAK;AAAA,UACdlC,OAAO,KAAK0B,IAAIlD;AAAAA,UAChBgD,WAAWO,KAAKD;AAAAA,QACpB,CAAC;AAAA,MACL,OAAO;AACH,cAAMR,MAAM,KAAKL,QAAQU,IAAG;AAC5B,aAAKV,QAAQiB,KAAK;AAAA,UACd,GAAGZ;AAAAA,UACHC,KAAK,KAAKG,IAAIlD;AAAAA,QAClB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,MAAMoD,WAAWC,MAAMC,MAAMV,QAAQC,KAAK;AAAA,EACrD;AAAA,EAEAI,gBAAgBlC,QAAQ;AACpB,QAAI;AAAA,MAAES;AAAAA,MAAOuB;AAAAA,IAAK,IAAGhC;AACrB,WAAM,KAAKmC,IAAIS,OAAOnC,KAAK,EAAEoC,MAAM,IAAI,GAAG;AACtCpC;AAAAA,IACJ;AACA,WAAO;AAAA,MACH,GAAGT;AAAAA,MACHS;AAAAA;EAER;AACJ;ACpDA,MAAeqC,WAAAA,KAAKlF,OAAO;AAAA,EACvBC,MAAM;AAAA;AAAA;AAAA;AAAA,EAINkF,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAW;AAAA,UACPV,KAAKjC,OAAOgC,MAAO;AAAA,gBAAAY,eAAAC;AACf,gBAAG,CAAC,KAAK/D,OAAOgE,QAAQJ,SAAS5E,QAAQiF,MAAM;AAC3CC,sBAAQC,KAAM,qBAAoBjB,KAAKG,KAAK5E,IAAK,uCAAsC;AACvF,qBAAO;AAAA,YACX;AACA,oBAAAqF,iBAAAC,eAAOK,YAAYlB,IAAI,OAACa,QAAAA,mCAAjBA,aAAoB,CAAC,OAAC,QAAAD,kBAAAA,SAAAA,gBAAI;AAAA,UACpC;AAAA,UACDO,MAAMnD,OAAOgC,MAAM;AAAA,gBAAAoB,gBAAAC;AACf,gBAAG,CAAC,KAAKvE,OAAOgE,QAAQJ,SAAS5E,QAAQiF,MAAM;AAC3C,qBAAO;AAAA,YACX;AACA,oBAAAK,kBAAAC,gBAAOH,YAAYlB,IAAI,OAACqB,QAAAA,oCAAjBA,cAAoB,CAAC,OAAC,QAAAD,mBAAAA,SAAAA,iBAAI;AAAA,UACrC;AAAA,QACH;AAAA,QACDE,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AAED,SAASJ,YAAYlB,MAAM;AACvB,QAAMuB,SAASvB,KAAKG,KAAKoB;AACzB,QAAMjC,OAAOiC,OAAO/D,KAAK,KAAK,CAACwC,IAAI,CAAC;AACpC,QAAMe,OAAOS,oBAAoBC,SAASjD,KAAKc,IAAI,GAAGiC,MAAM;AAC5D,QAAMhB,QAAQQ,KAAKR,MAAM,qBAAqB;AAC9C,SAAOA,QAAQ,CAACA,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,IAAI;AAC1C;ACvCO,SAASmB,kBAAkBC,OAAO;AAErC,QAAMC,eAAgB,SAAQD,KAAM;AAEpC,SAAO,IAAIE,OAAOC,UAAW,EAACC,gBAAgBH,cAAc,WAAW,EAAEI;AAC7E;AAEO,SAASC,WAAWN,OAAO;AAC9B,SAAOA,UAAAA,QAAAA,4BAAAA,MACDO,QAAQ,MAAM,MAAM,EACrBA,QAAQ,MAAM,MAAM;AAC7B;AAEO,SAASC,eAAe7C,MAAM;AACjC,QAAMC,SAASD,KAAK8C;AACpB,QAAMC,UAAU9C,OAAO+C;AAEvB,SAAM/C,OAAOgD,cAAchD,OAAOgD,eAAejD,MAAM;AACnD+C,YAAQG,YAAYjD,OAAOgD,UAAU;AAAA,EACzC;AAEA,MAAGF,QAAQI,WAAW9F,SAAS,GAAG;AAC9B4C,WAAO6C,cAAcM,aAAaL,SAAS9C,MAAM;AAAA,EACrD;AACAA,SAAO6C,cAAcM,aAAapD,MAAMC,MAAM;AAC9C,MAAGA,OAAOkD,WAAW9F,WAAW,GAAG;AAC/B4C,WAAOoD,OAAM;AAAA,EACjB;AACJ;AAEO,SAASC,cAActD,MAAM;AAChC,QAAMC,SAASD,KAAKuD;AAEpB,SAAOvD,KAAKiD;AAAYhD,WAAOmD,aAAapD,KAAKiD,YAAYjD,IAAI;AAEjEC,SAAOuD,YAAYxD,IAAI;AAC3B;ACjCA,MAAeyD,WAAAA,OAAKzH,OAAO;AAAA,EACvBC,MAAM;AAAA,EACNkF,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAMC,QAAQ;AAC3B,cAAG,KAAKzC,OAAOgE,QAAQJ,SAAS5E,QAAQiF,MAAM;AAC1C/C,kBAAMgF,MAAMC,cAAc3D,MAAMC,MAAM,CAAC;AAAA,UAC3C,OAAO;AACHyB,oBAAQC,KAAM,qBAAoB3B,KAAKa,KAAK5E,IAAK,uCAAsC;AACvFyC,kBAAMgF,MAAO,IAAG1D,KAAKa,KAAK5E,IAAK,GAAE;AAAA,UACrC;AACA,cAAG+D,KAAK4D,SAAS;AACblF,kBAAMmF,WAAW7D,IAAI;AAAA,UACzB;AAAA,QACH;AAAA,QACDgC,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AAED,SAAS2B,cAAc3D,MAAMC,QAAQ;AACjC,QAAMgC,SAASjC,KAAKa,KAAKoB;AACzB,QAAMR,OAAOS,oBAAoBC,SAASjD,KAAKc,IAAI,GAAGiC,MAAM;AAE5D,MAAGjC,KAAK4D,YAAY3D,kBAAkBkC,YAAYlC,OAAOY,KAAK5E,SAASgG,OAAO6B,YAAY7H,OAAO;AAC7F,WAAO8H,YAAYtC,IAAI;AAAA,EAC3B;AAEA,SAAOA;AACX;AAKA,SAASsC,YAAYtC,MAAM;AACvB,QAAMuC,MAAM5B,kBAAkBX,IAAI;AAClC,QAAM7E,UAAUoH,IAAIC;AAEpBrH,UAAQsH,YAAYtH,QAAQsH,UAAUC,KAAI,IACnC;AAAA,EAAIvH,QAAQsH,SAAU;AAAA,IACtB;AAAA;AAEP,SAAOtH,QAAQwH;AACnB;AChDA,MAAMC,aAAaZ,OAAKzH,OAAO;AAAA,EAC3BC,MAAM;AACV,CAAC;AAED,MAAeoI,eAAAA,WAAWC,OAAO;AAAA;AAAA;AAAA;AAAA,EAI7BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B5E,MAAM6E;AAAAA,QAC3CxC,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACnBD,MAAMyC,aAAahB,OAAKzH,OAAO;AAAA,EAC3BC,MAAM;AACV,CAAC;AAED,MAAewI,eAAAA,WAAWH,OAAO;AAAA;AAAA;AAAA;AAAA,EAI7BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAM;AACnB,iBAAOtB,MAAMgG,WAAW1E,MAAM,MAAM,OAAO,KAAKxC,OAAOgE,QAAQJ,SAAS5E,QAAQmI,oBAAoB,OAAO,GAAG;AAAA,QACjH;AAAA,QACD3C,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACpBD,MAAM4C,YAAYnB,OAAKzH,OAAO;AAAA,EAC1BC,MAAM;AACV,CAAC;AAED,MAAe2I,cAAAA,UAAUN,OAAO;AAAA;AAAA;AAAA;AAAA,EAI5BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAM;AACnBtB,gBAAMgF,MAAM,SAAS1D,KAAKrC,MAAMkH,YAAY,MAAM,IAAI;AACtDnG,gBAAMR,KAAK8B,KAAK8E,aAAa,KAAK;AAClCpG,gBAAMqG,cAAa;AACnBrG,gBAAMgF,MAAM,KAAK;AACjBhF,gBAAMmF,WAAW7D,IAAI;AAAA,QACxB;AAAA,QACDgC,OAAO;AAAA,UACHgD,MAAMhH,aAAY;AAAA,gBAAAiH;AACdjH,YAAAA,YAAWkH,IAAI;AAAA,cACXC,aAAUF,wBAAE,KAAKzI,QAAQ4I,yBAAmBH,QAAAA,0BAAAA,SAAAA,wBAAI;AAAA,YACpD,CAAC;AAAA,UACJ;AAAA,UACDI,UAAUzI,SAAS;AACfA,oBAAQsH,YAAYtH,QAAQsH,UAAUtB,QAAQ,sBAAsB,eAAe;AAAA,UACvF;AAAA,QACJ;AAAA,MACJ;AAAA;EAER;AACJ,CAAC;AC/BD,MAAM0C,YAAY7B,OAAKzH,OAAO;AAAA,EAC1BC,MAAM;AACV,CAAC;AAED,MAAeqJ,cAAAA,UAAUhB,OAAO;AAAA;AAAA;AAAA;AAAA,EAI5BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAMC,QAAQC,OAAO;AAClC,mBAASqF,IAAIrF,QAAQ,GAAGqF,IAAItF,OAAOuF,YAAYD;AAC3C,gBAAItF,OAAOwF,MAAMF,CAAC,EAAE1E,QAAQb,KAAKa,MAAM;AACnCnC,oBAAMgF,MACFhF,MAAMmB,UACA6F,SAASlE,QAAQJ,SAASC,UAAU9C,KAAK,MAAMG,OAAOsB,MAAMC,MAAM,IAClE,MACV;AACA;AAAA,YACJ;AAAA,QACP;AAAA,QACD+B,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AC3BD,MAAM2D,UAAUlC,OAAKzH,OAAO;AAAA,EACxBC,MAAM;AACV,CAAC;AAED,MAAe0J,YAAAA,QAAQrB,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B5E,MAAMiG;AAAAA,QAC3C5D,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AClBD,MAAM6D,iBAAiBpC,OAAKzH,OAAO;AAAA,EAC/BC,MAAM;AACV,CAAC;AAED,MAAe4J,mBAAAA,eAAevB,OAAO;AAAA;AAAA;AAAA;AAAA,EAIjCnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B5E,MAAMmG;AAAAA,QAC3C9D,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AClBD,MAAM+D,QAAQtC,OAAKzH,OAAO;AAAA,EACtBC,MAAM;AACV,CAAC;AAED,MAAe8J,UAAAA,MAAMzB,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B5E,MAAMqG;AAAAA,QAC3ChE,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AClBD,MAAMiE,WAAWxC,OAAKzH,OAAO;AAAA,EACzBC,MAAM;AACV,CAAC;AAED,MAAegK,aAAAA,SAAS3B,OAAO;AAAA;AAAA;AAAA;AAAA,EAI3BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B5E,MAAMuG;AAAAA,QAC3ClE,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACnBD,MAAMmE,cAAc1C,OAAKzH,OAAO;AAAA,EAC5BC,MAAM;AACV,CAAC;AAED,SAASmK,wBAAwBpG,MAAMC,QAAQC,OAAO;AAClD,MAAIqF,IAAI;AACR,SAAOrF,QAAQqF,IAAI,GAAGA,KAAK;AACvB,QAAItF,OAAOwF,MAAMvF,QAAQqF,IAAI,CAAC,EAAE1E,KAAK5E,SAAS+D,KAAKa,KAAK5E,MAAM;AAC1D;AAAA,IACJ;AAAA,EACJ;AACA,SAAOsJ;AACX;AAEA,MAAeY,gBAAAA,YAAY7B,OAAO;AAAA;AAAA;AAAA;AAAA,EAI9BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAMC,QAAQC,OAAO;AAClC,gBAAMrB,QAAQmB,KAAKrC,MAAMkB,SAAS;AAClC,gBAAMwH,OAAOC,OAAOzH,QAAQmB,KAAKwF,aAAa,CAAC,EAAEnI;AACjD,gBAAMkJ,QAAQ7H,MAAM8H,OAAO,KAAKH,OAAO,CAAC;AACxC,gBAAMI,gBAAgBL,wBAAwBpG,MAAMC,QAAQC,KAAK;AACjE,gBAAMwG,YAAYD,gBAAgB,IAAI,OAAO;AAC7C/H,gBAAMgG,WAAW1E,MAAMuG,OAAOhB,OAAK;AAC/B,kBAAMoB,OAAOL,OAAOzH,QAAQ0G,CAAC;AAC7B,mBAAO7G,MAAM8H,OAAO,KAAKH,OAAOM,KAAKtJ,MAAM,IAAIsJ,OAAOD;AAAAA,UAC1D,CAAC;AAAA,QACJ;AAAA,QACD1E,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACrCD,MAAM4E,YAAYnD,OAAKzH,OAAO;AAAA,EAC1BC,MAAM;AACV,CAAC;AAED,MAAe2K,cAAAA,UAAUtC,OAAO;AAAA;AAAA;AAAA;AAAA,EAI5BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B5E,MAAMkH;AAAAA,QAC3C7E,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACpBM,SAASmB,WAAWnD,MAAM;AAAA,MAAA8G,uBAAAC;AAC7B,UAAAD,wBAAO9G,SAAI,QAAJA,SAAI+G,WAAAA,gBAAJ/G,KAAMgH,qBAAOD,kBAAA,SAAA,SAAbA,cAAeC,qBAAOF,0BAAA,SAAAA,wBAAI;AACrC;ACAA,MAAMG,QAAQxD,OAAKzH,OAAO;AAAA,EACtBC,MAAM;AACV,CAAC;AAED,MAAegL,UAAAA,MAAM3C,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAMC,QAAQ;AAC3B,cAAG,CAACiH,uBAAuBlH,IAAI,GAAG;AAC9B0F,qBAASlE,QAAQJ,SAASC,UAAU9C,KAAK,MAAMG,OAAOsB,MAAMC,MAAM;AAClE;AAAA,UACJ;AACAvB,gBAAMmB,UAAU;AAChBG,eAAKmH,QAAQ,CAACC,KAAKC,GAAG9B,MAAM;AACxB7G,kBAAMgF,MAAM,IAAI;AAChB0D,gBAAID,QAAQ,CAACG,KAAKD,IAAGE,MAAM;AACvB,kBAAGA,GAAG;AACF7I,sBAAMgF,MAAM,KAAK;AAAA,cACrB;AACA,oBAAM8D,cAAcF,IAAIrE;AACxB,kBAAGuE,YAAY1C,YAAYX,QAAQ;AAC/BzF,sBAAM+I,aAAaD,WAAW;AAAA,cAClC;AAAA,YACJ,CAAC;AACD9I,kBAAMgF,MAAM,IAAI;AAChBhF,kBAAMqG,cAAa;AACnB,gBAAG,CAACQ,GAAG;AACH,oBAAMmC,eAAeC,MAAMzI,KAAK;AAAA,gBAAC7B,QAAQ+J,IAAI5B;AAAAA,eAAW,EAAEoC,IAAI,MAAM,KAAK,EAAEC,KAAK,KAAK;AACrFnJ,oBAAMgF,MAAO,KAAIgE,YAAa,IAAG;AACjChJ,oBAAMqG,cAAa;AAAA,YACvB;AAAA,UACJ,CAAC;AACDrG,gBAAMmF,WAAW7D,IAAI;AACrBtB,gBAAMmB,UAAU;AAAA,QACnB;AAAA,QACDmC,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AAGD,SAAS8F,QAAQ9H,MAAM;AACnB,SAAOA,KAAKrC,MAAMoK,UAAU,KAAK/H,KAAKrC,MAAMqK,UAAU;AAC1D;AAEA,SAASd,uBAAuBlH,MAAM;AAClC,QAAMiI,OAAO9E,WAAWnD,IAAI;AAC5B,QAAMkI,WAAWD,KAAK,CAAC;AACvB,QAAME,WAAWF,KAAKG,MAAM,CAAC;AAE7B,MAAGjF,WAAW+E,QAAQ,EAAEpK,KAAKuK,UAAQA,KAAKxH,KAAK5E,SAAS,iBAAiB6L,QAAQO,IAAI,KAAKA,KAAK7C,aAAa,CAAC,GAAG;AAC5G,WAAO;AAAA,EACX;AAEA,MAAG2C,SAASrK,KAAKsJ,SACbjE,WAAWiE,GAAG,EAAEtJ,KAAKuK,UAAQA,KAAKxH,KAAK5E,SAAS,iBAAiB6L,QAAQO,IAAI,KAAKA,KAAK7C,aAAa,CAAC,CACzG,GAAG;AACC,WAAO;AAAA,EACX;AAEA,SAAO;AACX;ACrEA,MAAM8C,WAAW7E,OAAKzH,OAAO;AAAA,EACzBC,MAAM;AACV,CAAC;AAED,MAAeqM,aAAAA,SAAShE,OAAO;AAAA;AAAA;AAAA;AAAA,EAI3BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAM;AACnB,gBAAMuI,QAAQvI,KAAKrC,MAAM6K,UAAU,QAAQ;AAC3C9J,gBAAMgF,MAAO,GAAE6E,KAAM,GAAE;AACvB7J,gBAAM+J,cAAczI,IAAI;AAAA,QAC3B;AAAA,QACDgC,OAAO;AAAA,UACHqD,UAAUzI,SAAS;AACf,aAAC,GAAGA,QAAQ8L,iBAAiB,iBAAiB,CAAC,EAC1CvB,QAAQwB,UAAQ;AACb,oBAAMC,QAAQD,KAAK7L,cAAc,OAAO;AACxC6L,mBAAKE,aAAa,aAAa,UAAU;AACzC,kBAAGD,OAAO;AACND,qBAAKE,aAAa,gBAAgBD,MAAMJ,OAAO;AAC/CI,sBAAMvF,OAAM;AAAA,cAChB;AAAA,YACJ,CAAC;AAAA,UACT;AAAA,QACJ;AAAA,MACJ;AAAA;EAER;AACJ,CAAC;AC9BD,MAAMyF,WAAWrF,OAAKzH,OAAO;AAAA,EACzBC,MAAM;AACV,CAAC;AAED,MAAe6M,aAAAA,SAASxE,OAAO;AAAA;AAAA;AAAA;AAAA,EAI3BnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWoD,aAAWjD,QAAQJ,SAASC;AAAAA,QACvCW,OAAO;AAAA,UACHgD,MAAMhH,aAAY;AACdA,YAAAA,YAAW+K,IAAIC,cAAc;AAAA,UAChC;AAAA,UACD3D,UAAUzI,SAAS;AACf,aAAC,GAAGA,QAAQ8L,iBAAiB,qBAAqB,CAAC,EAC9CvB,QAAQ8B,UAAQ;AACbA,mBAAKJ,aAAa,aAAa,UAAU;AAAA,YAC7C,CAAC;AAAA,UACT;AAAA,QACJ;AAAA,MACJ;AAAA;EAER;AACJ,CAAC;AC3BD,MAAMK,OAAOzF,OAAKzH,OAAO;AAAA,EACrBC,MAAM;AACV,CAAC;AAED,MAAeiN,SAAAA,KAAK5E,OAAO;AAAA;AAAA;AAAA;AAAA,EAIvBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,UAAU3C,OAAOsB,MAAM;AACnBtB,gBAAMR,KAAKyE,WAAW3C,KAAK9B,IAAI,CAAC;AAAA,QACnC;AAAA,QACD8D,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACpBD,MAAMmH,OAAOjI,KAAKlF,OAAO;AAAA,EACrBC,MAAM;AACV,CAAC;AAED,MAAekN,SAAAA,KAAK7E,OAAO;AAAA;AAAA;AAAA;AAAA,EAIvBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B3E,MAAMwJ;AAAAA,QAC3CpH,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AClBD,MAAMqH,OAAOnI,KAAKlF,OAAO;AAAA,EACrBC,MAAM;AACV,CAAC;AAED,MAAeoN,SAAAA,KAAK/E,OAAO;AAAA;AAAA;AAAA;AAAA,EAIvBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B3E,MAAM0J;AAAAA,QAC3CtH,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AClBD,MAAMuH,SAASrI,KAAKlF,OAAO;AAAA,EACvBC,MAAM;AACV,CAAC;AAED,MAAesN,WAAAA,OAAOjF,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B3E,MAAM4J;AAAAA,QAC3CxH,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;AClBD,MAAMyH,OAAOvI,KAAKlF,OAAO;AAAA,EACrBC,MAAM;AACV,CAAC;AAED,MAAewN,SAAAA,KAAKnF,OAAO;AAAA;AAAA;AAAA;AAAA,EAIvBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAWkD,0BAA0B3E,MAAM8J;AAAAA,QAC3C1H,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACnBD,MAAM2H,SAASzI,KAAKlF,OAAO;AAAA,EACvBC,MAAM;AACV,CAAC;AAED,MAAe0N,WAAAA,OAAOrF,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzBnD,aAAa;AACT,WAAO;AAAA,MACHC,UAAU;AAAA,QACNC,WAAW;AAAA,UAACV,MAAM;AAAA,UAAMkB,OAAO;AAAA,UAAMf,0BAA0B;AAAA,QAAK;AAAA,QACpEkB,OAAO;AAAA;AAAA,QACH;AAAA,MAER;AAAA;EAER;AACJ,CAAC;ACGD,MAAA,qBAAe,CACXqC,cACAI,cACAG,aACAU,aACAK,WACAE,kBACAH,UACAK,SACAE,YACAE,eACAS,aACAK,SACAqB,YACAQ,YACAI,QAEAC,QACAE,QACAO,UACAL,UACAE,QACAE,QAAM;AC3CH,SAASE,gBAAgBC,WAAW;AAAA,MAAAC,oBAAAC;AACvC,QAAMC,gBAAYF,qBAAGD,UAAUtI,aAAO,QAAAuI,uBAAA,SAAA,SAAjBA,mBAAmB3I;AACxC,QAAM8I,uBAAmBF,wBAAGG,mBAAmBC,KAAKC,OAAKA,EAAEpO,SAAS6N,UAAU7N,IAAI,OAAC,QAAA+N,0BAAA,SAAA,SAAvDA,sBAAyDxI,QAAQJ;AAE7F,MAAG6I,gBAAgBC,qBAAqB;AACpC,WAAO;AAAA,MACH,GAAGA;AAAAA,MACH,GAAGD;AAAAA;EAEX;AAEA,SAAO;AACX;ACRO,MAAMK,mBAAmB;AAAA,EAM5B5K,YAAYlC,QAAQ;AAFpBA;AAAAA;AAAAA;AAAAA,kCAAS;AAGL,SAAKA,SAASA;AAAAA,EAClB;AAAA,EAEA6D,UAAU2F,SAAS;AACf,UAAMtI,QAAQ,IAAIc,wBAAwB,KAAKG,OAAO,KAAKC,OAAO;AAAA,MAC9D2K,mBAAmBjF,YAAUrJ;AAAAA,IACjC,CAAC;AAEDyC,UAAM+J,cAAczB,OAAO;AAE3B,WAAOtI,MAAM6B;AAAAA,EACjB;AAAA,EAEA,IAAIZ,QAAQ;AAAA,QAAA6K;AACR,WAAO;AAAA,MACH,GAAGC,OAAOC,YACND,OAAOE,KAAK,KAAKnN,OAAOyE,OAAOtC,KAAK,EAC/BiI,IAAI3L,UAAQ,CAACA,MAAM,KAAK2O,cAAclF,QAAQ,CAAC,CAAC,CACzD;AAAA,MACA,GAAG+E,OAAOC,aAAWF,wBACjB,KAAKhN,OAAOqN,iBAAiBC,WACxBC,OAAOjB,eAAaA,UAAUjJ,SAAS,UAAU,KAAK+J,cAAcd,SAAS,CAAC,EAC9ElC,IAAIkC,eAAa,CAACA,UAAU7N,MAAM,KAAK2O,cAAcd,SAAS,CAAC,CAAC,OAAC,QAAAU,0BAAA,SAAAA,wBACnE,EACP;AAAA;EAER;AAAA,EAEA,IAAI5K,QAAQ;AAAA,QAAAoL;AACR,WAAO;AAAA,MACH,GAAGP,OAAOC,YACND,OAAOE,KAAK,KAAKnN,OAAOyE,OAAOrC,KAAK,EAC/BgI,IAAI3L,UAAQ,CAACA,MAAM,KAAKgP,cAAcrB,QAAQ,CAAC,CAAC,CACzD;AAAA,MACA,GAAGa,OAAOC,aAAWM,yBACjB,KAAKxN,OAAOqN,iBAAiBC,WACxBC,OAAOjB,eAAaA,UAAUjJ,SAAS,UAAU,KAAKoK,cAAcnB,SAAS,CAAC,EAC9ElC,IAAIkC,eAAa,CAACA,UAAU7N,MAAM,KAAKgP,cAAcnB,SAAS,CAAC,CAAC,OAAC,QAAAkB,2BAAA,SAAAA,yBACnE,EACP;AAAA;EAER;AAAA,EAEAJ,cAAc5K,MAAM;AAAA,QAAAkL;AAChB,YAAAA,mBAAOrB,gBAAgB7J,IAAI,OAAC,QAAAkL,qBAAAA,WAAAA,mBAArBA,iBAAuB7J,eAAS6J,QAAAA,uCAAhCA,iBAAkCC,KAAK;AAAA,MAAE3N,QAAQ,KAAKA;AAAAA,MAAQhB,SAASwD,KAAKxD;AAAAA,IAAQ,CAAC;AAAA,EAChG;AAAA,EAEAyO,cAAcvK,MAAM;AAAA,QAAA0K;AAChB,UAAM/J,aAAS+J,oBAAGvB,gBAAgBnJ,IAAI,OAAC0K,QAAAA,sBAArBA,SAAAA,SAAAA,kBAAuB/J;AACzC,WAAOA,YACD;AAAA,MACE,GAAGA;AAAAA,MACHV,MAAM,OAAOU,UAAUV,SAAS,aAAaU,UAAUV,KAAKwK,KAAK;AAAA,QAAE3N,QAAQ,KAAKA;AAAAA,QAAQhB,SAASkE,KAAKlE;AAAAA,MAAQ,CAAC,IAAI6E,UAAUV;AAAAA,MAC7HkB,OAAO,OAAOR,UAAUQ,UAAU,aAAaR,UAAUQ,MAAMsJ,KAAK;AAAA,QAAE3N,QAAQ,KAAKA;AAAAA,QAAQhB,SAASkE,KAAKlE;AAAAA,OAAS,IAAI6E,UAAUQ;AAAAA,IACnI,IACC;AAAA,EACV;AACJ;ACnEO,MAAMwJ,eAAe;AAAA,EAUxB3L,YAAYlC,QAAMD,MAA6B;AAN/CC;AAAAA;AAAAA;AAAAA,kCAAS;AAITO;AAAAA;AAAAA;AAAAA,8BAAK;AAE0C,QAA3B;AAAA,MAAE0D;AAAAA,MAAM6J;AAAAA,MAASC;AAAAA,IAAQ,IAAAhO;AACzC,SAAKC,SAASA;AACd,SAAKO,KAAK,KAAKyN,oBAAoBxN,WAAW;AAAA,MAC1CyD;AAAAA,MACA6J;AAAAA,MACAC;AAAAA,IACH,CAAA,CAAC;AAAA,EACN;AAAA,EAEAvJ,MAAMgF,SAA0B;AAAA,QAAjB;AAAA,MAAE5I;AAAAA,IAAQ,IAAAhB,UAAAC,SAAAD,KAAAA,UAAAE,CAAAA,MAAAA,SAAAF,UAAG,CAAA,IAAA;AACxB,QAAG,OAAO4J,YAAY,UAAU;AAC5B,WAAKxJ,OAAOqN,iBAAiBC,WAAW3D,QAAQ2C,eAAS;AAAA,YAAAoB;AAAA,gBAAAA,mBACrDrB,gBAAgBC,SAAS,OAACoB,QAAAA,qBAAAA,WAAAA,mBAA1BA,iBAA4BlJ,WAAKkJ,QAAAA,qBAAAA,WAAAA,mBAAjCA,iBAAmClG,WAAKkG,QAAAA,qBAAxCA,SAAAA,SAAAA,iBAA0C3M,KAAK;AAAA,UAAEf,QAAO,KAAKA;AAAAA,UAAQhB,SAAQsN,UAAUtN;AAAAA,QAAQ,GAAG,KAAKuB,EAAE;AAAA,MAAC,CAC9G;AAEA,YAAM0N,eAAe,KAAK1N,GAAGgC,OAAOiH,OAAO;AAC3C,YAAMpK,UAAUwF,kBAAkBqJ,YAAY;AAE9C,WAAKjO,OAAOqN,iBAAiBC,WAAW3D,QAAQ2C,eAAS;AAAA,YAAAsB;AAAA,gBAAAA,oBACrDvB,gBAAgBC,SAAS,OAACsB,QAAAA,sBAAAA,WAAAA,oBAA1BA,kBAA4BpJ,WAAKoJ,QAAAA,sBAAAA,WAAAA,oBAAjCA,kBAAmC/F,eAAS+F,QAAAA,sBAA5CA,SAAAA,SAAAA,kBAA8C7M,KAAK;AAAA,UAAEf,QAAO,KAAKA;AAAAA,UAAQhB,SAAQsN,UAAUtN;AAAAA,QAAS,GAAEI,OAAO;AAAA,MAAC,CAClH;AAEA,WAAK8O,aAAa9O,SAAS;AAAA,QAAEwB;AAAAA,QAAQ4I;AAAAA,MAAQ,CAAC;AAE9C,aAAOpK,QAAQsH;AAAAA,IACnB;AAEA,WAAO8C;AAAAA,EACX;AAAA,EAEA0E,aAAa1L,MAAI2L,OAAuB;AAAA,QAArB;AAAA,MAAEvN;AAAAA,MAAQ4I;AAAAA,IAAS,IAAA2E;AAClC,SAAKC,gBAAgB5L,IAAI;AAGzBA,SAAK0I,iBAAiB,GAAG,EAAEvB,QAAQ0E,QAAM;AAAA,UAAAC;AACrC,YAAGA,kBAAAD,GAAGE,iBAAWD,QAAAA,oBAAdA,SAAAA,SAAAA,gBAAgBE,cAAavI,KAAKwI,aAAa,CAACJ,GAAGK,QAAQ,KAAK,GAAG;AAClEL,WAAGE,YAAYjH,cAAc+G,GAAGE,YAAYjH,YAAYlC,QAAQ,OAAO,EAAE;AAAA,MAC7E;AAAA,IACJ,CAAC;AAED,QAAGxE,QAAQ;AACP,WAAKkC,gBAAgBN,MAAMgH,OAAO;AAAA,IACtC;AAEA,WAAOhH;AAAAA,EACX;AAAA,EAEA4L,gBAAgB5L,MAAM;AAClB,UAAMmM,SAAS1B,OAAO2B,OAAO,KAAK5O,OAAOyE,OAAOtC,KAAK,EAChDoL,OAAO/K,CAAAA,UAAQA,MAAK4D,OAAO;AAEhC,UAAMyI,WAAWF,OACZvE,IAAI0E,WAAK;AAAA,UAAAC;AAAA,cAAAA,uBAAID,MAAME,KAAKC,cAAQF,QAAAA,2CAAnBA,qBAAqB3E,IAAI4E,UAAQA,KAAKE,GAAG;AAAA,IAAC,CAAA,EACvDC,KAAI,EACJ5B,OAAO6B,OAAO,EACd/E,KAAK,GAAG;AAEb,QAAG,CAACwE,UAAU;AACV;AAAA,IACJ;AAEA,KAAC,GAAGrM,KAAK0I,iBAAiB2D,QAAQ,CAAC,EAAElF,QAAQ0E,QAAM;AAC/C,UAAGA,GAAG/I,cAAc+J,QAAQ,GAAG,GAAG;AAC9BhK,uBAAegJ,EAAE;AAAA,MACrB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEAvL,gBAAgBN,MAAMgH,SAAS;AAAA,QAAA8F;AAC3B,SAAAA,wBAAG9M,KAAKiE,uBAAiB6I,QAAAA,0BAAtBA,UAAAA,sBAAwBD,QAAQ,GAAG,GAAG;AAAA,UAAAE,iBAAAC,gBAAAC,kBAAAC;AACrC,YAAMC,iBAAiBnN,KAAKiE;AAC5B,YAAM;AAAA,QAAEmJ;AAAAA,MAAoB,IAAGD;AAC/B,YAAME,eAAWN,mBAAAC,iBAAGhG,QAAQ/F,MAAM,MAAM,eAAC+L,mBAAA,SAAA,SAArBA,eAAwB,CAAC,eAACD,oBAAA,SAAAA,kBAAI;AAClD,YAAMO,YAAY,CAACF,sBAAkBH,oBAAAC,kBAC/BlG,QAAQ/F,MAAM,MAAM,OAAC,QAAAiM,oBAAA,SAAA,SAArBA,gBAAwB,CAAC,OAACD,QAAAA,qBAAAA,SAAAA,mBAAI,KAC9B;AAEN,UAAGjG,QAAQ/F,MAAM,OAAO,GAAG;AACvBkM,uBAAejJ,YAAa,GAAEiJ,eAAejJ,SAAU,GAAEoJ,SAAU;AACnE;AAAA,MACJ;AAEAhK,oBAAc6J,cAAc;AAE5BnN,WAAKkE,YAAa,GAAEmJ,WAAY,GAAErN,KAAKkE,SAAU,GAAEoJ,SAAU;AAAA,IACjE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKA9B,oBAAoBzN,KAAI;AACpB,UAAMwP,iBAAkBC,cAAa,WAAa;AAC9C,YAAMC,WAAWD,SAAS,GAAApQ,SAAO;AACjC,UAAGqQ,aAAa,MAAM;AAClB,eAAOA;AAAAA,MACX;AACA,UAAGA,SAASA,SAASpQ,SAAS,CAAC,MAAM,MAAM;AACvC,eAAOoQ,SAASrF,MAAM,GAAG,EAAE;AAAA,MAC/B;AACA,aAAOqF;AAAAA;AAGX1P,IAAAA,IAAGyP,SAASE,MAAMC,YAAYJ,eAAexP,IAAGyP,SAASE,MAAMC,SAAS;AACxE5P,IAAAA,IAAGyP,SAASE,MAAME,YAAYL,eAAexP,IAAGyP,SAASE,MAAME,SAAS;AACxE7P,IAAAA,IAAGyP,SAASE,MAAMG,QAAQN,eAAexP,IAAGyP,SAASE,MAAMG,KAAK;AAChE9P,IAAAA,IAAGyP,SAASE,MAAMI,aAAaP,eAAexP,IAAGyP,SAASE,MAAMI,UAAU;AAC1E/P,IAAAA,IAAGyP,SAASO,cAAcR,eAAexP,IAAGyP,SAASO,YAAY5C,KAAKpN,IAAGyP,QAAQ,CAAC;AAElF,WAAOzP;AAAAA,EACX;AACJ;ACxHO,MAAMiQ,oBAAoBjS,UAAUC,OAAO;AAAA,EAC9CC,MAAM;AAAA,EACNC,aAAa;AACT,WAAO;AAAA,MACH+R,qBAAqB;AAAA,MACrBC,qBAAqB;AAAA;EAE5B;AAAA,EACDC,wBAAwB;AACpB,WAAO,CACH,IAAIC,OAAO;AAAA,MACPC,KAAK,IAAIC,UAAU,mBAAmB;AAAA,MACtCC,OAAO;AAAA,QACHC,qBAAqBA,CAACtQ,MAAMuQ,SAASC,cAAc;AAC/C,cAAGA,aAAa,CAAC,KAAKlS,QAAQyR,qBAAqB;AAC/C,mBAAO;AAAA,UACX;AACA,gBAAMU,SAAS,KAAKnR,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAM9D,MAAM;AAAA,YAAEE,QAAQ;AAAA,UAAK,CAAC;AAC/E,iBAAOoE,UAAUqM,WAAW,KAAKrR,OAAOyE,MAAM,EACzC6M,WAAW1M,kBAAkBuM,MAAM,GAAG;AAAA,YACnCI,oBAAoB;AAAA,YACpBN;AAAAA,UACJ,CAAC;AAAA,QACR;AAAA;AAAA;AAAA;AAAA,QAIDO,yBAA0B5G,WAAU;AAChC,cAAG,CAAC,KAAK5L,QAAQ0R,qBAAqB;AAClC,mBAAO;AAAA,UACX;AACA,iBAAO,KAAK1Q,OAAOgE,QAAQJ,SAAS6N,WAAW5N,UAAU+G,MAAMpB,OAAO;AAAA,QAC1E;AAAA,MACJ;AAAA,IACH,CAAA,CAAC;AAAA,EAEV;AACJ,CAAC;MCpCYkI,WAAWnT,UAAUC,OAAO;AAAA,EACrCC,MAAM;AAAA,EACNkT,UAAU;AAAA,EACVjT,aAAa;AACT,WAAO;AAAA,MACHuF,MAAM;AAAA,MACN2N,YAAY;AAAA,MACZC,gBAAgB;AAAA,MAChB1K,kBAAkB;AAAA,MAClB2G,SAAS;AAAA,MACTC,QAAQ;AAAA,MACR0C,qBAAqB;AAAA,MACrBC,qBAAqB;AAAA;EAE5B;AAAA,EACDjR,cAAc;AACV,UAAMQ,WAAWqN,WAAWwE,SAASC,OAAOtS,YAAW;AACvD,WAAO;AAAA,MACHuS,YAAYA,CAACxI,SAASyI,YAAYC,iBAAkBnB,WAAU;AAC1D,eAAO9Q,SAAS+R,WACZjB,MAAM/Q,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAMgF,OAAO,GAClDyI,YACAC,YACJ,EAAEnB,KAAK;AAAA,MACV;AAAA,MACDoB,iBAAiBA,CAACC,OAAO5I,SAASxK,YAAa+R,WAAU;AACrD,eAAO9Q,SAASkS,gBACZC,OACArB,MAAM/Q,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAMgF,SAAS;AAAA,UAAE5I,QAAQ;AAAA,QAAK,CAAC,GACpE5B,OACJ,EAAE+R,KAAK;AAAA,MACX;AAAA;EAEP;AAAA,EACDsB,iBAAiB;AACb,SAAKrS,OAAOgE,QAAQJ,WAAW;AAAA,MAC3B5E,SAAS;AAAA,QAAE,GAAG,KAAKA;AAAAA,MAAS;AAAA,MAC5BoS,QAAQ,IAAIvD,eAAe,KAAK7N,QAAQ,KAAKhB,OAAO;AAAA,MACpDyS,YAAY,IAAI3E,mBAAmB,KAAK9M,MAAM;AAAA,MAC9CsS,aAAaA,MAAM;AACf,eAAO,KAAKtS,OAAOgE,QAAQJ,SAAS6N,WAAW5N,UAAU,KAAK7D,OAAOkB,MAAMqR,GAAG;AAAA,MAClF;AAAA;AAEJ,SAAKvS,OAAOhB,QAAQwT,iBAAiB,KAAKxS,OAAOhB,QAAQwK;AACzD,SAAKxJ,OAAOhB,QAAQwK,UAAU,KAAKxJ,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAM,KAAKxE,OAAOhB,QAAQwK,OAAO;AAAA,EACtG;AAAA,EACDiJ,WAAW;AACP,SAAKzS,OAAOhB,QAAQwK,UAAU,KAAKxJ,OAAOhB,QAAQwT;AAClD,WAAO,KAAKxS,OAAOhB,QAAQwT;AAAAA,EAC9B;AAAA,EACD7O,aAAa;AACT,WAAO;AAAA;AAAA;EAGV;AAAA,EACD+O,gBAAgB;AACZ,WAAO,CACHpU,mBAAmBqU,UAAU;AAAA,MACzBhU,OAAO,KAAKK,QAAQ4S;AAAAA,MACpBhT,YAAY,KAAKI,QAAQ6S;AAAAA,IAC7B,CAAC,GACDrB,kBAAkBmC,UAAU;AAAA,MACxBlC,qBAAqB,KAAKzR,QAAQyR;AAAAA,MAClCC,qBAAqB,KAAK1R,QAAQ0R;AAAAA,IACrC,CAAA,CAAC;AAAA,EAEV;AACJ,CAAC;"}