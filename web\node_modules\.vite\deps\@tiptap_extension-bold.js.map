{"version": 3, "sources": ["../../@tiptap/extension-bold/src/bold.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType,\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType,\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['strong', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBold: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleBold: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetBold: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCO,IAAM,iBAAiB;AAKvB,IAAM,iBAAiB;AAKvB,IAAM,uBAAuB;AAK7B,IAAM,uBAAuB;AAMvB,IAAA,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,KAAK;QACL,UAAU,UAAS,KAAqB,MAAM,eAAe,YAAY;MAC1E;MACD;QACE,OAAO;QACP,WAAW,UAAQ,KAAK,KAAK,SAAS,KAAK;MAC5C;MACD;QACE,OAAO;QACP,UAAU,WAAS,4BAA4B,KAAK,KAAe,KAAK;MACzE;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,UAAU,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGnF,cAAW;AACT,WAAO;MACL,SAAS,MAAM,CAAC,EAAE,SAAQ,MAAM;AAC9B,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,YAAY,MAAM,CAAC,EAAE,SAAQ,MAAM;AACjC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAU;MAC9C,SAAS,MAAM,KAAK,OAAO,SAAS,WAAU;;;EAIlD,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;MACD,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;MACD,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;", "names": []}