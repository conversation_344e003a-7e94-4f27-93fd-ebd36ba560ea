{"version": 3, "file": "popper-base.js", "sources": ["../../src/dom-utils/getWindow.js", "../../src/dom-utils/instanceOf.js", "../../src/utils/math.js", "../../src/utils/userAgent.js", "../../src/dom-utils/isLayoutViewport.js", "../../src/dom-utils/getBoundingClientRect.js", "../../src/dom-utils/getWindowScroll.js", "../../src/dom-utils/getHTMLElementScroll.js", "../../src/dom-utils/getNodeScroll.js", "../../src/dom-utils/getNodeName.js", "../../src/dom-utils/getDocumentElement.js", "../../src/dom-utils/getWindowScrollBarX.js", "../../src/dom-utils/getComputedStyle.js", "../../src/dom-utils/isScrollParent.js", "../../src/dom-utils/getCompositeRect.js", "../../src/dom-utils/getLayoutRect.js", "../../src/dom-utils/getParentNode.js", "../../src/dom-utils/getScrollParent.js", "../../src/dom-utils/listScrollParents.js", "../../src/dom-utils/isTableElement.js", "../../src/dom-utils/getOffsetParent.js", "../../src/enums.js", "../../src/utils/orderModifiers.js", "../../src/utils/debounce.js", "../../src/utils/mergeByName.js", "../../src/dom-utils/getViewportRect.js", "../../src/dom-utils/getDocumentRect.js", "../../src/dom-utils/contains.js", "../../src/utils/rectToClientRect.js", "../../src/dom-utils/getClippingRect.js", "../../src/utils/getBasePlacement.js", "../../src/utils/getVariation.js", "../../src/utils/getMainAxisFromPlacement.js", "../../src/utils/computeOffsets.js", "../../src/utils/getFreshSideObject.js", "../../src/utils/mergePaddingObject.js", "../../src/utils/expandToHashMap.js", "../../src/utils/detectOverflow.js", "../../src/createPopper.js"], "sourcesContent": ["// @flow\nimport type { Window } from '../types';\ndeclare function getWindow(node: Node | Window): Window;\n\nexport default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    const ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}\n", "// @flow\nimport getWindow from './getWindow';\n\ndeclare function isElement(node: mixed): boolean %checks(node instanceof\n  Element);\nfunction isElement(node) {\n  const OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\ndeclare function isHTMLElement(node: mixed): boolean %checks(node instanceof\n  HTMLElement);\nfunction isHTMLElement(node) {\n  const OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\ndeclare function isShadowRoot(node: mixed): boolean %checks(node instanceof\n  ShadowRoot);\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };\n", "// @flow\nexport const max = Math.max;\nexport const min = Math.min;\nexport const round = Math.round;\n", "// @flow\ntype Navigator = Navigator & { userAgentData?: NavigatorUAData };\n\ninterface NavigatorUAData {\n  brands: Array<{ brand: string, version: string }>;\n  mobile: boolean;\n  platform: string;\n}\n\nexport default function getUAString(): string {\n  const uaData = (navigator: Navigator).userAgentData;\n\n  if (uaData?.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands\n      .map((item) => `${item.brand}/${item.version}`)\n      .join(' ');\n  }\n\n  return navigator.userAgent;\n}\n", "// @flow\nimport getUAString from '../utils/userAgent';\n\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}\n", "// @flow\nimport type { ClientRectObject, VirtualElement } from '../types';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport { round } from '../utils/math';\nimport getWindow from './getWindow';\nimport isLayoutViewport from './isLayoutViewport';\n\nexport default function getBoundingClientRect(\n  element: Element | VirtualElement,\n  includeScale: boolean = false,\n  isFixedStrategy: boolean = false\n): ClientRectObject {\n  const clientRect = element.getBoundingClientRect();\n  let scaleX = 1;\n  let scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX =\n      (element: HTMLElement).offsetWidth > 0\n        ? round(clientRect.width) / (element: HTMLElement).offsetWidth || 1\n        : 1;\n    scaleY =\n      (element: HTMLElement).offsetHeight > 0\n        ? round(clientRect.height) / (element: HTMLElement).offsetHeight || 1\n        : 1;\n  }\n\n  const { visualViewport } = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n\n  const x =\n    (clientRect.left +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) /\n    scaleX;\n  const y =\n    (clientRect.top +\n      (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) /\n    scaleY;\n  const width = clientRect.width / scaleX;\n  const height = clientRect.height / scaleY;\n\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y,\n  };\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport type { Window } from '../types';\n\nexport default function getWindowScroll(node: Node | Window) {\n  const win = getWindow(node);\n  const scrollLeft = win.pageXOffset;\n  const scrollTop = win.pageYOffset;\n\n  return {\n    scrollLeft,\n    scrollTop,\n  };\n}\n", "// @flow\n\nexport default function getHTMLElementScroll(element: HTMLElement) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop,\n  };\n}\n", "// @flow\nimport getWindowScroll from './getWindowScroll';\nimport getWindow from './getWindow';\nimport { isHTMLElement } from './instanceOf';\nimport getHTMLElementScroll from './getHTMLElementScroll';\nimport type { Window } from '../types';\n\nexport default function getNodeScroll(node: Node | Window) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}\n", "// @flow\nimport type { Window } from '../types';\n\nexport default function getNodeName(element: ?Node | Window): ?string {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}\n", "// @flow\nimport { isElement } from './instanceOf';\nimport type { Window } from '../types';\n\nexport default function getDocumentElement(\n  element: Element | Window\n): HTMLElement {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return (\n    (isElement(element)\n      ? element.ownerDocument\n      : // $FlowFixMe[prop-missing]\n        element.document) || window.document\n  ).documentElement;\n}\n", "// @flow\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScroll from './getWindowScroll';\n\nexport default function getWindowScrollBarX(element: Element): number {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return (\n    getBoundingClientRect(getDocumentElement(element)).left +\n    getWindowScroll(element).scrollLeft\n  );\n}\n", "// @flow\nimport getWindow from './getWindow';\n\nexport default function getComputedStyle(\n  element: Element\n): CSSStyleDeclaration {\n  return getWindow(element).getComputedStyle(element);\n}\n", "// @flow\nimport getComputedStyle from './getComputedStyle';\n\nexport default function isScrollParent(element: HTMLElement): boolean {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n", "// @flow\nimport type { Rect, VirtualElement, Window } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getNodeScroll from './getNodeScroll';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getDocumentElement from './getDocumentElement';\nimport isScrollParent from './isScrollParent';\nimport { round } from '../utils/math';\n\nfunction isElementScaled(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  const scaleX = round(rect.width) / element.offsetWidth || 1;\n  const scaleY = round(rect.height) / element.offsetHeight || 1;\n\n  return scaleX !== 1 || scaleY !== 1;\n}\n\n// Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nexport default function getCompositeRect(\n  elementOrVirtualElement: Element | VirtualElement,\n  offsetParent: Element | Window,\n  isFixed: boolean = false\n): Rect {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const offsetParentIsScaled =\n    isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(\n    elementOrVirtualElement,\n    offsetParentIsScaled,\n    isFixed\n  );\n\n  let scroll = { scrollLeft: 0, scrollTop: 0 };\n  let offsets = { x: 0, y: 0 };\n\n  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {\n    if (\n      getNodeName(offsetParent) !== 'body' ||\n      // https://github.com/popperjs/popper-core/issues/1078\n      isScrollParent(documentElement)\n    ) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getBoundingClientRect from './getBoundingClientRect';\n\n// Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nexport default function getLayoutRect(element: HTMLElement): Rect {\n  const clientRect = getBoundingClientRect(element);\n\n  // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width,\n    height,\n  };\n}\n", "// @flow\nimport getNodeName from './getNodeName';\nimport getDocumentElement from './getDocumentElement';\nimport { isShadowRoot } from './instanceOf';\n\nexport default function getParentNode(element: Node | ShadowRoot): Node {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || // DOM Element detected\n    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}\n", "// @flow\nimport getParentNode from './getParentNode';\nimport isScrollParent from './isScrollParent';\nimport getNodeName from './getNodeName';\nimport { isHTMLElement } from './instanceOf';\n\nexport default function getScrollParent(node: Node): HTMLElement {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}\n", "// @flow\nimport getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getWindow from './getWindow';\nimport type { Window, VisualViewport } from '../types';\nimport isScrollParent from './isScrollParent';\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\nexport default function listScrollParents(\n  element: Node,\n  list: Array<Element | Window> = []\n): Array<Element | Window | VisualViewport> {\n  const scrollParent = getScrollParent(element);\n  const isBody = scrollParent === element.ownerDocument?.body;\n  const win = getWindow(scrollParent);\n  const target = isBody\n    ? [win].concat(\n        win.visualViewport || [],\n        isScrollParent(scrollParent) ? scrollParent : []\n      )\n    : scrollParent;\n  const updatedList = list.concat(target);\n\n  return isBody\n    ? updatedList\n    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n      updatedList.concat(listScrollParents(getParentNode(target)));\n}\n", "// @flow\nimport getNodeName from './getNodeName';\n\nexport default function isTableElement(element: Element): boolean {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getNodeName from './getNodeName';\nimport getComputedStyle from './getComputedStyle';\nimport { isHTMLElement, isShadowRoot } from './instanceOf';\nimport isTableElement from './isTableElement';\nimport getParentNode from './getParentNode';\nimport getUAString from '../utils/userAgent';\n\nfunction getTrueOffsetParent(element: Element): ?Element {\n  if (\n    !isHTMLElement(element) ||\n    // https://github.com/popperjs/popper-core/issues/837\n    getComputedStyle(element).position === 'fixed'\n  ) {\n    return null;\n  }\n\n  return element.offsetParent;\n}\n\n// `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element: Element) {\n  const isFirefox = /firefox/i.test(getUAString());\n  const isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    const elementCss = getComputedStyle(element);\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  let currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (\n    isHTMLElement(currentNode) &&\n    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0\n  ) {\n    const css = getComputedStyle(currentNode);\n\n    // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    if (\n      css.transform !== 'none' ||\n      css.perspective !== 'none' ||\n      css.contain === 'paint' ||\n      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||\n      (isFirefox && css.willChange === 'filter') ||\n      (isFirefox && css.filter && css.filter !== 'none')\n    ) {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nexport default function getOffsetParent(element: Element) {\n  const window = getWindow(element);\n\n  let offsetParent = getTrueOffsetParent(element);\n\n  while (\n    offsetParent &&\n    isTableElement(offsetParent) &&\n    getComputedStyle(offsetParent).position === 'static'\n  ) {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (\n    offsetParent &&\n    (getNodeName(offsetParent) === 'html' ||\n      (getNodeName(offsetParent) === 'body' &&\n        getComputedStyle(offsetParent).position === 'static'))\n  ) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}\n", "// @flow\nexport const top: 'top' = 'top';\nexport const bottom: 'bottom' = 'bottom';\nexport const right: 'right' = 'right';\nexport const left: 'left' = 'left';\nexport const auto: 'auto' = 'auto';\nexport type BasePlacement =\n  | typeof top\n  | typeof bottom\n  | typeof right\n  | typeof left;\nexport const basePlacements: Array<BasePlacement> = [top, bottom, right, left];\n\nexport const start: 'start' = 'start';\nexport const end: 'end' = 'end';\nexport type Variation = typeof start | typeof end;\n\nexport const clippingParents: 'clippingParents' = 'clippingParents';\nexport const viewport: 'viewport' = 'viewport';\nexport type Boundary = Element | Array<Element> | typeof clippingParents;\nexport type RootBoundary = typeof viewport | 'document';\n\nexport const popper: 'popper' = 'popper';\nexport const reference: 'reference' = 'reference';\nexport type Context = typeof popper | typeof reference;\n\nexport type VariationPlacement =\n  | 'top-start'\n  | 'top-end'\n  | 'bottom-start'\n  | 'bottom-end'\n  | 'right-start'\n  | 'right-end'\n  | 'left-start'\n  | 'left-end';\nexport type AutoPlacement = 'auto' | 'auto-start' | 'auto-end';\nexport type ComputedPlacement = VariationPlacement | BasePlacement;\nexport type Placement = AutoPlacement | BasePlacement | VariationPlacement;\n\nexport const variationPlacements: Array<VariationPlacement> = basePlacements.reduce(\n  (acc: Array<VariationPlacement>, placement: BasePlacement) =>\n    acc.concat([(`${placement}-${start}`: any), (`${placement}-${end}`: any)]),\n  []\n);\nexport const placements: Array<Placement> = [...basePlacements, auto].reduce(\n  (\n    acc: Array<Placement>,\n    placement: BasePlacement | typeof auto\n  ): Array<Placement> =>\n    acc.concat([\n      placement,\n      (`${placement}-${start}`: any),\n      (`${placement}-${end}`: any),\n    ]),\n  []\n);\n\n// modifiers that need to read the DOM\nexport const beforeRead: 'beforeRead' = 'beforeRead';\nexport const read: 'read' = 'read';\nexport const afterRead: 'afterRead' = 'afterRead';\n// pure-logic modifiers\nexport const beforeMain: 'beforeMain' = 'beforeMain';\nexport const main: 'main' = 'main';\nexport const afterMain: 'afterMain' = 'afterMain';\n// modifier with the purpose to write to the DOM (or write into a framework state)\nexport const beforeWrite: 'beforeWrite' = 'beforeWrite';\nexport const write: 'write' = 'write';\nexport const afterWrite: 'afterWrite' = 'afterWrite';\nexport const modifierPhases: Array<ModifierPhases> = [\n  beforeRead,\n  read,\n  afterRead,\n  beforeMain,\n  main,\n  afterMain,\n  beforeWrite,\n  write,\n  afterWrite,\n];\n\nexport type ModifierPhases =\n  | typeof beforeRead\n  | typeof read\n  | typeof afterRead\n  | typeof beforeMain\n  | typeof main\n  | typeof afterMain\n  | typeof beforeWrite\n  | typeof write\n  | typeof afterWrite;\n", "// @flow\nimport type { Modifier } from '../types';\nimport { modifierPhases } from '../enums';\n\n// source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n  const map = new Map();\n  const visited = new Set();\n  const result = [];\n\n  modifiers.forEach(modifier => {\n    map.set(modifier.name, modifier);\n  });\n\n  // On visiting object, check for its dependencies and visit them recursively\n  function sort(modifier: Modifier<any, any>) {\n    visited.add(modifier.name);\n\n    const requires = [\n      ...(modifier.requires || []),\n      ...(modifier.requiresIfExists || []),\n    ];\n\n    requires.forEach(dep => {\n      if (!visited.has(dep)) {\n        const depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n\n    result.push(modifier);\n  }\n\n  modifiers.forEach(modifier => {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n\n  return result;\n}\n\nexport default function orderModifiers(\n  modifiers: Array<Modifier<any, any>>\n): Array<Modifier<any, any>> {\n  // order based on dependencies\n  const orderedModifiers = order(modifiers);\n\n  // order based on phase\n  return modifierPhases.reduce((acc, phase) => {\n    return acc.concat(\n      orderedModifiers.filter(modifier => modifier.phase === phase)\n    );\n  }, []);\n}\n", "// @flow\n\nexport default function debounce<T>(fn: Function): () => Promise<T> {\n  let pending;\n  return () => {\n    if (!pending) {\n      pending = new Promise<T>(resolve => {\n        Promise.resolve().then(() => {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}\n", "// @flow\nimport type { Modifier } from '../types';\n\nexport default function mergeByName(\n  modifiers: Array<$Shape<Modifier<any, any>>>\n): Array<$Shape<Modifier<any, any>>> {\n  const merged = modifiers.reduce((merged, current) => {\n    const existing = merged[current.name];\n    merged[current.name] = existing\n      ? {\n          ...existing,\n          ...current,\n          options: { ...existing.options, ...current.options },\n          data: { ...existing.data, ...current.data },\n        }\n      : current;\n    return merged;\n  }, {});\n\n  // IE11 does not support Object.values\n  return Object.keys(merged).map(key => merged[key]);\n}\n", "// @flow\nimport getWindow from './getWindow';\nimport getDocumentElement from './getDocumentElement';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport isLayoutViewport from './isLayoutViewport';\nimport type { PositioningStrategy } from '../types';\n\nexport default function getViewportRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n\n    const layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || (!layoutViewport && strategy === 'fixed')) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width,\n    height,\n    x: x + getWindowScrollBarX(element),\n    y,\n  };\n}\n", "// @flow\nimport type { Rect } from '../types';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport getWindowScrollBarX from './getWindowScrollBarX';\nimport getWindowScroll from './getWindowScroll';\nimport { max } from '../utils/math';\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nexport default function getDocumentRect(element: HTMLElement): Rect {\n  const html = getDocumentElement(element);\n  const winScroll = getWindowScroll(element);\n  const body = element.ownerDocument?.body;\n\n  const width = max(\n    html.scrollWidth,\n    html.clientWidth,\n    body ? body.scrollWidth : 0,\n    body ? body.clientWidth : 0\n  );\n  const height = max(\n    html.scrollHeight,\n    html.clientHeight,\n    body ? body.scrollHeight : 0,\n    body ? body.clientHeight : 0\n  );\n\n  let x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return { width, height, x, y };\n}\n", "// @flow\nimport { isShadowRoot } from './instanceOf';\n\nexport default function contains(parent: Element, child: Element) {\n  const rootNode = child.getRootNode && child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n  // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    do {\n      if (next && parent.isSameNode(next)) {\n        return true;\n      }\n      // $FlowFixMe[prop-missing]: need a better way to handle this...\n      next = next.parentNode || next.host;\n    } while (next);\n  }\n\n  // Give up, the result is false\n  return false;\n}\n", "// @flow\nimport type { Rect, ClientRectObject } from '../types';\n\nexport default function rectToClientRect(rect: Rect): ClientRectObject {\n  return {\n    ...rect,\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height,\n  };\n}\n", "// @flow\nimport type { ClientRectObject, PositioningStrategy } from '../types';\nimport type { Boundary, RootBoundary } from '../enums';\nimport { viewport } from '../enums';\nimport getViewportRect from './getViewportRect';\nimport getDocumentRect from './getDocumentRect';\nimport listScrollParents from './listScrollParents';\nimport getOffsetParent from './getOffsetParent';\nimport getDocumentElement from './getDocumentElement';\nimport getComputedStyle from './getComputedStyle';\nimport { isElement, isHTMLElement } from './instanceOf';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getParentNode from './getParentNode';\nimport contains from './contains';\nimport getNodeName from './getNodeName';\nimport rectToClientRect from '../utils/rectToClientRect';\nimport { max, min } from '../utils/math';\n\nfunction getInnerBoundingClientRect(\n  element: Element,\n  strategy: PositioningStrategy\n) {\n  const rect = getBoundingClientRect(element, false, strategy === 'fixed');\n\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n\n  return rect;\n}\n\nfunction getClientRectFromMixedType(\n  element: Element,\n  clippingParent: Element | RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  return clippingParent === viewport\n    ? rectToClientRect(getViewportRect(element, strategy))\n    : isElement(clippingParent)\n    ? getInnerBoundingClientRect(clippingParent, strategy)\n    : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n}\n\n// A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element: Element): Array<Element> {\n  const clippingParents = listScrollParents(getParentNode(element));\n  const canEscapeClipping =\n    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  const clipperElement =\n    canEscapeClipping && isHTMLElement(element)\n      ? getOffsetParent(element)\n      : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  }\n\n  // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n  return clippingParents.filter(\n    (clippingParent) =>\n      isElement(clippingParent) &&\n      contains(clippingParent, clipperElement) &&\n      getNodeName(clippingParent) !== 'body'\n  );\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nexport default function getClippingRect(\n  element: Element,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  strategy: PositioningStrategy\n): ClientRectObject {\n  const mainClippingParents =\n    boundary === 'clippingParents'\n      ? getClippingParents(element)\n      : [].concat(boundary);\n  const clippingParents = [...mainClippingParents, rootBoundary];\n  const firstClippingParent = clippingParents[0];\n\n  const clippingRect = clippingParents.reduce((accRect, clippingParent) => {\n    const rect = getClientRectFromMixedType(element, clippingParent, strategy);\n\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n\n  return clippingRect;\n}\n", "// @flow\nimport { type BasePlacement, type Placement, auto } from '../enums';\n\nexport default function getBasePlacement(\n  placement: Placement | typeof auto\n): BasePlacement {\n  return (placement.split('-')[0]: any);\n}\n", "// @flow\nimport { type Variation, type Placement } from '../enums';\n\nexport default function getVariation(placement: Placement): ?Variation {\n  return (placement.split('-')[1]: any);\n}\n", "// @flow\nimport type { Placement } from '../enums';\n\nexport default function getMainAxisFromPlacement(\n  placement: Placement\n): 'x' | 'y' {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}\n", "// @flow\nimport getBasePlacement from './getBasePlacement';\nimport getVariation from './getVariation';\nimport getMainAxisFromPlacement from './getMainAxisFromPlacement';\nimport type {\n  Rect,\n  PositioningStrategy,\n  Offsets,\n  ClientRectObject,\n} from '../types';\nimport { top, right, bottom, left, start, end, type Placement } from '../enums';\n\nexport default function computeOffsets({\n  reference,\n  element,\n  placement,\n}: {\n  reference: Rect | ClientRectObject,\n  element: Rect | ClientRectObject,\n  strategy: PositioningStrategy,\n  placement?: Placement,\n}): Offsets {\n  const basePlacement = placement ? getBasePlacement(placement) : null;\n  const variation = placement ? getVariation(placement) : null;\n  const commonX = reference.x + reference.width / 2 - element.width / 2;\n  const commonY = reference.y + reference.height / 2 - element.height / 2;\n\n  let offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height,\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height,\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY,\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY,\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y,\n      };\n  }\n\n  const mainAxis = basePlacement\n    ? getMainAxisFromPlacement(basePlacement)\n    : null;\n\n  if (mainAxis != null) {\n    const len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] =\n          offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] =\n          offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n\n  return offsets;\n}\n", "// @flow\nimport type { SideObject } from '../types';\n\nexport default function getFreshSideObject(): SideObject {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n  };\n}\n", "// @flow\nimport type { SideObject } from '../types';\nimport getFreshSideObject from './getFreshSideObject';\n\nexport default function mergePaddingObject(\n  paddingObject: $Shape<SideObject>\n): SideObject {\n  return {\n    ...getFreshSideObject(),\n    ...paddingObject,\n  };\n}\n", "// @flow\n\nexport default function expandToHashMap<\n  T: number | string | boolean,\n  K: string\n>(value: T, keys: Array<K>): { [key: string]: T } {\n  return keys.reduce((hashMap, key) => {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}\n", "// @flow\nimport type { State, SideObject, Padding, PositioningStrategy } from '../types';\nimport type { Placement, Boundary, RootBoundary, Context } from '../enums';\nimport getClippingRect from '../dom-utils/getClippingRect';\nimport getDocumentElement from '../dom-utils/getDocumentElement';\nimport getBoundingClientRect from '../dom-utils/getBoundingClientRect';\nimport computeOffsets from './computeOffsets';\nimport rectToClientRect from './rectToClientRect';\nimport {\n  clippingParents,\n  reference,\n  popper,\n  bottom,\n  top,\n  right,\n  basePlacements,\n  viewport,\n} from '../enums';\nimport { isElement } from '../dom-utils/instanceOf';\nimport mergePaddingObject from './mergePaddingObject';\nimport expandToHashMap from './expandToHashMap';\n\n// eslint-disable-next-line import/no-unused-modules\nexport type Options = {\n  placement: Placement,\n  strategy: PositioningStrategy,\n  boundary: Boundary,\n  rootBoundary: RootBoundary,\n  elementContext: Context,\n  altBoundary: boolean,\n  padding: Padding,\n};\n\nexport default function detectOverflow(\n  state: State,\n  options: $Shape<Options> = {}\n): SideObject {\n  const {\n    placement = state.placement,\n    strategy = state.strategy,\n    boundary = clippingParents,\n    rootBoundary = viewport,\n    elementContext = popper,\n    altBoundary = false,\n    padding = 0,\n  } = options;\n\n  const paddingObject = mergePaddingObject(\n    typeof padding !== 'number'\n      ? padding\n      : expandToHashMap(padding, basePlacements)\n  );\n\n  const altContext = elementContext === popper ? reference : popper;\n\n  const popperRect = state.rects.popper;\n  const element = state.elements[altBoundary ? altContext : elementContext];\n\n  const clippingClientRect = getClippingRect(\n    isElement(element)\n      ? element\n      : element.contextElement || getDocumentElement(state.elements.popper),\n    boundary,\n    rootBoundary,\n    strategy\n  );\n\n  const referenceClientRect = getBoundingClientRect(state.elements.reference);\n\n  const popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement,\n  });\n\n  const popperClientRect = rectToClientRect({\n    ...popperRect,\n    ...popperOffsets,\n  });\n\n  const elementClientRect =\n    elementContext === popper ? popperClientRect : referenceClientRect;\n\n  // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n  const overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom:\n      elementClientRect.bottom -\n      clippingClientRect.bottom +\n      paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right:\n      elementClientRect.right - clippingClientRect.right + paddingObject.right,\n  };\n\n  const offsetData = state.modifiersData.offset;\n\n  // Offsets can be applied only to the popper element\n  if (elementContext === popper && offsetData) {\n    const offset = offsetData[placement];\n\n    Object.keys(overflowOffsets).forEach((key) => {\n      const multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      const axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}\n", "// @flow\nimport type {\n  State,\n  OptionsGeneric,\n  Modifier,\n  Instance,\n  VirtualElement,\n} from './types';\nimport getCompositeRect from './dom-utils/getCompositeRect';\nimport getLayoutRect from './dom-utils/getLayoutRect';\nimport listScrollParents from './dom-utils/listScrollParents';\nimport getOffsetParent from './dom-utils/getOffsetParent';\nimport orderModifiers from './utils/orderModifiers';\nimport debounce from './utils/debounce';\nimport mergeByName from './utils/mergeByName';\nimport detectOverflow from './utils/detectOverflow';\nimport { isElement } from './dom-utils/instanceOf';\n\nconst DEFAULT_OPTIONS: OptionsGeneric<any> = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute',\n};\n\ntype PopperGeneratorArgs = {\n  defaultModifiers?: Array<Modifier<any, any>>,\n  defaultOptions?: $Shape<OptionsGeneric<any>>,\n};\n\nfunction areValidElements(...args: Array<any>): boolean {\n  return !args.some(\n    (element) =>\n      !(element && typeof element.getBoundingClientRect === 'function')\n  );\n}\n\nexport function popperGenerator(generatorOptions: PopperGeneratorArgs = {}) {\n  const { defaultModifiers = [], defaultOptions = DEFAULT_OPTIONS } =\n    generatorOptions;\n\n  return function createPopper<TModifier: $Shape<Modifier<any, any>>>(\n    reference: Element | VirtualElement,\n    popper: HTMLElement,\n    options: $Shape<OptionsGeneric<TModifier>> = defaultOptions\n  ): Instance {\n    let state: $Shape<State> = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: { ...DEFAULT_OPTIONS, ...defaultOptions },\n      modifiersData: {},\n      elements: {\n        reference,\n        popper,\n      },\n      attributes: {},\n      styles: {},\n    };\n\n    let effectCleanupFns: Array<() => void> = [];\n    let isDestroyed = false;\n\n    const instance = {\n      state,\n      setOptions(setOptionsAction) {\n        const options =\n          typeof setOptionsAction === 'function'\n            ? setOptionsAction(state.options)\n            : setOptionsAction;\n\n        cleanupModifierEffects();\n\n        state.options = {\n          // $FlowFixMe[exponential-spread]\n          ...defaultOptions,\n          ...state.options,\n          ...options,\n        };\n\n        state.scrollParents = {\n          reference: isElement(reference)\n            ? listScrollParents(reference)\n            : reference.contextElement\n            ? listScrollParents(reference.contextElement)\n            : [],\n          popper: listScrollParents(popper),\n        };\n\n        // Orders the modifiers based on their dependencies and `phase`\n        // properties\n        const orderedModifiers = orderModifiers(\n          mergeByName([...defaultModifiers, ...state.options.modifiers])\n        );\n\n        // Strip out disabled modifiers\n        state.orderedModifiers = orderedModifiers.filter((m) => m.enabled);\n\n        runModifierEffects();\n\n        return instance.update();\n      },\n\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        const { reference, popper } = state.elements;\n\n        // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n        if (!areValidElements(reference, popper)) {\n          return;\n        }\n\n        // Store the reference and popper rects to be read by modifiers\n        state.rects = {\n          reference: getCompositeRect(\n            reference,\n            getOffsetParent(popper),\n            state.options.strategy === 'fixed'\n          ),\n          popper: getLayoutRect(popper),\n        };\n\n        // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n        state.reset = false;\n\n        state.placement = state.options.placement;\n\n        // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n        state.orderedModifiers.forEach(\n          (modifier) =>\n            (state.modifiersData[modifier.name] = {\n              ...modifier.data,\n            })\n        );\n\n        for (let index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          const { fn, options = {}, name } = state.orderedModifiers[index];\n\n          if (typeof fn === 'function') {\n            state = fn({ state, options, name, instance }) || state;\n          }\n        }\n      },\n\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce<$Shape<State>>(\n        () =>\n          new Promise<$Shape<State>>((resolve) => {\n            instance.forceUpdate();\n            resolve(state);\n          })\n      ),\n\n      destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      },\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then((state) => {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    });\n\n    // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(({ name, options = {}, effect }) => {\n        if (typeof effect === 'function') {\n          const cleanupFn = effect({ state, name, instance, options });\n          const noopFn = () => {};\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach((fn) => fn());\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\n\nexport const createPopper = popperGenerator();\n\n// eslint-disable-next-line import/no-unused-modules\nexport { detectOverflow };\n"], "names": ["getWindow", "node", "window", "toString", "ownerDocument", "defaultView", "isElement", "OwnElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom", "getWindowScroll", "win", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getHTMLElementScroll", "getNodeScroll", "getNodeName", "nodeName", "toLowerCase", "getDocumentElement", "document", "documentElement", "getWindowScrollBarX", "getComputedStyle", "isScrollParent", "overflow", "overflowX", "overflowY", "isElementScaled", "rect", "getCompositeRect", "elementOrVirtualElement", "offsetParent", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "scroll", "offsets", "clientLeft", "clientTop", "getLayoutRect", "abs", "getParentNode", "assignedSlot", "parentNode", "host", "getScrollParent", "indexOf", "body", "listScrollParents", "list", "scrollParent", "isBody", "target", "concat", "updatedList", "isTableElement", "getTrueOffsetParent", "position", "getContainingBlock", "isFirefox", "isIE", "elementCss", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getOffsetParent", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "order", "modifiers", "Map", "visited", "Set", "result", "for<PERSON>ach", "modifier", "set", "name", "sort", "add", "requires", "requiresIfExists", "dep", "has", "depModifier", "get", "push", "orderModifiers", "orderedModifiers", "reduce", "acc", "phase", "debounce", "fn", "pending", "Promise", "resolve", "then", "undefined", "mergeByName", "merged", "current", "existing", "options", "data", "Object", "keys", "key", "getViewportRect", "strategy", "html", "clientWidth", "clientHeight", "layoutViewport", "getDocumentRect", "winScroll", "scrollWidth", "scrollHeight", "direction", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "rectToClientRect", "getInnerBoundingClientRect", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "canEscapeClipping", "clipperElement", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "getBasePlacement", "placement", "split", "getVariation", "getMainAxisFromPlacement", "computeOffsets", "basePlacement", "variation", "commonX", "commonY", "mainAxis", "len", "getFreshSideObject", "mergePaddingObject", "paddingObject", "expandToHashMap", "value", "hashMap", "detectOverflow", "state", "elementContext", "altBoundary", "padding", "altContext", "popperRect", "rects", "elements", "clippingClientRect", "contextElement", "referenceClientRect", "popperOffsets", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "modifiersData", "offset", "multiply", "axis", "DEFAULT_OPTIONS", "areValidElements", "args", "some", "popperGenerator", "generatorOptions", "defaultModifiers", "defaultOptions", "createPopper", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "m", "enabled", "runModifierEffects", "update", "forceUpdate", "reset", "index", "length", "destroy", "onFirstUpdate", "effect", "cleanupFn", "noopFn"], "mappings": ";;;;;;;;;;EAIe,SAASA,SAAT,CAAmBC,IAAnB,EAAyB;EACtC,MAAIA,IAAI,IAAI,IAAZ,EAAkB;EAChB,WAAOC,MAAP;EACD;;EAED,MAAID,IAAI,CAACE,QAAL,OAAoB,iBAAxB,EAA2C;EACzC,QAAMC,aAAa,GAAGH,IAAI,CAACG,aAA3B;EACA,WAAOA,aAAa,GAAGA,aAAa,CAACC,WAAd,IAA6BH,MAAhC,GAAyCA,MAA7D;EACD;;EAED,SAAOD,IAAP;EACD;;ECVD,SAASK,SAAT,CAAmBL,IAAnB,EAAyB;EACvB,MAAMM,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBO,OAAnC;EACA,SAAOP,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYO,OAArD;EACD;;EAID,SAASC,aAAT,CAAuBR,IAAvB,EAA6B;EAC3B,MAAMM,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBS,WAAnC;EACA,SAAOT,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYS,WAArD;EACD;;EAID,SAASC,YAAT,CAAsBV,IAAtB,EAA4B;EAC1B;EACA,MAAI,OAAOW,UAAP,KAAsB,WAA1B,EAAuC;EACrC,WAAO,KAAP;EACD;;EACD,MAAML,UAAU,GAAGP,SAAS,CAACC,IAAD,CAAT,CAAgBW,UAAnC;EACA,SAAOX,IAAI,YAAYM,UAAhB,IAA8BN,IAAI,YAAYW,UAArD;EACD;;ECzBM,IAAMC,GAAG,GAAGC,IAAI,CAACD,GAAjB;EACA,IAAME,GAAG,GAAGD,IAAI,CAACC,GAAjB;EACA,IAAMC,KAAK,GAAGF,IAAI,CAACE,KAAnB;;ECMQ,SAASC,WAAT,GAA+B;EAC5C,MAAMC,MAAM,GAAIC,SAAD,CAAuBC,aAAtC;;EAEA,MAAIF,MAAM,QAAN,IAAAA,MAAM,CAAEG,MAAR,IAAkBC,KAAK,CAACC,OAAN,CAAcL,MAAM,CAACG,MAArB,CAAtB,EAAoD;EAClD,WAAOH,MAAM,CAACG,MAAP,CACJG,GADI,CACA,UAACC,IAAD;EAAA,aAAaA,IAAI,CAACC,KAAlB,SAA2BD,IAAI,CAACE,OAAhC;EAAA,KADA,EAEJC,IAFI,CAEC,GAFD,CAAP;EAGD;;EAED,SAAOT,SAAS,CAACU,SAAjB;EACD;;EChBc,SAASC,gBAAT,GAA4B;EACzC,SAAO,CAAC,iCAAiCC,IAAjC,CAAsCd,WAAW,EAAjD,CAAR;EACD;;ECEc,SAASe,qBAAT,CACbC,OADa,EAEbC,YAFa,EAGbC,eAHa,EAIK;EAAA,MAFlBD,YAEkB;EAFlBA,IAAAA,YAEkB,GAFM,KAEN;EAAA;;EAAA,MADlBC,eACkB;EADlBA,IAAAA,eACkB,GADS,KACT;EAAA;;EAClB,MAAMC,UAAU,GAAGH,OAAO,CAACD,qBAAR,EAAnB;EACA,MAAIK,MAAM,GAAG,CAAb;EACA,MAAIC,MAAM,GAAG,CAAb;;EAEA,MAAIJ,YAAY,IAAIzB,aAAa,CAACwB,OAAD,CAAjC,EAA4C;EAC1CI,IAAAA,MAAM,GACHJ,OAAD,CAAuBM,WAAvB,GAAqC,CAArC,GACIvB,KAAK,CAACoB,UAAU,CAACI,KAAZ,CAAL,GAA2BP,OAAD,CAAuBM,WAAjD,IAAgE,CADpE,GAEI,CAHN;EAIAD,IAAAA,MAAM,GACHL,OAAD,CAAuBQ,YAAvB,GAAsC,CAAtC,GACIzB,KAAK,CAACoB,UAAU,CAACM,MAAZ,CAAL,GAA4BT,OAAD,CAAuBQ,YAAlD,IAAkE,CADtE,GAEI,CAHN;EAID;;EAdiB,aAgBSnC,SAAS,CAAC2B,OAAD,CAAT,GAAqBjC,SAAS,CAACiC,OAAD,CAA9B,GAA0C/B,MAhBnD;EAAA,MAgBVyC,cAhBU,QAgBVA,cAhBU;;EAiBlB,MAAMC,gBAAgB,GAAG,CAACd,gBAAgB,EAAjB,IAAuBK,eAAhD;EAEA,MAAMU,CAAC,GACL,CAACT,UAAU,CAACU,IAAX,IACEF,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACI,UAApD,GAAiE,CADnE,CAAD,IAEAV,MAHF;EAIA,MAAMW,CAAC,GACL,CAACZ,UAAU,CAACa,GAAX,IACEL,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACO,SAApD,GAAgE,CADlE,CAAD,IAEAZ,MAHF;EAIA,MAAME,KAAK,GAAGJ,UAAU,CAACI,KAAX,GAAmBH,MAAjC;EACA,MAAMK,MAAM,GAAGN,UAAU,CAACM,MAAX,GAAoBJ,MAAnC;EAEA,SAAO;EACLE,IAAAA,KAAK,EAALA,KADK;EAELE,IAAAA,MAAM,EAANA,MAFK;EAGLO,IAAAA,GAAG,EAAED,CAHA;EAILG,IAAAA,KAAK,EAAEN,CAAC,GAAGL,KAJN;EAKLY,IAAAA,MAAM,EAAEJ,CAAC,GAAGN,MALP;EAMLI,IAAAA,IAAI,EAAED,CAND;EAOLA,IAAAA,CAAC,EAADA,CAPK;EAQLG,IAAAA,CAAC,EAADA;EARK,GAAP;EAUD;;EC/Cc,SAASK,eAAT,CAAyBpD,IAAzB,EAA8C;EAC3D,MAAMqD,GAAG,GAAGtD,SAAS,CAACC,IAAD,CAArB;EACA,MAAMsD,UAAU,GAAGD,GAAG,CAACE,WAAvB;EACA,MAAMC,SAAS,GAAGH,GAAG,CAACI,WAAtB;EAEA,SAAO;EACLH,IAAAA,UAAU,EAAVA,UADK;EAELE,IAAAA,SAAS,EAATA;EAFK,GAAP;EAID;;ECXc,SAASE,oBAAT,CAA8B1B,OAA9B,EAAoD;EACjE,SAAO;EACLsB,IAAAA,UAAU,EAAEtB,OAAO,CAACsB,UADf;EAELE,IAAAA,SAAS,EAAExB,OAAO,CAACwB;EAFd,GAAP;EAID;;ECAc,SAASG,aAAT,CAAuB3D,IAAvB,EAA4C;EACzD,MAAIA,IAAI,KAAKD,SAAS,CAACC,IAAD,CAAlB,IAA4B,CAACQ,aAAa,CAACR,IAAD,CAA9C,EAAsD;EACpD,WAAOoD,eAAe,CAACpD,IAAD,CAAtB;EACD,GAFD,MAEO;EACL,WAAO0D,oBAAoB,CAAC1D,IAAD,CAA3B;EACD;EACF;;ECVc,SAAS4D,WAAT,CAAqB5B,OAArB,EAAuD;EACpE,SAAOA,OAAO,GAAG,CAACA,OAAO,CAAC6B,QAAR,IAAoB,EAArB,EAAyBC,WAAzB,EAAH,GAA4C,IAA1D;EACD;;ECDc,SAASC,kBAAT,CACb/B,OADa,EAEA;EACb;EACA,SAAO,CACL,CAAC3B,SAAS,CAAC2B,OAAD,CAAT,GACGA,OAAO,CAAC7B,aADX;EAGG6B,EAAAA,OAAO,CAACgC,QAHZ,KAGyB/D,MAAM,CAAC+D,QAJ3B,EAKLC,eALF;EAMD;;ECTc,SAASC,mBAAT,CAA6BlC,OAA7B,EAAuD;EACpE;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SACED,qBAAqB,CAACgC,kBAAkB,CAAC/B,OAAD,CAAnB,CAArB,CAAmDa,IAAnD,GACAO,eAAe,CAACpB,OAAD,CAAf,CAAyBsB,UAF3B;EAID;;ECdc,SAASa,gBAAT,CACbnC,OADa,EAEQ;EACrB,SAAOjC,SAAS,CAACiC,OAAD,CAAT,CAAmBmC,gBAAnB,CAAoCnC,OAApC,CAAP;EACD;;ECJc,SAASoC,cAAT,CAAwBpC,OAAxB,EAAuD;EACpE;EADoE,0BAEzBmC,gBAAgB,CAACnC,OAAD,CAFS;EAAA,MAE5DqC,QAF4D,qBAE5DA,QAF4D;EAAA,MAElDC,SAFkD,qBAElDA,SAFkD;EAAA,MAEvCC,SAFuC,qBAEvCA,SAFuC;;EAGpE,SAAO,6BAA6BzC,IAA7B,CAAkCuC,QAAQ,GAAGE,SAAX,GAAuBD,SAAzD,CAAP;EACD;;ECID,SAASE,eAAT,CAAyBxC,OAAzB,EAA+C;EAC7C,MAAMyC,IAAI,GAAGzC,OAAO,CAACD,qBAAR,EAAb;EACA,MAAMK,MAAM,GAAGrB,KAAK,CAAC0D,IAAI,CAAClC,KAAN,CAAL,GAAoBP,OAAO,CAACM,WAA5B,IAA2C,CAA1D;EACA,MAAMD,MAAM,GAAGtB,KAAK,CAAC0D,IAAI,CAAChC,MAAN,CAAL,GAAqBT,OAAO,CAACQ,YAA7B,IAA6C,CAA5D;EAEA,SAAOJ,MAAM,KAAK,CAAX,IAAgBC,MAAM,KAAK,CAAlC;EACD;EAGD;;;EACe,SAASqC,gBAAT,CACbC,uBADa,EAEbC,YAFa,EAGbC,OAHa,EAIP;EAAA,MADNA,OACM;EADNA,IAAAA,OACM,GADa,KACb;EAAA;;EACN,MAAMC,uBAAuB,GAAGtE,aAAa,CAACoE,YAAD,CAA7C;EACA,MAAMG,oBAAoB,GACxBvE,aAAa,CAACoE,YAAD,CAAb,IAA+BJ,eAAe,CAACI,YAAD,CADhD;EAEA,MAAMX,eAAe,GAAGF,kBAAkB,CAACa,YAAD,CAA1C;EACA,MAAMH,IAAI,GAAG1C,qBAAqB,CAChC4C,uBADgC,EAEhCI,oBAFgC,EAGhCF,OAHgC,CAAlC;EAMA,MAAIG,MAAM,GAAG;EAAE1B,IAAAA,UAAU,EAAE,CAAd;EAAiBE,IAAAA,SAAS,EAAE;EAA5B,GAAb;EACA,MAAIyB,OAAO,GAAG;EAAErC,IAAAA,CAAC,EAAE,CAAL;EAAQG,IAAAA,CAAC,EAAE;EAAX,GAAd;;EAEA,MAAI+B,uBAAuB,IAAK,CAACA,uBAAD,IAA4B,CAACD,OAA7D,EAAuE;EACrE,QACEjB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B;EAEAR,IAAAA,cAAc,CAACH,eAAD,CAHhB,EAIE;EACAe,MAAAA,MAAM,GAAGrB,aAAa,CAACiB,YAAD,CAAtB;EACD;;EAED,QAAIpE,aAAa,CAACoE,YAAD,CAAjB,EAAiC;EAC/BK,MAAAA,OAAO,GAAGlD,qBAAqB,CAAC6C,YAAD,EAAe,IAAf,CAA/B;EACAK,MAAAA,OAAO,CAACrC,CAAR,IAAagC,YAAY,CAACM,UAA1B;EACAD,MAAAA,OAAO,CAAClC,CAAR,IAAa6B,YAAY,CAACO,SAA1B;EACD,KAJD,MAIO,IAAIlB,eAAJ,EAAqB;EAC1BgB,MAAAA,OAAO,CAACrC,CAAR,GAAYsB,mBAAmB,CAACD,eAAD,CAA/B;EACD;EACF;;EAED,SAAO;EACLrB,IAAAA,CAAC,EAAE6B,IAAI,CAAC5B,IAAL,GAAYmC,MAAM,CAAC1B,UAAnB,GAAgC2B,OAAO,CAACrC,CADtC;EAELG,IAAAA,CAAC,EAAE0B,IAAI,CAACzB,GAAL,GAAWgC,MAAM,CAACxB,SAAlB,GAA8ByB,OAAO,CAAClC,CAFpC;EAGLR,IAAAA,KAAK,EAAEkC,IAAI,CAAClC,KAHP;EAILE,IAAAA,MAAM,EAAEgC,IAAI,CAAChC;EAJR,GAAP;EAMD;;EC1DD;;EACe,SAAS2C,aAAT,CAAuBpD,OAAvB,EAAmD;EAChE,MAAMG,UAAU,GAAGJ,qBAAqB,CAACC,OAAD,CAAxC,CADgE;EAIhE;;EACA,MAAIO,KAAK,GAAGP,OAAO,CAACM,WAApB;EACA,MAAIG,MAAM,GAAGT,OAAO,CAACQ,YAArB;;EAEA,MAAI3B,IAAI,CAACwE,GAAL,CAASlD,UAAU,CAACI,KAAX,GAAmBA,KAA5B,KAAsC,CAA1C,EAA6C;EAC3CA,IAAAA,KAAK,GAAGJ,UAAU,CAACI,KAAnB;EACD;;EAED,MAAI1B,IAAI,CAACwE,GAAL,CAASlD,UAAU,CAACM,MAAX,GAAoBA,MAA7B,KAAwC,CAA5C,EAA+C;EAC7CA,IAAAA,MAAM,GAAGN,UAAU,CAACM,MAApB;EACD;;EAED,SAAO;EACLG,IAAAA,CAAC,EAAEZ,OAAO,CAACc,UADN;EAELC,IAAAA,CAAC,EAAEf,OAAO,CAACiB,SAFN;EAGLV,IAAAA,KAAK,EAALA,KAHK;EAILE,IAAAA,MAAM,EAANA;EAJK,GAAP;EAMD;;ECvBc,SAAS6C,aAAT,CAAuBtD,OAAvB,EAAyD;EACtE,MAAI4B,WAAW,CAAC5B,OAAD,CAAX,KAAyB,MAA7B,EAAqC;EACnC,WAAOA,OAAP;EACD;;EAED;EAEE;EACA;EACAA,IAAAA,OAAO,CAACuD,YAAR;EACAvD,IAAAA,OAAO,CAACwD,UADR;EAEC9E,IAAAA,YAAY,CAACsB,OAAD,CAAZ,GAAwBA,OAAO,CAACyD,IAAhC,GAAuC,IAFxC;EAGA;EACA1B,IAAAA,kBAAkB,CAAC/B,OAAD,CARpB;;EAAA;EAUD;;ECdc,SAAS0D,eAAT,CAAyB1F,IAAzB,EAAkD;EAC/D,MAAI,CAAC,MAAD,EAAS,MAAT,EAAiB,WAAjB,EAA8B2F,OAA9B,CAAsC/B,WAAW,CAAC5D,IAAD,CAAjD,KAA4D,CAAhE,EAAmE;EACjE;EACA,WAAOA,IAAI,CAACG,aAAL,CAAmByF,IAA1B;EACD;;EAED,MAAIpF,aAAa,CAACR,IAAD,CAAb,IAAuBoE,cAAc,CAACpE,IAAD,CAAzC,EAAiD;EAC/C,WAAOA,IAAP;EACD;;EAED,SAAO0F,eAAe,CAACJ,aAAa,CAACtF,IAAD,CAAd,CAAtB;EACD;;ECVD;EACA;EACA;EACA;EACA;EACA;;EACe,SAAS6F,iBAAT,CACb7D,OADa,EAEb8D,IAFa,EAG6B;EAAA;;EAAA,MAD1CA,IAC0C;EAD1CA,IAAAA,IAC0C,GADV,EACU;EAAA;;EAC1C,MAAMC,YAAY,GAAGL,eAAe,CAAC1D,OAAD,CAApC;EACA,MAAMgE,MAAM,GAAGD,YAAY,+BAAK/D,OAAO,CAAC7B,aAAb,qBAAK,sBAAuByF,IAA5B,CAA3B;EACA,MAAMvC,GAAG,GAAGtD,SAAS,CAACgG,YAAD,CAArB;EACA,MAAME,MAAM,GAAGD,MAAM,GACjB,CAAC3C,GAAD,EAAM6C,MAAN,CACE7C,GAAG,CAACX,cAAJ,IAAsB,EADxB,EAEE0B,cAAc,CAAC2B,YAAD,CAAd,GAA+BA,YAA/B,GAA8C,EAFhD,CADiB,GAKjBA,YALJ;EAMA,MAAMI,WAAW,GAAGL,IAAI,CAACI,MAAL,CAAYD,MAAZ,CAApB;EAEA,SAAOD,MAAM,GACTG,WADS;EAGTA,EAAAA,WAAW,CAACD,MAAZ,CAAmBL,iBAAiB,CAACP,aAAa,CAACW,MAAD,CAAd,CAApC,CAHJ;EAID;;EC7Bc,SAASG,cAAT,CAAwBpE,OAAxB,EAAmD;EAChE,SAAO,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsB2D,OAAtB,CAA8B/B,WAAW,CAAC5B,OAAD,CAAzC,KAAuD,CAA9D;EACD;;ECID,SAASqE,mBAAT,CAA6BrE,OAA7B,EAAyD;EACvD,MACE,CAACxB,aAAa,CAACwB,OAAD,CAAd;EAEAmC,EAAAA,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BsE,QAA1B,KAAuC,OAHzC,EAIE;EACA,WAAO,IAAP;EACD;;EAED,SAAOtE,OAAO,CAAC4C,YAAf;EACD;EAGD;;;EACA,SAAS2B,kBAAT,CAA4BvE,OAA5B,EAA8C;EAC5C,MAAMwE,SAAS,GAAG,WAAW1E,IAAX,CAAgBd,WAAW,EAA3B,CAAlB;EACA,MAAMyF,IAAI,GAAG,WAAW3E,IAAX,CAAgBd,WAAW,EAA3B,CAAb;;EAEA,MAAIyF,IAAI,IAAIjG,aAAa,CAACwB,OAAD,CAAzB,EAAoC;EAClC;EACA,QAAM0E,UAAU,GAAGvC,gBAAgB,CAACnC,OAAD,CAAnC;;EACA,QAAI0E,UAAU,CAACJ,QAAX,KAAwB,OAA5B,EAAqC;EACnC,aAAO,IAAP;EACD;EACF;;EAED,MAAIK,WAAW,GAAGrB,aAAa,CAACtD,OAAD,CAA/B;;EAEA,MAAItB,YAAY,CAACiG,WAAD,CAAhB,EAA+B;EAC7BA,IAAAA,WAAW,GAAGA,WAAW,CAAClB,IAA1B;EACD;;EAED,SACEjF,aAAa,CAACmG,WAAD,CAAb,IACA,CAAC,MAAD,EAAS,MAAT,EAAiBhB,OAAjB,CAAyB/B,WAAW,CAAC+C,WAAD,CAApC,IAAqD,CAFvD,EAGE;EACA,QAAMC,GAAG,GAAGzC,gBAAgB,CAACwC,WAAD,CAA5B,CADA;EAIA;EACA;;EACA,QACEC,GAAG,CAACC,SAAJ,KAAkB,MAAlB,IACAD,GAAG,CAACE,WAAJ,KAAoB,MADpB,IAEAF,GAAG,CAACG,OAAJ,KAAgB,OAFhB,IAGA,CAAC,WAAD,EAAc,aAAd,EAA6BpB,OAA7B,CAAqCiB,GAAG,CAACI,UAAzC,MAAyD,CAAC,CAH1D,IAICR,SAAS,IAAII,GAAG,CAACI,UAAJ,KAAmB,QAJjC,IAKCR,SAAS,IAAII,GAAG,CAACK,MAAjB,IAA2BL,GAAG,CAACK,MAAJ,KAAe,MAN7C,EAOE;EACA,aAAON,WAAP;EACD,KATD,MASO;EACLA,MAAAA,WAAW,GAAGA,WAAW,CAACnB,UAA1B;EACD;EACF;;EAED,SAAO,IAAP;EACD;EAGD;;;EACe,SAAS0B,eAAT,CAAyBlF,OAAzB,EAA2C;EACxD,MAAM/B,MAAM,GAAGF,SAAS,CAACiC,OAAD,CAAxB;EAEA,MAAI4C,YAAY,GAAGyB,mBAAmB,CAACrE,OAAD,CAAtC;;EAEA,SACE4C,YAAY,IACZwB,cAAc,CAACxB,YAAD,CADd,IAEAT,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAH9C,EAIE;EACA1B,IAAAA,YAAY,GAAGyB,mBAAmB,CAACzB,YAAD,CAAlC;EACD;;EAED,MACEA,YAAY,KACXhB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B,IACEhB,WAAW,CAACgB,YAAD,CAAX,KAA8B,MAA9B,IACCT,gBAAgB,CAACS,YAAD,CAAhB,CAA+B0B,QAA/B,KAA4C,QAHpC,CADd,EAKE;EACA,WAAOrG,MAAP;EACD;;EAED,SAAO2E,YAAY,IAAI2B,kBAAkB,CAACvE,OAAD,CAAlC,IAA+C/B,MAAtD;EACD;;EC3FM,IAAM+C,GAAU,GAAG,KAAnB;EACA,IAAMG,MAAgB,GAAG,QAAzB;EACA,IAAMD,KAAc,GAAG,OAAvB;EACA,IAAML,IAAY,GAAG,MAArB;EAOA,IAAMsE,cAAoC,GAAG,CAACnE,GAAD,EAAMG,MAAN,EAAcD,KAAd,EAAqBL,IAArB,CAA7C;EAEA,IAAMuE,KAAc,GAAG,OAAvB;EACA,IAAMC,GAAU,GAAG,KAAnB;EAGA,IAAMC,eAAkC,GAAG,iBAA3C;EACA,IAAMC,QAAoB,GAAG,UAA7B;EAIA,IAAMC,MAAgB,GAAG,QAAzB;EACA,IAAMC,SAAsB,GAAG,WAA/B;;EAmCA,IAAMC,UAAwB,GAAG,YAAjC;EACA,IAAMC,IAAY,GAAG,MAArB;EACA,IAAMC,SAAsB,GAAG,WAA/B;;EAEA,IAAMC,UAAwB,GAAG,YAAjC;EACA,IAAMC,IAAY,GAAG,MAArB;EACA,IAAMC,SAAsB,GAAG,WAA/B;;EAEA,IAAMC,WAA0B,GAAG,aAAnC;EACA,IAAMC,KAAc,GAAG,OAAvB;EACA,IAAMC,UAAwB,GAAG,YAAjC;EACA,IAAMC,cAAqC,GAAG,CACnDT,UADmD,EAEnDC,IAFmD,EAGnDC,SAHmD,EAInDC,UAJmD,EAKnDC,IALmD,EAMnDC,SANmD,EAOnDC,WAPmD,EAQnDC,KARmD,EASnDC,UATmD,CAA9C;;EChEP,SAASE,KAAT,CAAeC,SAAf,EAA0B;EACxB,MAAM9G,GAAG,GAAG,IAAI+G,GAAJ,EAAZ;EACA,MAAMC,OAAO,GAAG,IAAIC,GAAJ,EAAhB;EACA,MAAMC,MAAM,GAAG,EAAf;EAEAJ,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAAAC,QAAQ,EAAI;EAC5BpH,IAAAA,GAAG,CAACqH,GAAJ,CAAQD,QAAQ,CAACE,IAAjB,EAAuBF,QAAvB;EACD,GAFD,EALwB;;EAUxB,WAASG,IAAT,CAAcH,QAAd,EAA4C;EAC1CJ,IAAAA,OAAO,CAACQ,GAAR,CAAYJ,QAAQ,CAACE,IAArB;EAEA,QAAMG,QAAQ,aACRL,QAAQ,CAACK,QAAT,IAAqB,EADb,EAERL,QAAQ,CAACM,gBAAT,IAA6B,EAFrB,CAAd;EAKAD,IAAAA,QAAQ,CAACN,OAAT,CAAiB,UAAAQ,GAAG,EAAI;EACtB,UAAI,CAACX,OAAO,CAACY,GAAR,CAAYD,GAAZ,CAAL,EAAuB;EACrB,YAAME,WAAW,GAAG7H,GAAG,CAAC8H,GAAJ,CAAQH,GAAR,CAApB;;EAEA,YAAIE,WAAJ,EAAiB;EACfN,UAAAA,IAAI,CAACM,WAAD,CAAJ;EACD;EACF;EACF,KARD;EAUAX,IAAAA,MAAM,CAACa,IAAP,CAAYX,QAAZ;EACD;;EAEDN,EAAAA,SAAS,CAACK,OAAV,CAAkB,UAAAC,QAAQ,EAAI;EAC5B,QAAI,CAACJ,OAAO,CAACY,GAAR,CAAYR,QAAQ,CAACE,IAArB,CAAL,EAAiC;EAC/B;EACAC,MAAAA,IAAI,CAACH,QAAD,CAAJ;EACD;EACF,GALD;EAOA,SAAOF,MAAP;EACD;;EAEc,SAASc,cAAT,CACblB,SADa,EAEc;EAC3B;EACA,MAAMmB,gBAAgB,GAAGpB,KAAK,CAACC,SAAD,CAA9B,CAF2B;;EAK3B,SAAOF,cAAc,CAACsB,MAAf,CAAsB,UAACC,GAAD,EAAMC,KAAN,EAAgB;EAC3C,WAAOD,GAAG,CAACxD,MAAJ,CACLsD,gBAAgB,CAACvC,MAAjB,CAAwB,UAAA0B,QAAQ;EAAA,aAAIA,QAAQ,CAACgB,KAAT,KAAmBA,KAAvB;EAAA,KAAhC,CADK,CAAP;EAGD,GAJM,EAIJ,EAJI,CAAP;EAKD;;ECxDc,SAASC,QAAT,CAAqBC,EAArB,EAAqD;EAClE,MAAIC,OAAJ;EACA,SAAO,YAAM;EACX,QAAI,CAACA,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAIC,OAAJ,CAAe,UAAAC,OAAO,EAAI;EAClCD,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,YAAM;EAC3BH,UAAAA,OAAO,GAAGI,SAAV;EACAF,UAAAA,OAAO,CAACH,EAAE,EAAH,CAAP;EACD,SAHD;EAID,OALS,CAAV;EAMD;;EAED,WAAOC,OAAP;EACD,GAXD;EAYD;;ECbc,SAASK,WAAT,CACb9B,SADa,EAEsB;EACnC,MAAM+B,MAAM,GAAG/B,SAAS,CAACoB,MAAV,CAAiB,UAACW,MAAD,EAASC,OAAT,EAAqB;EACnD,QAAMC,QAAQ,GAAGF,MAAM,CAACC,OAAO,CAACxB,IAAT,CAAvB;EACAuB,IAAAA,MAAM,CAACC,OAAO,CAACxB,IAAT,CAAN,GAAuByB,QAAQ,qBAEtBA,QAFsB,EAGtBD,OAHsB;EAIzBE,MAAAA,OAAO,oBAAOD,QAAQ,CAACC,OAAhB,EAA4BF,OAAO,CAACE,OAApC,CAJkB;EAKzBC,MAAAA,IAAI,oBAAOF,QAAQ,CAACE,IAAhB,EAAyBH,OAAO,CAACG,IAAjC;EALqB,SAO3BH,OAPJ;EAQA,WAAOD,MAAP;EACD,GAXc,EAWZ,EAXY,CAAf,CADmC;;EAenC,SAAOK,MAAM,CAACC,IAAP,CAAYN,MAAZ,EAAoB7I,GAApB,CAAwB,UAAAoJ,GAAG;EAAA,WAAIP,MAAM,CAACO,GAAD,CAAV;EAAA,GAA3B,CAAP;EACD;;ECdc,SAASC,eAAT,CACb5I,OADa,EAEb6I,QAFa,EAGb;EACA,MAAMxH,GAAG,GAAGtD,SAAS,CAACiC,OAAD,CAArB;EACA,MAAM8I,IAAI,GAAG/G,kBAAkB,CAAC/B,OAAD,CAA/B;EACA,MAAMU,cAAc,GAAGW,GAAG,CAACX,cAA3B;EAEA,MAAIH,KAAK,GAAGuI,IAAI,CAACC,WAAjB;EACA,MAAItI,MAAM,GAAGqI,IAAI,CAACE,YAAlB;EACA,MAAIpI,CAAC,GAAG,CAAR;EACA,MAAIG,CAAC,GAAG,CAAR;;EAEA,MAAIL,cAAJ,EAAoB;EAClBH,IAAAA,KAAK,GAAGG,cAAc,CAACH,KAAvB;EACAE,IAAAA,MAAM,GAAGC,cAAc,CAACD,MAAxB;EAEA,QAAMwI,cAAc,GAAGpJ,gBAAgB,EAAvC;;EAEA,QAAIoJ,cAAc,IAAK,CAACA,cAAD,IAAmBJ,QAAQ,KAAK,OAAvD,EAAiE;EAC/DjI,MAAAA,CAAC,GAAGF,cAAc,CAACI,UAAnB;EACAC,MAAAA,CAAC,GAAGL,cAAc,CAACO,SAAnB;EACD;EACF;;EAED,SAAO;EACLV,IAAAA,KAAK,EAALA,KADK;EAELE,IAAAA,MAAM,EAANA,MAFK;EAGLG,IAAAA,CAAC,EAAEA,CAAC,GAAGsB,mBAAmB,CAAClC,OAAD,CAHrB;EAILe,IAAAA,CAAC,EAADA;EAJK,GAAP;EAMD;;EC7BD;;EACe,SAASmI,eAAT,CAAyBlJ,OAAzB,EAAqD;EAAA;;EAClE,MAAM8I,IAAI,GAAG/G,kBAAkB,CAAC/B,OAAD,CAA/B;EACA,MAAMmJ,SAAS,GAAG/H,eAAe,CAACpB,OAAD,CAAjC;EACA,MAAM4D,IAAI,4BAAG5D,OAAO,CAAC7B,aAAX,qBAAG,sBAAuByF,IAApC;EAEA,MAAMrD,KAAK,GAAG3B,GAAG,CACfkK,IAAI,CAACM,WADU,EAEfN,IAAI,CAACC,WAFU,EAGfnF,IAAI,GAAGA,IAAI,CAACwF,WAAR,GAAsB,CAHX,EAIfxF,IAAI,GAAGA,IAAI,CAACmF,WAAR,GAAsB,CAJX,CAAjB;EAMA,MAAMtI,MAAM,GAAG7B,GAAG,CAChBkK,IAAI,CAACO,YADW,EAEhBP,IAAI,CAACE,YAFW,EAGhBpF,IAAI,GAAGA,IAAI,CAACyF,YAAR,GAAuB,CAHX,EAIhBzF,IAAI,GAAGA,IAAI,CAACoF,YAAR,GAAuB,CAJX,CAAlB;EAOA,MAAIpI,CAAC,GAAG,CAACuI,SAAS,CAAC7H,UAAX,GAAwBY,mBAAmB,CAAClC,OAAD,CAAnD;EACA,MAAMe,CAAC,GAAG,CAACoI,SAAS,CAAC3H,SAArB;;EAEA,MAAIW,gBAAgB,CAACyB,IAAI,IAAIkF,IAAT,CAAhB,CAA+BQ,SAA/B,KAA6C,KAAjD,EAAwD;EACtD1I,IAAAA,CAAC,IAAIhC,GAAG,CAACkK,IAAI,CAACC,WAAN,EAAmBnF,IAAI,GAAGA,IAAI,CAACmF,WAAR,GAAsB,CAA7C,CAAH,GAAqDxI,KAA1D;EACD;;EAED,SAAO;EAAEA,IAAAA,KAAK,EAALA,KAAF;EAASE,IAAAA,MAAM,EAANA,MAAT;EAAiBG,IAAAA,CAAC,EAADA,CAAjB;EAAoBG,IAAAA,CAAC,EAADA;EAApB,GAAP;EACD;;ECjCc,SAASwI,QAAT,CAAkBC,MAAlB,EAAmCC,KAAnC,EAAmD;EAChE,MAAMC,QAAQ,GAAGD,KAAK,CAACE,WAAN,IAAqBF,KAAK,CAACE,WAAN,EAAtC,CADgE;;EAIhE,MAAIH,MAAM,CAACD,QAAP,CAAgBE,KAAhB,CAAJ,EAA4B;EAC1B,WAAO,IAAP;EACD,GAFD;EAAA,OAIK,IAAIC,QAAQ,IAAIhL,YAAY,CAACgL,QAAD,CAA5B,EAAwC;EAC3C,UAAIE,IAAI,GAAGH,KAAX;;EACA,SAAG;EACD,YAAIG,IAAI,IAAIJ,MAAM,CAACK,UAAP,CAAkBD,IAAlB,CAAZ,EAAqC;EACnC,iBAAO,IAAP;EACD,SAHA;;;EAKDA,QAAAA,IAAI,GAAGA,IAAI,CAACpG,UAAL,IAAmBoG,IAAI,CAACnG,IAA/B;EACD,OAND,QAMSmG,IANT;EAOD,KAjB+D;;;EAoBhE,SAAO,KAAP;EACD;;ECrBc,SAASE,gBAAT,CAA0BrH,IAA1B,EAAwD;EACrE,2BACKA,IADL;EAEE5B,IAAAA,IAAI,EAAE4B,IAAI,CAAC7B,CAFb;EAGEI,IAAAA,GAAG,EAAEyB,IAAI,CAAC1B,CAHZ;EAIEG,IAAAA,KAAK,EAAEuB,IAAI,CAAC7B,CAAL,GAAS6B,IAAI,CAAClC,KAJvB;EAKEY,IAAAA,MAAM,EAAEsB,IAAI,CAAC1B,CAAL,GAAS0B,IAAI,CAAChC;EALxB;EAOD;;ECOD,SAASsJ,0BAAT,CACE/J,OADF,EAEE6I,QAFF,EAGE;EACA,MAAMpG,IAAI,GAAG1C,qBAAqB,CAACC,OAAD,EAAU,KAAV,EAAiB6I,QAAQ,KAAK,OAA9B,CAAlC;EAEApG,EAAAA,IAAI,CAACzB,GAAL,GAAWyB,IAAI,CAACzB,GAAL,GAAWhB,OAAO,CAACmD,SAA9B;EACAV,EAAAA,IAAI,CAAC5B,IAAL,GAAY4B,IAAI,CAAC5B,IAAL,GAAYb,OAAO,CAACkD,UAAhC;EACAT,EAAAA,IAAI,CAACtB,MAAL,GAAcsB,IAAI,CAACzB,GAAL,GAAWhB,OAAO,CAACgJ,YAAjC;EACAvG,EAAAA,IAAI,CAACvB,KAAL,GAAauB,IAAI,CAAC5B,IAAL,GAAYb,OAAO,CAAC+I,WAAjC;EACAtG,EAAAA,IAAI,CAAClC,KAAL,GAAaP,OAAO,CAAC+I,WAArB;EACAtG,EAAAA,IAAI,CAAChC,MAAL,GAAcT,OAAO,CAACgJ,YAAtB;EACAvG,EAAAA,IAAI,CAAC7B,CAAL,GAAS6B,IAAI,CAAC5B,IAAd;EACA4B,EAAAA,IAAI,CAAC1B,CAAL,GAAS0B,IAAI,CAACzB,GAAd;EAEA,SAAOyB,IAAP;EACD;;EAED,SAASuH,0BAAT,CACEhK,OADF,EAEEiK,cAFF,EAGEpB,QAHF,EAIoB;EAClB,SAAOoB,cAAc,KAAK1E,QAAnB,GACHuE,gBAAgB,CAAClB,eAAe,CAAC5I,OAAD,EAAU6I,QAAV,CAAhB,CADb,GAEHxK,SAAS,CAAC4L,cAAD,CAAT,GACAF,0BAA0B,CAACE,cAAD,EAAiBpB,QAAjB,CAD1B,GAEAiB,gBAAgB,CAACZ,eAAe,CAACnH,kBAAkB,CAAC/B,OAAD,CAAnB,CAAhB,CAJpB;EAKD;EAGD;EACA;;;EACA,SAASkK,kBAAT,CAA4BlK,OAA5B,EAA8D;EAC5D,MAAMsF,eAAe,GAAGzB,iBAAiB,CAACP,aAAa,CAACtD,OAAD,CAAd,CAAzC;EACA,MAAMmK,iBAAiB,GACrB,CAAC,UAAD,EAAa,OAAb,EAAsBxG,OAAtB,CAA8BxB,gBAAgB,CAACnC,OAAD,CAAhB,CAA0BsE,QAAxD,KAAqE,CADvE;EAEA,MAAM8F,cAAc,GAClBD,iBAAiB,IAAI3L,aAAa,CAACwB,OAAD,CAAlC,GACIkF,eAAe,CAAClF,OAAD,CADnB,GAEIA,OAHN;;EAKA,MAAI,CAAC3B,SAAS,CAAC+L,cAAD,CAAd,EAAgC;EAC9B,WAAO,EAAP;EACD,GAX2D;;;EAc5D,SAAO9E,eAAe,CAACL,MAAhB,CACL,UAACgF,cAAD;EAAA,WACE5L,SAAS,CAAC4L,cAAD,CAAT,IACAV,QAAQ,CAACU,cAAD,EAAiBG,cAAjB,CADR,IAEAxI,WAAW,CAACqI,cAAD,CAAX,KAAgC,MAHlC;EAAA,GADK,CAAP;EAMD;EAGD;;;EACe,SAASI,eAAT,CACbrK,OADa,EAEbsK,QAFa,EAGbC,YAHa,EAIb1B,QAJa,EAKK;EAClB,MAAM2B,mBAAmB,GACvBF,QAAQ,KAAK,iBAAb,GACIJ,kBAAkB,CAAClK,OAAD,CADtB,GAEI,GAAGkE,MAAH,CAAUoG,QAAV,CAHN;EAIA,MAAMhF,eAAe,aAAOkF,mBAAP,GAA4BD,YAA5B,EAArB;EACA,MAAME,mBAAmB,GAAGnF,eAAe,CAAC,CAAD,CAA3C;EAEA,MAAMoF,YAAY,GAAGpF,eAAe,CAACmC,MAAhB,CAAuB,UAACkD,OAAD,EAAUV,cAAV,EAA6B;EACvE,QAAMxH,IAAI,GAAGuH,0BAA0B,CAAChK,OAAD,EAAUiK,cAAV,EAA0BpB,QAA1B,CAAvC;EAEA8B,IAAAA,OAAO,CAAC3J,GAAR,GAAcpC,GAAG,CAAC6D,IAAI,CAACzB,GAAN,EAAW2J,OAAO,CAAC3J,GAAnB,CAAjB;EACA2J,IAAAA,OAAO,CAACzJ,KAAR,GAAgBpC,GAAG,CAAC2D,IAAI,CAACvB,KAAN,EAAayJ,OAAO,CAACzJ,KAArB,CAAnB;EACAyJ,IAAAA,OAAO,CAACxJ,MAAR,GAAiBrC,GAAG,CAAC2D,IAAI,CAACtB,MAAN,EAAcwJ,OAAO,CAACxJ,MAAtB,CAApB;EACAwJ,IAAAA,OAAO,CAAC9J,IAAR,GAAejC,GAAG,CAAC6D,IAAI,CAAC5B,IAAN,EAAY8J,OAAO,CAAC9J,IAApB,CAAlB;EAEA,WAAO8J,OAAP;EACD,GAToB,EASlBX,0BAA0B,CAAChK,OAAD,EAAUyK,mBAAV,EAA+B5B,QAA/B,CATR,CAArB;EAWA6B,EAAAA,YAAY,CAACnK,KAAb,GAAqBmK,YAAY,CAACxJ,KAAb,GAAqBwJ,YAAY,CAAC7J,IAAvD;EACA6J,EAAAA,YAAY,CAACjK,MAAb,GAAsBiK,YAAY,CAACvJ,MAAb,GAAsBuJ,YAAY,CAAC1J,GAAzD;EACA0J,EAAAA,YAAY,CAAC9J,CAAb,GAAiB8J,YAAY,CAAC7J,IAA9B;EACA6J,EAAAA,YAAY,CAAC3J,CAAb,GAAiB2J,YAAY,CAAC1J,GAA9B;EAEA,SAAO0J,YAAP;EACD;;ECtGc,SAASE,gBAAT,CACbC,SADa,EAEE;EACf,SAAQA,SAAS,CAACC,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAR;EACD;;ECJc,SAASC,YAAT,CAAsBF,SAAtB,EAAwD;EACrE,SAAQA,SAAS,CAACC,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAR;EACD;;ECFc,SAASE,wBAAT,CACbH,SADa,EAEF;EACX,SAAO,CAAC,KAAD,EAAQ,QAAR,EAAkBlH,OAAlB,CAA0BkH,SAA1B,KAAwC,CAAxC,GAA4C,GAA5C,GAAkD,GAAzD;EACD;;ECKc,SAASI,cAAT,OASH;EAAA,MARVxF,SAQU,QARVA,SAQU;EAAA,MAPVzF,OAOU,QAPVA,OAOU;EAAA,MANV6K,SAMU,QANVA,SAMU;EACV,MAAMK,aAAa,GAAGL,SAAS,GAAGD,gBAAgB,CAACC,SAAD,CAAnB,GAAiC,IAAhE;EACA,MAAMM,SAAS,GAAGN,SAAS,GAAGE,YAAY,CAACF,SAAD,CAAf,GAA6B,IAAxD;EACA,MAAMO,OAAO,GAAG3F,SAAS,CAAC7E,CAAV,GAAc6E,SAAS,CAAClF,KAAV,GAAkB,CAAhC,GAAoCP,OAAO,CAACO,KAAR,GAAgB,CAApE;EACA,MAAM8K,OAAO,GAAG5F,SAAS,CAAC1E,CAAV,GAAc0E,SAAS,CAAChF,MAAV,GAAmB,CAAjC,GAAqCT,OAAO,CAACS,MAAR,GAAiB,CAAtE;EAEA,MAAIwC,OAAJ;;EACA,UAAQiI,aAAR;EACE,SAAKlK,GAAL;EACEiC,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAEwK,OADK;EAERrK,QAAAA,CAAC,EAAE0E,SAAS,CAAC1E,CAAV,GAAcf,OAAO,CAACS;EAFjB,OAAV;EAIA;;EACF,SAAKU,MAAL;EACE8B,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAEwK,OADK;EAERrK,QAAAA,CAAC,EAAE0E,SAAS,CAAC1E,CAAV,GAAc0E,SAAS,CAAChF;EAFnB,OAAV;EAIA;;EACF,SAAKS,KAAL;EACE+B,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE6E,SAAS,CAAC7E,CAAV,GAAc6E,SAAS,CAAClF,KADnB;EAERQ,QAAAA,CAAC,EAAEsK;EAFK,OAAV;EAIA;;EACF,SAAKxK,IAAL;EACEoC,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE6E,SAAS,CAAC7E,CAAV,GAAcZ,OAAO,CAACO,KADjB;EAERQ,QAAAA,CAAC,EAAEsK;EAFK,OAAV;EAIA;;EACF;EACEpI,MAAAA,OAAO,GAAG;EACRrC,QAAAA,CAAC,EAAE6E,SAAS,CAAC7E,CADL;EAERG,QAAAA,CAAC,EAAE0E,SAAS,CAAC1E;EAFL,OAAV;EA1BJ;;EAgCA,MAAMuK,QAAQ,GAAGJ,aAAa,GAC1BF,wBAAwB,CAACE,aAAD,CADE,GAE1B,IAFJ;;EAIA,MAAII,QAAQ,IAAI,IAAhB,EAAsB;EACpB,QAAMC,GAAG,GAAGD,QAAQ,KAAK,GAAb,GAAmB,QAAnB,GAA8B,OAA1C;;EAEA,YAAQH,SAAR;EACE,WAAK/F,KAAL;EACEnC,QAAAA,OAAO,CAACqI,QAAD,CAAP,GACErI,OAAO,CAACqI,QAAD,CAAP,IAAqB7F,SAAS,CAAC8F,GAAD,CAAT,GAAiB,CAAjB,GAAqBvL,OAAO,CAACuL,GAAD,CAAP,GAAe,CAAzD,CADF;EAEA;;EACF,WAAKlG,GAAL;EACEpC,QAAAA,OAAO,CAACqI,QAAD,CAAP,GACErI,OAAO,CAACqI,QAAD,CAAP,IAAqB7F,SAAS,CAAC8F,GAAD,CAAT,GAAiB,CAAjB,GAAqBvL,OAAO,CAACuL,GAAD,CAAP,GAAe,CAAzD,CADF;EAEA;EARJ;EAWD;;EAED,SAAOtI,OAAP;EACD;;EC9Ec,SAASuI,kBAAT,GAA0C;EACvD,SAAO;EACLxK,IAAAA,GAAG,EAAE,CADA;EAELE,IAAAA,KAAK,EAAE,CAFF;EAGLC,IAAAA,MAAM,EAAE,CAHH;EAILN,IAAAA,IAAI,EAAE;EAJD,GAAP;EAMD;;ECNc,SAAS4K,kBAAT,CACbC,aADa,EAED;EACZ,2BACKF,kBAAkB,EADvB,EAEKE,aAFL;EAID;;ECTc,SAASC,eAAT,CAGbC,KAHa,EAGHlD,IAHG,EAGmC;EAChD,SAAOA,IAAI,CAACjB,MAAL,CAAY,UAACoE,OAAD,EAAUlD,GAAV,EAAkB;EACnCkD,IAAAA,OAAO,CAAClD,GAAD,CAAP,GAAeiD,KAAf;EACA,WAAOC,OAAP;EACD,GAHM,EAGJ,EAHI,CAAP;EAID;;ECuBc,SAASC,cAAT,CACbC,KADa,EAEbxD,OAFa,EAGD;EAAA,MADZA,OACY;EADZA,IAAAA,OACY,GADe,EACf;EAAA;;EAAA,iBASRA,OATQ;EAAA,oCAEVsC,SAFU;EAAA,MAEVA,SAFU,mCAEEkB,KAAK,CAAClB,SAFR;EAAA,mCAGVhC,QAHU;EAAA,MAGVA,QAHU,kCAGCkD,KAAK,CAAClD,QAHP;EAAA,mCAIVyB,QAJU;EAAA,MAIVA,QAJU,kCAIChF,eAJD;EAAA,uCAKViF,YALU;EAAA,MAKVA,YALU,sCAKKhF,QALL;EAAA,uCAMVyG,cANU;EAAA,MAMVA,cANU,sCAMOxG,MANP;EAAA,sCAOVyG,WAPU;EAAA,MAOVA,WAPU,qCAOI,KAPJ;EAAA,kCAQVC,OARU;EAAA,MAQVA,OARU,iCAQA,CARA;EAWZ,MAAMR,aAAa,GAAGD,kBAAkB,CACtC,OAAOS,OAAP,KAAmB,QAAnB,GACIA,OADJ,GAEIP,eAAe,CAACO,OAAD,EAAU/G,cAAV,CAHmB,CAAxC;EAMA,MAAMgH,UAAU,GAAGH,cAAc,KAAKxG,MAAnB,GAA4BC,SAA5B,GAAwCD,MAA3D;EAEA,MAAM4G,UAAU,GAAGL,KAAK,CAACM,KAAN,CAAY7G,MAA/B;EACA,MAAMxF,OAAO,GAAG+L,KAAK,CAACO,QAAN,CAAeL,WAAW,GAAGE,UAAH,GAAgBH,cAA1C,CAAhB;EAEA,MAAMO,kBAAkB,GAAGlC,eAAe,CACxChM,SAAS,CAAC2B,OAAD,CAAT,GACIA,OADJ,GAEIA,OAAO,CAACwM,cAAR,IAA0BzK,kBAAkB,CAACgK,KAAK,CAACO,QAAN,CAAe9G,MAAhB,CAHR,EAIxC8E,QAJwC,EAKxCC,YALwC,EAMxC1B,QANwC,CAA1C;EASA,MAAM4D,mBAAmB,GAAG1M,qBAAqB,CAACgM,KAAK,CAACO,QAAN,CAAe7G,SAAhB,CAAjD;EAEA,MAAMiH,aAAa,GAAGzB,cAAc,CAAC;EACnCxF,IAAAA,SAAS,EAAEgH,mBADwB;EAEnCzM,IAAAA,OAAO,EAAEoM,UAF0B;EAGnCvD,IAAAA,QAAQ,EAAE,UAHyB;EAInCgC,IAAAA,SAAS,EAATA;EAJmC,GAAD,CAApC;EAOA,MAAM8B,gBAAgB,GAAG7C,gBAAgB,mBACpCsC,UADoC,EAEpCM,aAFoC,EAAzC;EAKA,MAAME,iBAAiB,GACrBZ,cAAc,KAAKxG,MAAnB,GAA4BmH,gBAA5B,GAA+CF,mBADjD,CA7CY;EAiDZ;;EACA,MAAMI,eAAe,GAAG;EACtB7L,IAAAA,GAAG,EAAEuL,kBAAkB,CAACvL,GAAnB,GAAyB4L,iBAAiB,CAAC5L,GAA3C,GAAiD0K,aAAa,CAAC1K,GAD9C;EAEtBG,IAAAA,MAAM,EACJyL,iBAAiB,CAACzL,MAAlB,GACAoL,kBAAkB,CAACpL,MADnB,GAEAuK,aAAa,CAACvK,MALM;EAMtBN,IAAAA,IAAI,EAAE0L,kBAAkB,CAAC1L,IAAnB,GAA0B+L,iBAAiB,CAAC/L,IAA5C,GAAmD6K,aAAa,CAAC7K,IANjD;EAOtBK,IAAAA,KAAK,EACH0L,iBAAiB,CAAC1L,KAAlB,GAA0BqL,kBAAkB,CAACrL,KAA7C,GAAqDwK,aAAa,CAACxK;EAR/C,GAAxB;EAWA,MAAM4L,UAAU,GAAGf,KAAK,CAACgB,aAAN,CAAoBC,MAAvC,CA7DY;;EAgEZ,MAAIhB,cAAc,KAAKxG,MAAnB,IAA6BsH,UAAjC,EAA6C;EAC3C,QAAME,MAAM,GAAGF,UAAU,CAACjC,SAAD,CAAzB;EAEApC,IAAAA,MAAM,CAACC,IAAP,CAAYmE,eAAZ,EAA6BnG,OAA7B,CAAqC,UAACiC,GAAD,EAAS;EAC5C,UAAMsE,QAAQ,GAAG,CAAC/L,KAAD,EAAQC,MAAR,EAAgBwC,OAAhB,CAAwBgF,GAAxB,KAAgC,CAAhC,GAAoC,CAApC,GAAwC,CAAC,CAA1D;EACA,UAAMuE,IAAI,GAAG,CAAClM,GAAD,EAAMG,MAAN,EAAcwC,OAAd,CAAsBgF,GAAtB,KAA8B,CAA9B,GAAkC,GAAlC,GAAwC,GAArD;EACAkE,MAAAA,eAAe,CAAClE,GAAD,CAAf,IAAwBqE,MAAM,CAACE,IAAD,CAAN,GAAeD,QAAvC;EACD,KAJD;EAKD;;EAED,SAAOJ,eAAP;EACD;;EC7FD,IAAMM,eAAoC,GAAG;EAC3CtC,EAAAA,SAAS,EAAE,QADgC;EAE3CxE,EAAAA,SAAS,EAAE,EAFgC;EAG3CwC,EAAAA,QAAQ,EAAE;EAHiC,CAA7C;;EAWA,SAASuE,gBAAT,GAAwD;EAAA,oCAA3BC,IAA2B;EAA3BA,IAAAA,IAA2B;EAAA;;EACtD,SAAO,CAACA,IAAI,CAACC,IAAL,CACN,UAACtN,OAAD;EAAA,WACE,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACD,qBAAf,KAAyC,UAAtD,CADF;EAAA,GADM,CAAR;EAID;;EAEM,SAASwN,eAAT,CAAyBC,gBAAzB,EAAqE;EAAA,MAA5CA,gBAA4C;EAA5CA,IAAAA,gBAA4C,GAAJ,EAAI;EAAA;;EAAA,0BAExEA,gBAFwE;EAAA,gDAClEC,gBADkE;EAAA,MAClEA,gBADkE,sCAC/C,EAD+C;EAAA,iDAC3CC,cAD2C;EAAA,MAC3CA,cAD2C,uCAC1BP,eAD0B;EAI1E,SAAO,SAASQ,YAAT,CACLlI,SADK,EAELD,MAFK,EAGL+C,OAHK,EAIK;EAAA,QADVA,OACU;EADVA,MAAAA,OACU,GADmCmF,cACnC;EAAA;;EACV,QAAI3B,KAAoB,GAAG;EACzBlB,MAAAA,SAAS,EAAE,QADc;EAEzBrD,MAAAA,gBAAgB,EAAE,EAFO;EAGzBe,MAAAA,OAAO,oBAAO4E,eAAP,EAA2BO,cAA3B,CAHkB;EAIzBX,MAAAA,aAAa,EAAE,EAJU;EAKzBT,MAAAA,QAAQ,EAAE;EACR7G,QAAAA,SAAS,EAATA,SADQ;EAERD,QAAAA,MAAM,EAANA;EAFQ,OALe;EASzBoI,MAAAA,UAAU,EAAE,EATa;EAUzBC,MAAAA,MAAM,EAAE;EAViB,KAA3B;EAaA,QAAIC,gBAAmC,GAAG,EAA1C;EACA,QAAIC,WAAW,GAAG,KAAlB;EAEA,QAAMC,QAAQ,GAAG;EACfjC,MAAAA,KAAK,EAALA,KADe;EAEfkC,MAAAA,UAFe,sBAEJC,gBAFI,EAEc;EAC3B,YAAM3F,OAAO,GACX,OAAO2F,gBAAP,KAA4B,UAA5B,GACIA,gBAAgB,CAACnC,KAAK,CAACxD,OAAP,CADpB,GAEI2F,gBAHN;EAKAC,QAAAA,sBAAsB;EAEtBpC,QAAAA,KAAK,CAACxD,OAAN,qBAEKmF,cAFL,EAGK3B,KAAK,CAACxD,OAHX,EAIKA,OAJL;EAOAwD,QAAAA,KAAK,CAACqC,aAAN,GAAsB;EACpB3I,UAAAA,SAAS,EAAEpH,SAAS,CAACoH,SAAD,CAAT,GACP5B,iBAAiB,CAAC4B,SAAD,CADV,GAEPA,SAAS,CAAC+G,cAAV,GACA3I,iBAAiB,CAAC4B,SAAS,CAAC+G,cAAX,CADjB,GAEA,EALgB;EAMpBhH,UAAAA,MAAM,EAAE3B,iBAAiB,CAAC2B,MAAD;EANL,SAAtB,CAf2B;EAyB3B;;EACA,YAAMgC,gBAAgB,GAAGD,cAAc,CACrCY,WAAW,WAAKsF,gBAAL,EAA0B1B,KAAK,CAACxD,OAAN,CAAclC,SAAxC,EAD0B,CAAvC,CA1B2B;;EA+B3B0F,QAAAA,KAAK,CAACvE,gBAAN,GAAyBA,gBAAgB,CAACvC,MAAjB,CAAwB,UAACoJ,CAAD;EAAA,iBAAOA,CAAC,CAACC,OAAT;EAAA,SAAxB,CAAzB;EAEAC,QAAAA,kBAAkB;EAElB,eAAOP,QAAQ,CAACQ,MAAT,EAAP;EACD,OAtCc;EAwCf;EACA;EACA;EACA;EACA;EACAC,MAAAA,WA7Ce,yBA6CD;EACZ,YAAIV,WAAJ,EAAiB;EACf;EACD;;EAHW,8BAKkBhC,KAAK,CAACO,QALxB;EAAA,YAKJ7G,SALI,mBAKJA,SALI;EAAA,YAKOD,MALP,mBAKOA,MALP;EAQZ;;EACA,YAAI,CAAC4H,gBAAgB,CAAC3H,SAAD,EAAYD,MAAZ,CAArB,EAA0C;EACxC;EACD,SAXW;;;EAcZuG,QAAAA,KAAK,CAACM,KAAN,GAAc;EACZ5G,UAAAA,SAAS,EAAE/C,gBAAgB,CACzB+C,SADyB,EAEzBP,eAAe,CAACM,MAAD,CAFU,EAGzBuG,KAAK,CAACxD,OAAN,CAAcM,QAAd,KAA2B,OAHF,CADf;EAMZrD,UAAAA,MAAM,EAAEpC,aAAa,CAACoC,MAAD;EANT,SAAd,CAdY;EAwBZ;EACA;EACA;EACA;;EACAuG,QAAAA,KAAK,CAAC2C,KAAN,GAAc,KAAd;EAEA3C,QAAAA,KAAK,CAAClB,SAAN,GAAkBkB,KAAK,CAACxD,OAAN,CAAcsC,SAAhC,CA9BY;EAiCZ;EACA;EACA;;EACAkB,QAAAA,KAAK,CAACvE,gBAAN,CAAuBd,OAAvB,CACE,UAACC,QAAD;EAAA,iBACGoF,KAAK,CAACgB,aAAN,CAAoBpG,QAAQ,CAACE,IAA7B,sBACIF,QAAQ,CAAC6B,IADb,CADH;EAAA,SADF;;EAOA,aAAK,IAAImG,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG5C,KAAK,CAACvE,gBAAN,CAAuBoH,MAAnD,EAA2DD,KAAK,EAAhE,EAAoE;EAClE,cAAI5C,KAAK,CAAC2C,KAAN,KAAgB,IAApB,EAA0B;EACxB3C,YAAAA,KAAK,CAAC2C,KAAN,GAAc,KAAd;EACAC,YAAAA,KAAK,GAAG,CAAC,CAAT;EACA;EACD;;EALiE,sCAO/B5C,KAAK,CAACvE,gBAAN,CAAuBmH,KAAvB,CAP+B;EAAA,cAO1D9G,EAP0D,yBAO1DA,EAP0D;EAAA,6DAOtDU,OAPsD;EAAA,cAOtDA,QAPsD,uCAO5C,EAP4C;EAAA,cAOxC1B,IAPwC,yBAOxCA,IAPwC;;EASlE,cAAI,OAAOgB,EAAP,KAAc,UAAlB,EAA8B;EAC5BkE,YAAAA,KAAK,GAAGlE,EAAE,CAAC;EAAEkE,cAAAA,KAAK,EAALA,KAAF;EAASxD,cAAAA,OAAO,EAAPA,QAAT;EAAkB1B,cAAAA,IAAI,EAAJA,IAAlB;EAAwBmH,cAAAA,QAAQ,EAARA;EAAxB,aAAD,CAAF,IAA0CjC,KAAlD;EACD;EACF;EACF,OArGc;EAuGf;EACA;EACAyC,MAAAA,MAAM,EAAE5G,QAAQ,CACd;EAAA,eACE,IAAIG,OAAJ,CAA2B,UAACC,OAAD,EAAa;EACtCgG,UAAAA,QAAQ,CAACS,WAAT;EACAzG,UAAAA,OAAO,CAAC+D,KAAD,CAAP;EACD,SAHD,CADF;EAAA,OADc,CAzGD;EAiHf8C,MAAAA,OAjHe,qBAiHL;EACRV,QAAAA,sBAAsB;EACtBJ,QAAAA,WAAW,GAAG,IAAd;EACD;EApHc,KAAjB;;EAuHA,QAAI,CAACX,gBAAgB,CAAC3H,SAAD,EAAYD,MAAZ,CAArB,EAA0C;EACxC,aAAOwI,QAAP;EACD;;EAEDA,IAAAA,QAAQ,CAACC,UAAT,CAAoB1F,OAApB,EAA6BN,IAA7B,CAAkC,UAAC8D,KAAD,EAAW;EAC3C,UAAI,CAACgC,WAAD,IAAgBxF,OAAO,CAACuG,aAA5B,EAA2C;EACzCvG,QAAAA,OAAO,CAACuG,aAAR,CAAsB/C,KAAtB;EACD;EACF,KAJD,EA5IU;EAmJV;EACA;EACA;EACA;;EACA,aAASwC,kBAAT,GAA8B;EAC5BxC,MAAAA,KAAK,CAACvE,gBAAN,CAAuBd,OAAvB,CAA+B,gBAAoC;EAAA,YAAjCG,IAAiC,QAAjCA,IAAiC;EAAA,gCAA3B0B,OAA2B;EAAA,YAA3BA,OAA2B,6BAAjB,EAAiB;EAAA,YAAbwG,MAAa,QAAbA,MAAa;;EACjE,YAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;EAChC,cAAMC,SAAS,GAAGD,MAAM,CAAC;EAAEhD,YAAAA,KAAK,EAALA,KAAF;EAASlF,YAAAA,IAAI,EAAJA,IAAT;EAAemH,YAAAA,QAAQ,EAARA,QAAf;EAAyBzF,YAAAA,OAAO,EAAPA;EAAzB,WAAD,CAAxB;;EACA,cAAM0G,MAAM,GAAG,SAATA,MAAS,GAAM,EAArB;;EACAnB,UAAAA,gBAAgB,CAACxG,IAAjB,CAAsB0H,SAAS,IAAIC,MAAnC;EACD;EACF,OAND;EAOD;;EAED,aAASd,sBAAT,GAAkC;EAChCL,MAAAA,gBAAgB,CAACpH,OAAjB,CAAyB,UAACmB,EAAD;EAAA,eAAQA,EAAE,EAAV;EAAA,OAAzB;EACAiG,MAAAA,gBAAgB,GAAG,EAAnB;EACD;;EAED,WAAOE,QAAP;EACD,GA3KD;EA4KD;MAEYL,YAAY,gBAAGJ,eAAe;;;;;;;;"}