import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  ROUND_ARROW,
  animateFill,
  create<PERSON><PERSON><PERSON>,
  delegate,
  followCursor,
  hideAll,
  inlinePositioning,
  sticky,
  tippy_esm_default
} from "./chunk-A266GPMG.js";
import "./chunk-ZMSOBIYE.js";
export {
  animateFill,
  createSingleton,
  tippy_esm_default as default,
  delegate,
  followCursor,
  hideAll,
  inlinePositioning,
  ROUND_ARROW as roundArrow,
  sticky
};
