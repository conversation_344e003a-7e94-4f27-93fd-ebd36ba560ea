{"name": "tiptap-markdown", "version": "0.8.10", "description": "Edit markdown content in tiptap editor.", "scripts": {"test": "vitest", "dev": "vite example", "build": "vite build", "build:example": "vite build example", "preview": "vite preview example", "preversion": "npm run build", "update:tiptap": "update-by-scope @tiptap"}, "workspaces": ["example"], "main": "dist/tiptap-markdown.umd.js", "module": "dist/tiptap-markdown.es.js", "type": "module", "sideEffects": false, "exports": {".": {"import": "./dist/tiptap-markdown.es.js", "require": "./dist/tiptap-markdown.umd.js", "types": "./index.d.ts"}}, "files": ["src", "dist", "index.d.ts"], "browserslist": ["defaults", "not IE 11"], "repository": {"type": "git", "url": "git+https://github.com/aguingand/tiptap-markdown.git"}, "bugs": {"url": "https://github.com/aguingand/tiptap-markdown/issues"}, "types": "index.d.ts", "author": "<PERSON>", "license": "MIT", "dependencies": {"@types/markdown-it": "^13.0.7", "markdown-it": "^14.1.0", "markdown-it-task-lists": "^2.1.1", "prosemirror-markdown": "^1.11.1"}, "peerDependencies": {"@tiptap/core": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/preset-env": "^7.23.2", "@rollup/plugin-babel": "^5.3.1", "@tiptap/core": "^2.0.3", "@tiptap/extension-highlight": "^2.0.3", "@tiptap/extension-image": "^2.0.3", "@tiptap/extension-link": "^2.0.3", "@tiptap/extension-table": "^2.0.3", "@tiptap/extension-table-cell": "^2.0.3", "@tiptap/extension-table-header": "^2.0.3", "@tiptap/extension-table-row": "^2.0.3", "@tiptap/extension-task-item": "^2.0.3", "@tiptap/extension-task-list": "^2.0.3", "@tiptap/extension-underline": "^2.0.3", "@tiptap/starter-kit": "^2.0.3", "@tiptap/vue-3": "^2.0.3", "@types/jest": "^28.1.7", "jest-serializer-html": "^7.1.0", "jsdom": "^22.1.0", "terser": "^5.24.0", "update-by-scope": "^1.1.3", "vite": "^4.5.0", "vitest": "^0.34.6"}}