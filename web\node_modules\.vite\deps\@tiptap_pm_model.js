import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import "./chunk-AL46U7Q7.js";
import {
  ContentMatch,
  DOMParser,
  DOMSerializer,
  Fragment,
  Mark,
  MarkType,
  Node,
  NodeRange,
  NodeType,
  ReplaceError,
  Resolved<PERSON>os,
  Schema,
  Slice
} from "./chunk-O7RF7KNN.js";
import "./chunk-ZMSOBIYE.js";
export {
  ContentMatch,
  DOMParser,
  DOMSerializer,
  Fragment,
  Mark,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>de<PERSON><PERSON><PERSON>,
  <PERSON>deType,
  ReplaceError,
  <PERSON>sol<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
};
