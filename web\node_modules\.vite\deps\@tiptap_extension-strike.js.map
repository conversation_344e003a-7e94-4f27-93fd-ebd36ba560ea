{"version": 3, "sources": ["../../@tiptap/extension-strike/src/strike.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface StrikeOptions {\n  /**\n   * HTML attributes to add to the strike element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    strike: {\n      /**\n       * Set a strike mark\n       * @example editor.commands.setStrike()\n       */\n      setStrike: () => ReturnType,\n      /**\n       * Toggle a strike mark\n       * @example editor.commands.toggleStrike()\n       */\n      toggleStrike: () => ReturnType,\n      /**\n       * Unset a strike mark\n       * @example editor.commands.unsetStrike()\n       */\n      unsetStrike: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a strike to a ~~strike~~ on input.\n */\nexport const inputRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))$/\n\n/**\n * Matches a strike to a ~~strike~~ on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))/g\n\n/**\n * This extension allows you to create strike text.\n * @see https://www.tiptap.dev/api/marks/strike\n */\nexport const Strike = Mark.create<StrikeOptions>({\n  name: 'strike',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 's',\n      },\n      {\n        tag: 'del',\n      },\n      {\n        tag: 'strike',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('line-through') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setStrike: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleStrike: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetStrike: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-s': () => this.editor.commands.toggleStrike(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCO,IAAM,aAAa;AAKnB,IAAM,aAAa;AAMb,IAAA,SAAS,KAAK,OAAsB;EAC/C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,KAAK;MACN;MACD;QACE,KAAK;MACN;MACD;QACE,OAAO;QACP,WAAW;QACX,UAAU,WAAW,MAAiB,SAAS,cAAc,IAAI,CAAA,IAAK;MACvE;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG9E,cAAW;AACT,WAAO;MACL,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,cAAc,MAAM,CAAC,EAAE,SAAQ,MAAM;AACnC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,aAAa,MAAM,CAAC,EAAE,SAAQ,MAAM;AAClC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,aAAY;;;EAI1D,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;", "names": []}