import { Extension } from '@tiptap/core'

/**
 * 列表对齐扩展
 * 用于增强列表创建时的属性继承功能，确保列表能够继承当前段落的对齐属性
 */
export const ListAlignment = Extension.create({
  name: 'listAlignment',

  addCommands() {
    return {
      /**
       * 创建有序列表并继承当前段落的对齐属性
       */
      toggleOrderedListWithAlignment: () => ({ commands, chain, state, editor }) => {
        // 获取当前段落的对齐属性
        const currentAttributes = this.getCurrentParagraphAttributes(state)
        
        // 调用原始的 toggleOrderedList 命令
        const result = commands.toggleOrderedList()
        
        // 如果成功创建了列表且有对齐属性，则应用对齐
        if (result && currentAttributes.textAlign) {
          return chain()
            .setTextAlign(currentAttributes.textAlign)
            .run()
        }
        
        return result
      },

      /**
       * 创建无序列表并继承当前段落的对齐属性
       */
      toggleBulletListWithAlignment: () => ({ commands, chain, state, editor }) => {
        // 获取当前段落的对齐属性
        const currentAttributes = this.getCurrentParagraphAttributes(state)
        
        // 调用原始的 toggleBulletList 命令
        const result = commands.toggleBulletList()
        
        // 如果成功创建了列表且有对齐属性，则应用对齐
        if (result && currentAttributes.textAlign) {
          return chain()
            .setTextAlign(currentAttributes.textAlign)
            .run()
        }
        
        return result
      },

      /**
       * 创建任务列表并继承当前段落的对齐属性
       */
      toggleTaskListWithAlignment: () => ({ commands, chain, state, editor }) => {
        // 获取当前段落的对齐属性
        const currentAttributes = this.getCurrentParagraphAttributes(state)
        
        // 调用原始的 toggleTaskList 命令
        const result = commands.toggleTaskList()
        
        // 如果成功创建了列表且有对齐属性，则应用对齐
        if (result && currentAttributes.textAlign) {
          return chain()
            .setTextAlign(currentAttributes.textAlign)
            .run()
        }
        
        return result
      },
    }
  },

  /**
   * 获取当前段落的属性
   */
  getCurrentParagraphAttributes(state: any) {
    const { selection } = state
    const { $from } = selection
    
    let attributes: any = {}
    
    // 尝试从当前节点获取属性
    const currentNode = $from.node($from.depth)
    if (currentNode && currentNode.attrs) {
      if (currentNode.attrs.textAlign) {
        attributes.textAlign = currentNode.attrs.textAlign
      }
    }
    
    // 如果当前节点没有对齐属性，尝试从父节点获取
    if (!attributes.textAlign && $from.depth > 0) {
      for (let depth = $from.depth - 1; depth >= 0; depth--) {
        const parentNode = $from.node(depth)
        if (parentNode && parentNode.attrs && parentNode.attrs.textAlign) {
          attributes.textAlign = parentNode.attrs.textAlign
          break
        }
      }
    }
    
    return attributes
  },

  addKeyboardShortcuts() {
    return {
      // 重写默认的快捷键，使用带对齐的版本
      'Mod-Shift-8': () => this.editor.commands.toggleBulletListWithAlignment(),
      'Mod-Shift-7': () => this.editor.commands.toggleOrderedListWithAlignment(),
      'Mod-Shift-9': () => this.editor.commands.toggleTaskListWithAlignment(),
    }
  },
})

export default ListAlignment
