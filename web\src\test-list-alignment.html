<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列表对齐测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .test-title {
            color: #2d8cf0;
            margin-bottom: 1rem;
        }
        
        /* 模拟 ProseMirror 样式 */
        .ProseMirror {
            outline: none;
            padding: 1rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            min-height: 200px;
            background: white;
        }
        
        /* 应用我们修改的列表样式 */
        .ProseMirror ul,
        .ProseMirror ol {
            margin: 0.5rem;
        }

        .ProseMirror ul li,
        .ProseMirror ol li {
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .ProseMirror ul li p,
        .ProseMirror ol li p {
            display: inline;
            margin: 0;
        }

        /* 无序列表样式 */
        .ProseMirror ul:not([data-type='taskList']) {
            padding-left: 1rem;
            padding-right: 0;
            list-style-position: outside;
            list-style-type: disc;
        }

        .ProseMirror ul[style*="text-align: left"] {
            padding-left: 1rem;
            padding-right: 0;
            list-style-position: outside;
            text-align: left;
        }

        .ProseMirror ul[style*="text-align: center"] {
            padding-left: 0;
            padding-right: 0;
            text-align: center;
            list-style: none;
        }

        .ProseMirror ul[style*="text-align: center"] li {
            text-align: center;
            position: relative;
            padding-left: 1.2em;
        }

        .ProseMirror ul[style*="text-align: center"] li::before {
            content: '•';
            position: absolute;
            left: 0;
            width: 1.2em;
            text-align: center;
            color: inherit;
        }

        .ProseMirror ul[style*="text-align: right"] {
            padding-left: 0;
            padding-right: 1rem;
            text-align: right;
            list-style: none;
        }

        .ProseMirror ul[style*="text-align: right"] li {
            text-align: right;
            position: relative;
            padding-right: 1.2em;
        }

        .ProseMirror ul[style*="text-align: right"] li::after {
            content: '•';
            position: absolute;
            right: 0;
            width: 1.2em;
            text-align: center;
            color: inherit;
        }

        /* 有序列表样式 */
        .ProseMirror ol {
            padding-left: 1.25rem;
            padding-right: 0;
            list-style-position: outside;
            list-style-type: decimal;
            counter-reset: list-counter;
        }

        .ProseMirror ol[style*="text-align: left"] {
            padding-left: 1.25rem;
            padding-right: 0;
            list-style-position: outside;
            text-align: left;
        }

        .ProseMirror ol[style*="text-align: center"] {
            padding-left: 0;
            padding-right: 0;
            text-align: center;
            list-style: none;
            counter-reset: list-counter;
        }

        .ProseMirror ol[style*="text-align: center"] li {
            text-align: center;
            position: relative;
            padding-left: 2em;
            counter-increment: list-counter;
        }

        .ProseMirror ol[style*="text-align: center"] li::before {
            content: counter(list-counter) '.';
            position: absolute;
            left: 0;
            width: 2em;
            text-align: center;
            color: inherit;
            font-weight: inherit;
        }

        .ProseMirror ol[style*="text-align: right"] {
            padding-left: 0;
            padding-right: 1.25rem;
            text-align: right;
            list-style: none;
            counter-reset: list-counter;
        }

        .ProseMirror ol[style*="text-align: right"] li {
            text-align: right;
            position: relative;
            padding-right: 2em;
            counter-increment: list-counter;
        }

        .ProseMirror ol[style*="text-align: right"] li::after {
            content: '.' counter(list-counter);
            position: absolute;
            right: 0;
            width: 2em;
            text-align: center;
            color: inherit;
            font-weight: inherit;
        }
        
        .controls {
            margin: 1rem 0;
        }
        
        .controls button {
            margin: 0 0.5rem 0.5rem 0;
            padding: 0.5rem 1rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #f0f0f0;
        }
        
        .controls button.active {
            background: #2d8cf0;
            color: white;
            border-color: #2d8cf0;
        }
    </style>
</head>
<body>
    <h1>Tiptap 列表对齐测试页面</h1>
    <p>这个页面用于测试列表对齐功能的 CSS 样式效果</p>
    
    <div class="test-section">
        <h2 class="test-title">测试说明</h2>
        <p>点击下面的对齐按钮来测试不同对齐方式下列表的显示效果：</p>
        
        <div class="controls">
            <button onclick="setAlignment('left')" id="btn-left" class="active">左对齐</button>
            <button onclick="setAlignment('center')" id="btn-center">居中对齐</button>
            <button onclick="setAlignment('right')" id="btn-right">右对齐</button>
            <button onclick="setAlignment('justify')" id="btn-justify">两端对齐</button>
        </div>
        
        <div class="ProseMirror" contenteditable="true">
            <h3>有序列表测试</h3>
            <ol id="ordered-list">
                <li>第一项内容 - 这是一个较长的文本用于测试对齐效果，观察是否会出现强制换行</li>
                <li>第二项内容 - 观察序号位置是否正确</li>
                <li>第三项内容 - 检查对齐是否正确，内容应该保持在同一行</li>
                <li>第四项 - 短内容测试</li>
                <li>第五项内容 - 包含<strong>粗体</strong>和<em>斜体</em>的混合格式文本测试</li>
            </ol>

            <h3>无序列表测试</h3>
            <ul id="unordered-list">
                <li>第一项内容 - 这是一个较长的文本用于测试对齐效果，观察是否会出现强制换行</li>
                <li>第二项内容 - 观察符号位置是否正确</li>
                <li>第三项内容 - 检查对齐是否正确，内容应该保持在同一行</li>
                <li>第四项 - 短内容测试</li>
                <li>第五项内容 - 包含<strong>粗体</strong>和<em>斜体</em>的混合格式文本测试</li>
            </ul>

            <h3>嵌套列表测试</h3>
            <ol id="nested-list">
                <li>第一级项目
                    <ol>
                        <li>第二级项目 A</li>
                        <li>第二级项目 B</li>
                    </ol>
                </li>
                <li>第一级项目 2</li>
            </ol>

            <p><strong>测试说明：</strong>点击上面的对齐按钮测试不同的对齐效果。重点观察：</p>
            <ul>
                <li>列表序号/符号是否跟随文本对齐</li>
                <li>长文本是否会出现不正常的强制换行</li>
                <li>切换对齐方式时是否有样式冲突</li>
                <li>嵌套列表的对齐是否正确</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">预期效果</h2>
        <ul>
            <li><strong>左对齐（默认）</strong>：序号/符号在左侧，内容左对齐</li>
            <li><strong>居中对齐</strong>：序号/符号和内容都居中显示</li>
            <li><strong>右对齐</strong>：序号/符号和内容都右对齐显示</li>
            <li><strong>两端对齐</strong>：序号/符号在左侧，内容两端对齐</li>
        </ul>
    </div>

    <script>
        function setAlignment(alignment) {
            const orderedList = document.getElementById('ordered-list');
            const unorderedList = document.getElementById('unordered-list');
            const nestedList = document.getElementById('nested-list');

            // 清除所有按钮的活动状态
            document.querySelectorAll('.controls button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 设置当前按钮为活动状态
            document.getElementById(`btn-${alignment}`).classList.add('active');

            // 应用对齐样式
            const style = `text-align: ${alignment}`;
            orderedList.setAttribute('style', style);
            unorderedList.setAttribute('style', style);
            nestedList.setAttribute('style', style);

            // 输出当前状态到控制台，便于调试
            console.log(`Applied alignment: ${alignment}`);
            console.log('Ordered list style:', orderedList.getAttribute('style'));
            console.log('Unordered list style:', unorderedList.getAttribute('style'));
            console.log('Nested list style:', nestedList.getAttribute('style'));
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('List alignment test page loaded');
            console.log('Available test lists:', {
                ordered: document.getElementById('ordered-list'),
                unordered: document.getElementById('unordered-list'),
                nested: document.getElementById('nested-list')
            });
        });
    </script>
</body>
</html>
