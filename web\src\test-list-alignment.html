<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列表对齐测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .test-title {
            color: #2d8cf0;
            margin-bottom: 1rem;
        }
        
        /* 模拟 ProseMirror 样式 */
        .ProseMirror {
            outline: none;
            padding: 1rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            min-height: 200px;
            background: white;
        }
        
        /* 应用我们修改的列表样式 */
        .ProseMirror ul,
        .ProseMirror ol {
            margin: 0.5rem;
        }
        
        .ProseMirror ul {
            padding: 0 1rem;
        }
        
        .ProseMirror ul[style*="text-align: left"],
        .ProseMirror ul:not([style*="text-align"]) {
            padding-left: 1rem;
            padding-right: 0;
        }
        
        .ProseMirror ul[style*="text-align: center"] {
            padding-left: 0;
            padding-right: 0;
            list-style-position: inside;
            text-align: center;
        }
        
        .ProseMirror ul[style*="text-align: right"] {
            padding-left: 0;
            padding-right: 1rem;
            list-style-position: inside;
            text-align: right;
        }
        
        .ProseMirror ol {
            padding-left: 1.25rem;
            list-style-position: outside;
            list-style-type: decimal;
        }
        
        .ProseMirror ol[style*="text-align: left"],
        .ProseMirror ol:not([style*="text-align"]) {
            padding-left: 1.25rem;
            padding-right: 0;
            list-style-position: outside;
        }
        
        .ProseMirror ol[style*="text-align: center"] {
            padding-left: 0;
            padding-right: 0;
            list-style-position: inside;
            text-align: center;
        }
        
        .ProseMirror ol[style*="text-align: right"] {
            padding-left: 0;
            padding-right: 1.25rem;
            list-style-position: inside;
            text-align: right;
        }
        
        .ProseMirror ul[style*="text-align: center"] li,
        .ProseMirror ol[style*="text-align: center"] li {
            text-align: center;
        }
        
        .ProseMirror ul[style*="text-align: right"] li,
        .ProseMirror ol[style*="text-align: right"] li {
            text-align: right;
        }
        
        .controls {
            margin: 1rem 0;
        }
        
        .controls button {
            margin: 0 0.5rem 0.5rem 0;
            padding: 0.5rem 1rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #f0f0f0;
        }
        
        .controls button.active {
            background: #2d8cf0;
            color: white;
            border-color: #2d8cf0;
        }
    </style>
</head>
<body>
    <h1>Tiptap 列表对齐测试页面</h1>
    <p>这个页面用于测试列表对齐功能的 CSS 样式效果</p>
    
    <div class="test-section">
        <h2 class="test-title">测试说明</h2>
        <p>点击下面的对齐按钮来测试不同对齐方式下列表的显示效果：</p>
        
        <div class="controls">
            <button onclick="setAlignment('left')" id="btn-left" class="active">左对齐</button>
            <button onclick="setAlignment('center')" id="btn-center">居中对齐</button>
            <button onclick="setAlignment('right')" id="btn-right">右对齐</button>
            <button onclick="setAlignment('justify')" id="btn-justify">两端对齐</button>
        </div>
        
        <div class="ProseMirror" contenteditable="true">
            <h3>有序列表测试</h3>
            <ol id="ordered-list">
                <li>第一项内容 - 这是一个较长的文本用于测试对齐效果</li>
                <li>第二项内容 - 观察序号位置</li>
                <li>第三项内容 - 检查对齐是否正确</li>
            </ol>
            
            <h3>无序列表测试</h3>
            <ul id="unordered-list">
                <li>第一项内容 - 这是一个较长的文本用于测试对齐效果</li>
                <li>第二项内容 - 观察符号位置</li>
                <li>第三项内容 - 检查对齐是否正确</li>
            </ul>
            
            <p>请点击上面的对齐按钮测试不同的对齐效果。观察列表序号/符号是否跟随文本对齐。</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">预期效果</h2>
        <ul>
            <li><strong>左对齐（默认）</strong>：序号/符号在左侧，内容左对齐</li>
            <li><strong>居中对齐</strong>：序号/符号和内容都居中显示</li>
            <li><strong>右对齐</strong>：序号/符号和内容都右对齐显示</li>
            <li><strong>两端对齐</strong>：序号/符号在左侧，内容两端对齐</li>
        </ul>
    </div>

    <script>
        function setAlignment(alignment) {
            const orderedList = document.getElementById('ordered-list');
            const unorderedList = document.getElementById('unordered-list');
            
            // 清除所有按钮的活动状态
            document.querySelectorAll('.controls button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 设置当前按钮为活动状态
            document.getElementById(`btn-${alignment}`).classList.add('active');
            
            // 应用对齐样式
            const style = `text-align: ${alignment}`;
            orderedList.setAttribute('style', style);
            unorderedList.setAttribute('style', style);
        }
    </script>
</body>
</html>
