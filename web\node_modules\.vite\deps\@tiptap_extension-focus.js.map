{"version": 3, "sources": ["../../@tiptap/extension-focus/src/focus.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface FocusOptions {\n  /**\n   * The class name that should be added to the focused node.\n   * @default 'has-focus'\n   * @example 'is-focused'\n   */\n  className: string\n\n  /**\n   * The mode by which the focused node is determined.\n   * - All: All nodes are marked as focused.\n   * - Deepest: Only the deepest node is marked as focused.\n   * - Shallowest: Only the shallowest node is marked as focused.\n   *\n   * @default 'all'\n   * @example 'deepest'\n   * @example 'shallowest'\n   */\n  mode: 'all' | 'deepest' | 'shallowest'\n}\n\n/**\n * This extension allows you to add a class to the focused node.\n * @see https://www.tiptap.dev/api/extensions/focus\n */\nexport const FocusClasses = Extension.create<FocusOptions>({\n  name: 'focus',\n\n  addOptions() {\n    return {\n      className: 'has-focus',\n      mode: 'all',\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('focus'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const { isEditable, isFocused } = this.editor\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!isEditable || !isFocused) {\n              return DecorationSet.create(doc, [])\n            }\n\n            // Maximum Levels\n            let maxLevels = 0\n\n            if (this.options.mode === 'deepest') {\n              doc.descendants((node, pos) => {\n                if (node.isText) {\n                  return\n                }\n\n                const isCurrent = anchor >= pos && anchor <= pos + node.nodeSize - 1\n\n                if (!isCurrent) {\n                  return false\n                }\n\n                maxLevels += 1\n              })\n            }\n\n            // Loop through current\n            let currentLevel = 0\n\n            doc.descendants((node, pos) => {\n              if (node.isText) {\n                return false\n              }\n\n              const isCurrent = anchor >= pos && anchor <= pos + node.nodeSize - 1\n\n              if (!isCurrent) {\n                return false\n              }\n\n              currentLevel += 1\n\n              const outOfScope = (this.options.mode === 'deepest' && maxLevels - currentLevel > 0)\n                || (this.options.mode === 'shallowest' && currentLevel > 1)\n\n              if (outOfScope) {\n                return this.options.mode === 'deepest'\n              }\n\n              decorations.push(\n                Decoration.node(pos, pos + node.nodeSize, {\n                  class: this.options.className,\n                }),\n              )\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6Ba,IAAA,eAAe,UAAU,OAAqB;EACzD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,WAAW;MACX,MAAM;;;EAIV,wBAAqB;AACnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,OAAO;QAC1B,OAAO;UACL,aAAa,CAAC,EAAE,KAAK,UAAS,MAAM;AAClC,kBAAM,EAAE,YAAY,UAAS,IAAK,KAAK;AACvC,kBAAM,EAAE,OAAM,IAAK;AACnB,kBAAM,cAA4B,CAAA;AAElC,gBAAI,CAAC,cAAc,CAAC,WAAW;AAC7B,qBAAO,cAAc,OAAO,KAAK,CAAA,CAAE;;AAIrC,gBAAI,YAAY;AAEhB,gBAAI,KAAK,QAAQ,SAAS,WAAW;AACnC,kBAAI,YAAY,CAAC,MAAM,QAAO;AAC5B,oBAAI,KAAK,QAAQ;AACf;;AAGF,sBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK,WAAW;AAEnE,oBAAI,CAAC,WAAW;AACd,yBAAO;;AAGT,6BAAa;cACf,CAAC;;AAIH,gBAAI,eAAe;AAEnB,gBAAI,YAAY,CAAC,MAAM,QAAO;AAC5B,kBAAI,KAAK,QAAQ;AACf,uBAAO;;AAGT,oBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK,WAAW;AAEnE,kBAAI,CAAC,WAAW;AACd,uBAAO;;AAGT,8BAAgB;AAEhB,oBAAM,aAAc,KAAK,QAAQ,SAAS,aAAa,YAAY,eAAe,KAC5E,KAAK,QAAQ,SAAS,gBAAgB,eAAe;AAE3D,kBAAI,YAAY;AACd,uBAAO,KAAK,QAAQ,SAAS;;AAG/B,0BAAY,KACV,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU;gBACxC,OAAO,KAAK,QAAQ;cACrB,CAAA,CAAC;YAEN,CAAC;AAED,mBAAO,cAAc,OAAO,KAAK,WAAW;;QAE/C;OACF;;;AAGN,CAAA;", "names": []}