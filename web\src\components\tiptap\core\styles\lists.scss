// 列表样式
.ProseMirror {
  // 普通列表样式
  ul,
  ol {
    margin: 0.5rem;

    li {
      transition: background-color 0.2s ease;

      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        border-radius: 4px;
      }

      p {
        margin-top: 0.15em;
        margin-bottom: 0.15em;
        position: relative;
      }
    }

    // 列表项对齐样式继承
    &[style*="text-align: center"] li {
      text-align: center;
    }

    &[style*="text-align: right"] li {
      text-align: right;
    }

    &[style*="text-align: justify"] li {
      text-align: justify;
    }
  }

  // 无序列表样式
  ul {
    padding: 0 1rem;

    // 左对齐（默认）
    &[style*="text-align: left"],
    &:not([style*="text-align"]) {
      padding-left: 1rem;
      padding-right: 0;
    }

    // 居中对齐
    &[style*="text-align: center"] {
      padding-left: 0;
      padding-right: 0;
      list-style-position: inside;
      text-align: center;
    }

    // 右对齐
    &[style*="text-align: right"] {
      padding-left: 0;
      padding-right: 1rem;
      list-style-position: inside;
      text-align: right;
    }

    // 两端对齐
    &[style*="text-align: justify"] {
      padding-left: 1rem;
      padding-right: 0;
      text-align: justify;
    }
  }

  // 有序列表样式 - 修复序号被遮挡问题并支持对齐
  ol {
    padding-left: 1.25rem; // 嵌套列表保持相同的左边距
    list-style-position: outside; // 确保序号显示在内容区域外
    list-style-type: decimal; // 明确指定序号类型

    // 左对齐（默认）
    &[style*="text-align: left"],
    &:not([style*="text-align"]) {
      padding-left: 1.25rem;
      padding-right: 0;
      list-style-position: outside;
    }

    // 居中对齐
    &[style*="text-align: center"] {
      padding-left: 0;
      padding-right: 0;
      list-style-position: inside;
      text-align: center;
    }

    // 右对齐
    &[style*="text-align: right"] {
      padding-left: 0;
      padding-right: 1.25rem;
      list-style-position: inside;
      text-align: right;
    }

    // 两端对齐
    &[style*="text-align: justify"] {
      padding-left: 1.25rem;
      padding-right: 0;
      list-style-position: outside;
      text-align: justify;
    }

    // 确保嵌套列表也有正确的间距
    ol {
      padding-left: 1.25rem; // 嵌套列表保持相同的左边距
    }
  }

  // 任务列表样式
  ul[data-type='taskList'] {
    list-style: none;
    margin-left: 0;
    padding: 0;

    // 任务列表对齐样式
    &[style*="text-align: center"] {
      text-align: center;

      li {
        justify-content: center;
      }
    }

    &[style*="text-align: right"] {
      text-align: right;

      li {
        justify-content: flex-end;
      }
    }

    &[style*="text-align: justify"] {
      text-align: justify;
    }

    li {
      align-items: flex-start;
      display: flex;
      position: relative;
      padding: 2px 4px;
      border-radius: 4px;
      transition:
        background-color 0.2s ease,
        box-shadow 0.2s ease;

      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        box-shadow: 0 0 0 1px rgba(90, 214, 150, 20%);
      }

      > label {
        flex: 0 0 auto;
        user-select: none;
      }

      > div {
        flex: 1 1 auto;
        position: relative;
      }
    }

    input[type='checkbox'] {
      cursor: pointer;
      position: relative;

      &:checked {
        accent-color: #2d8cf0;
      }
    }

    ul[data-type='taskList'] {
      margin: 0;
    }
  }
}

// 只读模式下的任务列表样式
.editor-readonly {
  .ProseMirrorInput,
  .ProseMirror {
    ul[data-type='taskList'] {
      input[type='checkbox'] {
        pointer-events: none;
        box-shadow: none;
        outline: none;
      }

      // 添加自定义禁用样式
      .cst-task-checkbox-wrapper {
        pointer-events: none;
        cursor: not-allowed;
        transition: none !important; // 禁用所有过渡动画
        transform: none !important; // 禁用任何变换

        &:hover {
          transform: none !important;
        }
      }

      // 禁用标签悬浮效果
      > label {
        transition: none !important;

        &:hover {
          transform: none !important;
        }
      }
    }
  }
}
