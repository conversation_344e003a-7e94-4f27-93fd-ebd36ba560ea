// 列表样式
.ProseMirror {
  // 普通列表样式基础
  ul,
  ol {
    margin: 0.5rem;

    li {
      transition: background-color 0.2s ease;
      // 防止内容强制换行
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;

      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        border-radius: 4px;
      }

      p {
        margin-top: 0.15em;
        margin-bottom: 0.15em;
        position: relative;
        // 确保段落不会导致额外换行
        display: inline;
      }
    }
  }

  // 无序列表样式 - 使用更高优先级的选择器避免冲突
  ul:not([data-type='taskList']) {
    // 默认样式（左对齐或无对齐属性）
    padding-left: 1rem;
    padding-right: 0;
    list-style-position: outside;
    list-style-type: disc;

    // 左对齐（显式设置）
    &[style*="text-align: left"] {
      padding-left: 1rem;
      padding-right: 0;
      list-style-position: outside;
      text-align: left;

      li {
        text-align: left;
      }
    }

    // 居中对齐 - 使用 flexbox 避免换行问题
    &[style*="text-align: center"] {
      padding-left: 0;
      padding-right: 0;
      text-align: center;
      list-style: none; // 移除默认样式

      li {
        text-align: center;
        position: relative;
        padding-left: 1.2em;

        // 使用伪元素创建居中的项目符号
        &::before {
          content: '•';
          position: absolute;
          left: 0;
          width: 1.2em;
          text-align: center;
          color: inherit;
        }
      }
    }

    // 右对齐 - 使用 flexbox 避免换行问题
    &[style*="text-align: right"] {
      padding-left: 0;
      padding-right: 1rem;
      text-align: right;
      list-style: none; // 移除默认样式

      li {
        text-align: right;
        position: relative;
        padding-right: 1.2em;

        // 使用伪元素创建右对齐的项目符号
        &::after {
          content: '•';
          position: absolute;
          right: 0;
          width: 1.2em;
          text-align: center;
          color: inherit;
        }
      }
    }

    // 两端对齐
    &[style*="text-align: justify"] {
      padding-left: 1rem;
      padding-right: 0;
      list-style-position: outside;
      text-align: justify;

      li {
        text-align: justify;
      }
    }
  }

  // 有序列表样式 - 修复序号被遮挡问题并支持对齐
  ol {
    // 默认样式（左对齐或无对齐属性）
    padding-left: 1.25rem;
    padding-right: 0;
    list-style-position: outside;
    list-style-type: decimal;
    counter-reset: list-counter;

    // 左对齐（显式设置）
    &[style*="text-align: left"] {
      padding-left: 1.25rem;
      padding-right: 0;
      list-style-position: outside;
      text-align: left;

      li {
        text-align: left;
      }
    }

    // 居中对齐 - 使用自定义计数器避免换行
    &[style*="text-align: center"] {
      padding-left: 0;
      padding-right: 0;
      text-align: center;
      list-style: none;
      counter-reset: list-counter;

      li {
        text-align: center;
        position: relative;
        padding-left: 2em;
        counter-increment: list-counter;

        // 使用伪元素创建居中的序号
        &::before {
          content: counter(list-counter) '.';
          position: absolute;
          left: 0;
          width: 2em;
          text-align: center;
          color: inherit;
          font-weight: inherit;
        }
      }
    }

    // 右对齐 - 使用自定义计数器避免换行
    &[style*="text-align: right"] {
      padding-left: 0;
      padding-right: 1.25rem;
      text-align: right;
      list-style: none;
      counter-reset: list-counter;

      li {
        text-align: right;
        position: relative;
        padding-right: 2em;
        counter-increment: list-counter;

        // 使用伪元素创建右对齐的序号
        &::after {
          content: '.' counter(list-counter);
          position: absolute;
          right: 0;
          width: 2em;
          text-align: center;
          color: inherit;
          font-weight: inherit;
        }
      }
    }

    // 两端对齐
    &[style*="text-align: justify"] {
      padding-left: 1.25rem;
      padding-right: 0;
      list-style-position: outside;
      text-align: justify;

      li {
        text-align: justify;
      }
    }

    // 确保嵌套列表也有正确的间距
    ol {
      margin-top: 0.25rem;
      margin-bottom: 0.25rem;

      // 嵌套列表继承父级对齐方式
      &:not([style*="text-align"]) {
        padding-left: 1.25rem;
      }
    }
  }

  // 任务列表样式
  ul[data-type='taskList'] {
    list-style: none;
    margin-left: 0;
    padding: 0;

    li {
      align-items: flex-start;
      display: flex;
      position: relative;
      padding: 2px 4px;
      border-radius: 4px;
      transition:
        background-color 0.2s ease,
        box-shadow 0.2s ease;
      // 防止内容强制换行
      white-space: normal;
      word-wrap: break-word;

      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        box-shadow: 0 0 0 1px rgba(90, 214, 150, 20%);
      }

      > label {
        flex: 0 0 auto;
        user-select: none;
      }

      > div {
        flex: 1 1 auto;
        position: relative;
        min-width: 0; // 防止 flex 子元素溢出
      }
    }

    // 左对齐（默认）
    &[style*="text-align: left"],
    &:not([style*="text-align"]) {
      li {
        justify-content: flex-start;
        text-align: left;

        > div {
          text-align: left;
        }
      }
    }

    // 居中对齐
    &[style*="text-align: center"] {
      li {
        justify-content: center;
        text-align: center;

        > div {
          text-align: center;
        }
      }
    }

    // 右对齐
    &[style*="text-align: right"] {
      li {
        justify-content: flex-end;
        text-align: right;

        > div {
          text-align: right;
        }
      }
    }

    // 两端对齐
    &[style*="text-align: justify"] {
      li {
        justify-content: flex-start;
        text-align: justify;

        > div {
          text-align: justify;
        }
      }
    }

    input[type='checkbox'] {
      cursor: pointer;
      position: relative;

      &:checked {
        accent-color: #2d8cf0;
      }
    }

    ul[data-type='taskList'] {
      margin: 0;
    }
  }
}

// 只读模式下的任务列表样式
.editor-readonly {
  .ProseMirrorInput,
  .ProseMirror {
    ul[data-type='taskList'] {
      input[type='checkbox'] {
        pointer-events: none;
        box-shadow: none;
        outline: none;
      }

      // 添加自定义禁用样式
      .cst-task-checkbox-wrapper {
        pointer-events: none;
        cursor: not-allowed;
        transition: none !important; // 禁用所有过渡动画
        transform: none !important; // 禁用任何变换

        &:hover {
          transform: none !important;
        }
      }

      // 禁用标签悬浮效果
      > label {
        transition: none !important;

        &:hover {
          transform: none !important;
        }
      }
    }
  }
}
