/**
 * @popperjs/core v2.11.8 - MIT License
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Popper={})}(this,(function(t){"use strict";function e(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function n(t){return t instanceof e(t).Element||t instanceof Element}function o(t){return t instanceof e(t).HTMLElement||t instanceof HTMLElement}function r(t){return"undefined"!=typeof ShadowRoot&&(t instanceof e(t).ShadowRoot||t instanceof ShadowRoot)}var i=Math.max,f=Math.min,a=Math.round;function c(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function u(){return!/^((?!chrome|android).)*safari/i.test(c())}function s(t,r,i){void 0===r&&(r=!1),void 0===i&&(i=!1);var f=t.getBoundingClientRect(),c=1,s=1;r&&o(t)&&(c=t.offsetWidth>0&&a(f.width)/t.offsetWidth||1,s=t.offsetHeight>0&&a(f.height)/t.offsetHeight||1);var d=(n(t)?e(t):window).visualViewport,l=!u()&&i,h=(f.left+(l&&d?d.offsetLeft:0))/c,p=(f.top+(l&&d?d.offsetTop:0))/s,m=f.width/c,g=f.height/s;return{width:m,height:g,top:p,right:h+m,bottom:p+g,left:h,x:h,y:p}}function d(t){var n=e(t);return{scrollLeft:n.pageXOffset,scrollTop:n.pageYOffset}}function l(t){return t?(t.nodeName||"").toLowerCase():null}function h(t){return((n(t)?t.ownerDocument:t.document)||window.document).documentElement}function p(t){return s(h(t)).left+d(t).scrollLeft}function m(t){return e(t).getComputedStyle(t)}function g(t){var e=m(t),n=e.overflow,o=e.overflowX,r=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+o)}function v(t,n,r){void 0===r&&(r=!1);var i,f,c=o(n),u=o(n)&&function(t){var e=t.getBoundingClientRect(),n=a(e.width)/t.offsetWidth||1,o=a(e.height)/t.offsetHeight||1;return 1!==n||1!==o}(n),m=h(n),v=s(t,u,r),b={scrollLeft:0,scrollTop:0},y={x:0,y:0};return(c||!c&&!r)&&(("body"!==l(n)||g(m))&&(b=(i=n)!==e(i)&&o(i)?{scrollLeft:(f=i).scrollLeft,scrollTop:f.scrollTop}:d(i)),o(n)?((y=s(n,!0)).x+=n.clientLeft,y.y+=n.clientTop):m&&(y.x=p(m))),{x:v.left+b.scrollLeft-y.x,y:v.top+b.scrollTop-y.y,width:v.width,height:v.height}}function b(t){return"html"===l(t)?t:t.assignedSlot||t.parentNode||(r(t)?t.host:null)||h(t)}function y(t){return["html","body","#document"].indexOf(l(t))>=0?t.ownerDocument.body:o(t)&&g(t)?t:y(b(t))}function w(t,n){var o;void 0===n&&(n=[]);var r=y(t),i=r===(null==(o=t.ownerDocument)?void 0:o.body),f=e(r),a=i?[f].concat(f.visualViewport||[],g(r)?r:[]):r,c=n.concat(a);return i?c:c.concat(w(b(a)))}function x(t){return["table","td","th"].indexOf(l(t))>=0}function O(t){return o(t)&&"fixed"!==m(t).position?t.offsetParent:null}function M(t){for(var n=e(t),i=O(t);i&&x(i)&&"static"===m(i).position;)i=O(i);return i&&("html"===l(i)||"body"===l(i)&&"static"===m(i).position)?n:i||function(t){var e=/firefox/i.test(c());if(/Trident/i.test(c())&&o(t)&&"fixed"===m(t).position)return null;var n=b(t);for(r(n)&&(n=n.host);o(n)&&["html","body"].indexOf(l(n))<0;){var i=m(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||e&&"filter"===i.willChange||e&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(t)||n}var E="top",T="bottom",W="right",j="left",L=[E,T,W,j],H="viewport",P="popper",R=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function D(t){var e=new Map,n=new Set,o=[];function r(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!n.has(t)){var o=e.get(t);o&&r(o)}})),o.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){n.has(t.name)||r(t)})),o}function C(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function S(t,o,r){return o===H?C(function(t,n){var o=e(t),r=h(t),i=o.visualViewport,f=r.clientWidth,a=r.clientHeight,c=0,s=0;if(i){f=i.width,a=i.height;var d=u();(d||!d&&"fixed"===n)&&(c=i.offsetLeft,s=i.offsetTop)}return{width:f,height:a,x:c+p(t),y:s}}(t,r)):n(o)?function(t,e){var n=s(t,!1,"fixed"===e);return n.top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n}(o,r):C(function(t){var e,n=h(t),o=d(t),r=null==(e=t.ownerDocument)?void 0:e.body,f=i(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),a=i(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),c=-o.scrollLeft+p(t),u=-o.scrollTop;return"rtl"===m(r||n).direction&&(c+=i(n.clientWidth,r?r.clientWidth:0)-f),{width:f,height:a,x:c,y:u}}(h(t)))}function k(t){var e=w(b(t)),i=["absolute","fixed"].indexOf(m(t).position)>=0&&o(t)?M(t):t;return n(i)?e.filter((function(t){return n(t)&&function(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&r(n)){var o=e;do{if(o&&t.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}(t,i)&&"body"!==l(t)})):[]}var N={placement:"bottom",modifiers:[],strategy:"absolute"};function A(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function B(t){void 0===t&&(t={});var e=t,o=e.defaultModifiers,r=void 0===o?[]:o,i=e.defaultOptions,f=void 0===i?N:i;return function(t,e,o){void 0===o&&(o=f);var i,a,c={placement:"bottom",orderedModifiers:[],options:Object.assign({},N,f),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},u=[],d=!1,l={state:c,setOptions:function(o){var i="function"==typeof o?o(c.options):o;h(),c.options=Object.assign({},f,c.options,i),c.scrollParents={reference:n(t)?w(t):t.contextElement?w(t.contextElement):[],popper:w(e)};var a,s,d=function(t){var e=D(t);return R.reduce((function(t,n){return t.concat(e.filter((function(t){return t.phase===n})))}),[])}((a=[].concat(r,c.options.modifiers),s=a.reduce((function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t}),{}),Object.keys(s).map((function(t){return s[t]}))));return c.orderedModifiers=d.filter((function(t){return t.enabled})),c.orderedModifiers.forEach((function(t){var e=t.name,n=t.options,o=void 0===n?{}:n,r=t.effect;if("function"==typeof r){var i=r({state:c,name:e,instance:l,options:o}),f=function(){};u.push(i||f)}})),l.update()},forceUpdate:function(){if(!d){var t=c.elements,e=t.reference,n=t.popper;if(A(e,n)){var o,r,i,f;c.rects={reference:v(e,M(n),"fixed"===c.options.strategy),popper:(o=n,r=s(o),i=o.offsetWidth,f=o.offsetHeight,Math.abs(r.width-i)<=1&&(i=r.width),Math.abs(r.height-f)<=1&&(f=r.height),{x:o.offsetLeft,y:o.offsetTop,width:i,height:f})},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach((function(t){return c.modifiersData[t.name]=Object.assign({},t.data)}));for(var a=0;a<c.orderedModifiers.length;a++)if(!0!==c.reset){var u=c.orderedModifiers[a],h=u.fn,p=u.options,m=void 0===p?{}:p,g=u.name;"function"==typeof h&&(c=h({state:c,options:m,name:g,instance:l})||c)}else c.reset=!1,a=-1}}},update:(i=function(){return new Promise((function(t){l.forceUpdate(),t(c)}))},function(){return a||(a=new Promise((function(t){Promise.resolve().then((function(){a=void 0,t(i())}))}))),a}),destroy:function(){h(),d=!0}};if(!A(t,e))return l;function h(){u.forEach((function(t){return t()})),u=[]}return l.setOptions(o).then((function(t){!d&&o.onFirstUpdate&&o.onFirstUpdate(t)})),l}}var U=B();t.createPopper=U,t.detectOverflow=function(t,e){void 0===e&&(e={});var o,r=e,a=r.placement,c=void 0===a?t.placement:a,u=r.strategy,d=void 0===u?t.strategy:u,l=r.boundary,p=void 0===l?"clippingParents":l,m=r.rootBoundary,g=void 0===m?H:m,v=r.elementContext,b=void 0===v?P:v,y=r.altBoundary,w=void 0!==y&&y,x=r.padding,O=void 0===x?0:x,M=function(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}("number"!=typeof O?O:(o=O,L.reduce((function(t,e){return t[e]=o,t}),{}))),R=b===P?"reference":P,D=t.rects.popper,N=t.elements[w?R:b],A=function(t,e,n,o){var r="clippingParents"===e?k(t):[].concat(e),a=[].concat(r,[n]),c=a[0],u=a.reduce((function(e,n){var r=S(t,n,o);return e.top=i(r.top,e.top),e.right=f(r.right,e.right),e.bottom=f(r.bottom,e.bottom),e.left=i(r.left,e.left),e}),S(t,c,o));return u.width=u.right-u.left,u.height=u.bottom-u.top,u.x=u.left,u.y=u.top,u}(n(N)?N:N.contextElement||h(t.elements.popper),p,g,d),B=s(t.elements.reference),U=function(t){var e,n=t.reference,o=t.element,r=t.placement,i=r?function(t){return t.split("-")[0]}(r):null,f=r?function(t){return t.split("-")[1]}(r):null,a=n.x+n.width/2-o.width/2,c=n.y+n.height/2-o.height/2;switch(i){case E:e={x:a,y:n.y-o.height};break;case T:e={x:a,y:n.y+n.height};break;case W:e={x:n.x+n.width,y:c};break;case j:e={x:n.x-o.width,y:c};break;default:e={x:n.x,y:n.y}}var u=i?function(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}(i):null;if(null!=u){var s="y"===u?"height":"width";switch(f){case"start":e[u]=e[u]-(n[s]/2-o[s]/2);break;case"end":e[u]=e[u]+(n[s]/2-o[s]/2)}}return e}({reference:B,element:D,strategy:"absolute",placement:c}),V=C(Object.assign({},D,U)),q=b===P?V:B,F={top:A.top-q.top+M.top,bottom:q.bottom-A.bottom+M.bottom,left:A.left-q.left+M.left,right:q.right-A.right+M.right},X=t.modifiersData.offset;if(b===P&&X){var Y=X[c];Object.keys(F).forEach((function(t){var e=[W,T].indexOf(t)>=0?1:-1,n=[E,T].indexOf(t)>=0?"y":"x";F[t]+=Y[n]*e}))}return F},t.popperGenerator=B,Object.defineProperty(t,"__esModule",{value:!0})}));
//# sourceMappingURL=popper-base.min.js.map
