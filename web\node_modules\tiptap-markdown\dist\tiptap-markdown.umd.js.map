{"version": 3, "file": "tiptap-markdown.umd.js", "sources": ["../src/extensions/tiptap/tight-lists.js", "../src/util/markdown.js", "../src/serialize/state.js", "../src/extensions/marks/html.js", "../src/util/dom.js", "../src/extensions/nodes/html.js", "../src/extensions/nodes/blockquote.js", "../src/extensions/nodes/bullet-list.js", "../src/extensions/nodes/code-block.js", "../src/extensions/nodes/hard-break.js", "../src/extensions/nodes/heading.js", "../src/extensions/nodes/horizontal-rule.js", "../src/extensions/nodes/image.js", "../src/extensions/nodes/list-item.js", "../src/extensions/nodes/ordered-list.js", "../src/extensions/nodes/paragraph.js", "../src/util/prosemirror.js", "../src/extensions/nodes/table.js", "../src/extensions/nodes/task-item.js", "../src/extensions/nodes/task-list.js", "../src/extensions/nodes/text.js", "../src/extensions/marks/bold.js", "../src/extensions/marks/code.js", "../src/extensions/marks/italic.js", "../src/extensions/marks/link.js", "../src/extensions/marks/strike.js", "../src/extensions/index.js", "../src/util/extensions.js", "../src/serialize/MarkdownSerializer.js", "../src/parse/MarkdownParser.js", "../src/extensions/tiptap/clipboard.js", "../src/Markdown.js"], "sourcesContent": ["import { Extension } from \"@tiptap/core\";\n\nexport const MarkdownTightLists = Extension.create({\n    name: 'markdownTightLists',\n    addOptions: () => ({\n        tight: true,\n        tightClass: 'tight',\n        listTypes: [\n            'bulletList',\n            'orderedList',\n        ],\n    }),\n    addGlobalAttributes() {\n        return [\n            {\n                types: this.options.listTypes,\n                attributes: {\n                    tight: {\n                        default: this.options.tight,\n                        parseHTML: element =>\n                            element.getAttribute('data-tight') === 'true' || !element.querySelector('p'),\n                        renderHTML: attributes => ({\n                            class: attributes.tight ? this.options.tightClass : null,\n                            'data-tight': attributes.tight ? 'true' : null,\n                        }),\n                    },\n                },\n            },\n        ]\n    },\n    addCommands() {\n        return {\n            toggleTight: (tight = null) => ({ editor, commands }) => {\n                function toggleTight(name) {\n                    if(!editor.isActive(name)) {\n                        return false;\n                    }\n                    const attrs = editor.getAttributes(name);\n                    return commands.updateAttributes(name, {\n                        tight: tight ?? !attrs?.tight,\n                    });\n                }\n                return this.options.listTypes\n                    .some(name => toggleTight(name));\n            }\n        }\n    },\n});\n", "import markdownit from 'markdown-it';\n\nconst md = markdownit();\n\nfunction scanDelims(text, pos) {\n    md.inline.State.prototype.scanDelims.call({ src: text, posMax: text.length })\n    const state = new (md.inline.State)(text, null, null, []);\n    return state.scanDelims(pos, true);\n}\n\nexport function shiftDelim(text, delim, start, offset) {\n    let res = text.substring(0, start) + text.substring(start + delim.length);\n    res = res.substring(0, start + offset) + delim + res.substring(start + offset);\n    return res;\n}\n\nfunction trimStart(text, delim, from, to) {\n    let pos = from, res = text;\n    while(pos < to) {\n        if(scanDelims(res, pos).can_open) {\n            break;\n        }\n        res = shiftDelim(res, delim, pos, 1);\n        pos++;\n    }\n    return { text: res, from: pos, to }\n}\n\nfunction trimEnd(text, delim, from, to) {\n    let pos = to, res = text;\n    while(pos > from) {\n        if(scanDelims(res, pos).can_close) {\n            break;\n        }\n        res = shiftDelim(res, delim, pos, -1);\n        pos--;\n    }\n    return { text: res, from, to: pos }\n}\n\nexport function trimInline(text, delim, from, to) {\n    let state = {\n        text,\n        from,\n        to,\n    }\n\n    state = trimStart(state.text, delim, state.from, state.to);\n    state = trimEnd(state.text, delim, state.from, state.to);\n\n    if(state.to - state.from < delim.length + 1) {\n        state.text = state.text.substring(0, state.from) + state.text.substring(state.to + delim.length);\n    }\n\n    return state.text;\n}\n", "import { MarkdownSerializerState as BaseMarkdownSerializerState } from \"prosemirror-markdown\";\nimport { trimInline } from \"../util/markdown\";\n\n\n/**\n * Override default MarkdownSerializerState to:\n * - handle commonmark delimiters (https://spec.commonmark.org/0.29/#left-flanking-delimiter-run)\n */\nexport class MarkdownSerializerState extends BaseMarkdownSerializerState {\n\n    inTable = false;\n\n    constructor(nodes, marks, options) {\n        super(nodes, marks, options ?? {});\n        this.inlines = [];\n    }\n\n    render(node, parent, index) {\n        super.render(node, parent, index);\n        const top = this.inlines[this.inlines.length - 1];\n        if(top?.start && top?.end) {\n            const { delimiter, start, end } = this.normalizeInline(top);\n            this.out = trimInline(this.out, delimiter, start, end);\n            this.inlines.pop();\n        }\n    }\n\n    markString(mark, open, parent, index) {\n        const info = this.marks[mark.type.name]\n        if(info.expelEnclosingWhitespace) {\n            if(open) {\n                this.inlines.push({\n                    start: this.out.length,\n                    delimiter: info.open,\n                });\n            } else {\n                const top = this.inlines.pop();\n                this.inlines.push({\n                    ...top,\n                    end: this.out.length,\n                });\n            }\n        }\n        return super.markString(mark, open, parent, index);\n    }\n\n    normalizeInline(inline) {\n        let { start, end } = inline;\n        while(this.out.charAt(start).match(/\\s/)) {\n            start++;\n        }\n        return {\n            ...inline,\n            start,\n        }\n    }\n}\n", "import { Fragment } from \"@tiptap/pm/model\";\nimport { getHTMLFromFragment, Mark } from \"@tiptap/core\";\n\n\nexport default Mark.create({\n    name: 'markdownHTMLMark',\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: {\n                    open(state, mark)  {\n                        if(!this.editor.storage.markdown.options.html) {\n                            console.warn(`Tiptap Markdown: \"${mark.type.name}\" mark is only available in html mode`);\n                            return '';\n                        }\n                        return getMarkTags(mark)?.[0] ?? '';\n                    },\n                    close(state, mark) {\n                        if(!this.editor.storage.markdown.options.html) {\n                            return '';\n                        }\n                        return getMarkTags(mark)?.[1] ?? '';\n                    },\n                },\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n});\n\nfunction getMarkTags(mark) {\n    const schema = mark.type.schema;\n    const node = schema.text(' ', [mark]);\n    const html = getHTMLFromFragment(Fragment.from(node), schema);\n    const match = html.match(/^(<.*?>) (<\\/.*?>)$/);\n    return match ? [match[1], match[2]] : null;\n}\n", "\n\nexport function elementFromString(value) {\n    // add a wrapper to preserve leading and trailing whitespace\n    const wrappedValue = `<body>${value}</body>`\n\n    return new window.DOMParser().parseFromString(wrappedValue, 'text/html').body\n}\n\nexport function escapeHTML(value) {\n    return value\n        ?.replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\n\nexport function extractElement(node) {\n    const parent = node.parentElement;\n    const prepend = parent.cloneNode();\n\n    while(parent.firstChild && parent.firstChild !== node) {\n        prepend.appendChild(parent.firstChild);\n    }\n\n    if(prepend.childNodes.length > 0) {\n        parent.parentElement.insertBefore(prepend, parent);\n    }\n    parent.parentElement.insertBefore(node, parent);\n    if(parent.childNodes.length === 0) {\n        parent.remove();\n    }\n}\n\nexport function unwrapElement(node) {\n    const parent = node.parentNode;\n\n    while (node.firstChild) parent.insertBefore(node.firstChild, node);\n\n    parent.removeChild(node);\n}\n", "import { Fragment } from \"@tiptap/pm/model\";\nimport { getHTMLFromFragment, Node } from \"@tiptap/core\";\nimport { elementFromString } from \"../../util/dom\";\n\n\nexport default Node.create({\n    name: 'markdownHTMLNode',\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent) {\n                    if(this.editor.storage.markdown.options.html) {\n                        state.write(serializeHTML(node, parent));\n                    } else {\n                        console.warn(`Tiptap Markdown: \"${node.type.name}\" node is only available in html mode`);\n                        state.write(`[${node.type.name}]`);\n                    }\n                    if(node.isBlock) {\n                        state.closeBlock(node);\n                    }\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            },\n        }\n    }\n});\n\nfunction serializeHTML(node, parent) {\n    const schema = node.type.schema;\n    const html = getHTMLFromFragment(Fragment.from(node), schema);\n\n    if(node.isBlock && (parent instanceof Fragment || parent.type.name === schema.topNodeType.name)) {\n        return formatBlock(html);\n    }\n\n    return html;\n}\n\n/**\n * format html block as per the commonmark spec\n */\nfunction formatBlock(html) {\n    const dom = elementFromString(html);\n    const element = dom.firstElementChild;\n\n    element.innerHTML = element.innerHTML.trim()\n        ? `\\n${element.innerHTML}\\n`\n        : `\\n`;\n\n    return element.outerHTML;\n}\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Blockquote = Node.create({\n    name: 'blockquote',\n});\n\nexport default Blockquote.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.blockquote,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\n\n\nconst BulletList = Node.create({\n    name: 'bulletList',\n});\n\nexport default BulletList.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    return state.renderList(node, \"  \", () => (this.editor.storage.markdown.options.bulletListMarker || \"-\") + \" \");\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\n\n\nconst CodeBlock = Node.create({\n    name: 'codeBlock',\n});\n\nexport default CodeBlock.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    state.write(\"```\" + (node.attrs.language || \"\") + \"\\n\");\n                    state.text(node.textContent, false);\n                    state.ensureNewLine();\n                    state.write(\"```\");\n                    state.closeBlock(node);\n                },\n                parse: {\n                    setup(markdownit) {\n                        markdownit.set({\n                            langPrefix: this.options.languageClassPrefix ?? 'language-',\n                        });\n                    },\n                    updateDOM(element) {\n                        element.innerHTML = element.innerHTML.replace(/\\n<\\/code><\\/pre>/g, '</code></pre>')\n                    },\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport HTMLNode from './html';\n\nconst HardBreak = Node.create({\n    name: 'hardBreak',\n});\n\nexport default HardBreak.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent, index) {\n                    for (let i = index + 1; i < parent.childCount; i++)\n                        if (parent.child(i).type != node.type) {\n                            state.write(\n                                state.inTable\n                                    ? HTMLNode.storage.markdown.serialize.call(this, state, node, parent)\n                                    : \"\\\\\\n\"\n                            );\n                            return;\n                        }\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Heading = Node.create({\n    name: 'heading',\n});\n\nexport default Heading.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.heading,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst HorizontalRule = Node.create({\n    name: 'horizontalRule',\n});\n\nexport default HorizontalRule.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.horizontal_rule,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Image = Node.create({\n    name: 'image',\n});\n\nexport default Image.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.image,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst ListItem = Node.create({\n    name: 'listItem',\n});\n\nexport default ListItem.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.list_item,\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\n\n\nconst OrderedList = Node.create({\n    name: 'orderedList',\n});\n\nfunction findIndexOfAdjacentNode(node, parent, index) {\n    let i = 0;\n    for (; index - i > 0; i++) {\n        if (parent.child(index - i - 1).type.name !== node.type.name) {\n            break;\n        }\n    }\n    return i;\n}\n\nexport default OrderedList.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent, index) {\n                    const start = node.attrs.start || 1\n                    const maxW = String(start + node.childCount - 1).length\n                    const space = state.repeat(\" \", maxW + 2)\n                    const adjacentIndex = findIndexOfAdjacentNode(node, parent, index);\n                    const separator = adjacentIndex % 2 ? ') ' : '. ';\n                    state.renderList(node, space, i => {\n                        const nStr = String(start + i)\n                        return state.repeat(\" \", maxW - nStr.length) + nStr + separator;\n                    })\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Paragraph = Node.create({\n    name: 'paragraph',\n});\n\nexport default Paragraph.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.nodes.paragraph,\n                parse: {\n                    // handled by markdown-it\n                },\n            },\n        }\n    }\n});\n", "\n\nexport function childNodes(node) {\n    return node?.content?.content ?? [];\n}\n", "import { Node } from \"@tiptap/core\";\nimport { childNodes } from \"../../util/prosemirror\";\nimport HTMLNode from './html';\n\nconst Table = Node.create({\n    name: 'table',\n});\n\nexport default Table.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node, parent) {\n                    if(!isMarkdownSerializable(node)) {\n                        HTMLNode.storage.markdown.serialize.call(this, state, node, parent);\n                        return;\n                    }\n                    state.inTable = true;\n                    node.forEach((row, p, i) => {\n                        state.write('| ');\n                        row.forEach((col, p, j) => {\n                            if(j) {\n                                state.write(' | ');\n                            }\n                            const cellContent = col.firstChild;\n                            if(cellContent.textContent.trim()) {\n                                state.renderInline(cellContent);\n                            }\n                        });\n                        state.write(' |')\n                        state.ensureNewLine();\n                        if(!i) {\n                            const delimiterRow = Array.from({length: row.childCount}).map(() => '---').join(' | ');\n                            state.write(`| ${delimiterRow} |`);\n                            state.ensureNewLine();\n                        }\n                    });\n                    state.closeBlock(node);\n                    state.inTable = false;\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n})\n\n\nfunction hasSpan(node) {\n    return node.attrs.colspan > 1 || node.attrs.rowspan > 1;\n}\n\nfunction isMarkdownSerializable(node) {\n    const rows = childNodes(node);\n    const firstRow = rows[0];\n    const bodyRows = rows.slice(1);\n\n    if(childNodes(firstRow).some(cell => cell.type.name !== 'tableHeader' || hasSpan(cell) || cell.childCount > 1)) {\n        return false;\n    }\n\n    if(bodyRows.some(row =>\n        childNodes(row).some(cell => cell.type.name === 'tableHeader' || hasSpan(cell) || cell.childCount > 1)\n    )) {\n        return false;\n    }\n\n    return true;\n}\n", "import { Node } from \"@tiptap/core\";\n\n\nconst TaskItem = Node.create({\n    name: 'taskItem',\n});\n\nexport default TaskItem.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    const check = node.attrs.checked ? '[x]' : '[ ]';\n                    state.write(`${check} `);\n                    state.renderContent(node);\n                },\n                parse: {\n                    updateDOM(element) {\n                        [...element.querySelectorAll('.task-list-item')]\n                            .forEach(item => {\n                                const input = item.querySelector('input');\n                                item.setAttribute('data-type', 'taskItem');\n                                if(input) {\n                                    item.setAttribute('data-checked', input.checked);\n                                    input.remove();\n                                }\n                            });\n                    },\n                }\n            }\n        }\n    }\n});\n", "import taskListPlugin from \"markdown-it-task-lists\";\nimport { Node } from \"@tiptap/core\";\nimport BulletList from \"./bullet-list\";\n\n\nconst TaskList = Node.create({\n    name: 'taskList',\n});\n\nexport default TaskList.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: BulletList.storage.markdown.serialize,\n                parse: {\n                    setup(markdownit) {\n                        markdownit.use(taskListPlugin);\n                    },\n                    updateDOM(element) {\n                        [...element.querySelectorAll('.contains-task-list')]\n                            .forEach(list => {\n                                list.setAttribute('data-type', 'taskList');\n                            });\n                    },\n                }\n            }\n        }\n    }\n});\n", "import { Node } from \"@tiptap/core\";\nimport { escapeHTML } from \"../../util/dom\";\n\n\nconst Text = Node.create({\n    name: 'text',\n});\n\nexport default Text.extend({\n    /**\n     * @return {{markdown: MarkdownNodeSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize(state, node) {\n                    state.text(escapeHTML(node.text));\n                },\n                parse: {\n                    // handled by markdown-it\n                },\n            }\n        }\n    }\n});\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Bold = Mark.create({\n    name: 'bold',\n});\n\nexport default Bold.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.strong,\n                parse: {\n                    // handled by markdown-it\n                }\n            },\n        }\n    }\n});\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Code = Mark.create({\n    name: 'code',\n});\n\nexport default Code.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.code,\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n})\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Italic = Mark.create({\n    name: 'italic',\n});\n\nexport default Italic.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.em,\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n})\n", "import { Mark } from \"@tiptap/core\";\nimport { defaultMarkdownSerializer } from \"prosemirror-markdown\";\n\n\nconst Link = Mark.create({\n    name: 'link',\n});\n\nexport default Link.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: defaultMarkdownSerializer.marks.link,\n                parse: {\n                    // handled by markdown-it\n                }\n            }\n        }\n    }\n})\n", "import { Mark } from \"@tiptap/core\";\n\n\nconst Strike = Mark.create({\n    name: 'strike',\n});\n\nexport default Strike.extend({\n    /**\n     * @return {{markdown: MarkdownMarkSpec}}\n     */\n    addStorage() {\n        return {\n            markdown: {\n                serialize: {open: '~~', close: '~~', expelEnclosingWhitespace: true},\n                parse: {\n                    // handled by markdown-it\n                },\n            },\n        }\n    }\n});\n", "import Blockquote from \"./nodes/blockquote\";\nimport BulletList from \"./nodes/bullet-list\";\nimport CodeB<PERSON> from \"./nodes/code-block\";\nimport HardBreak from \"./nodes/hard-break\";\nimport Heading from \"./nodes/heading\";\nimport HorizontalRule from \"./nodes/horizontal-rule\";\nimport HTMLNode from \"./nodes/html\";\nimport Image from \"./nodes/image\";\nimport ListItem from \"./nodes/list-item\";\nimport OrderedList from \"./nodes/ordered-list\";\nimport Paragraph from \"./nodes/paragraph\";\nimport Table from \"./nodes/table\";\nimport TaskItem from \"./nodes/task-item\";\nimport TaskList from \"./nodes/task-list\";\nimport Text from \"./nodes/text\";\n\nimport Bold from \"./marks/bold\";\nimport Code from \"./marks/code\";\nimport HTMLMark from \"./marks/html\";\nimport Italic from \"./marks/italic\";\nimport Link from \"./marks/link\";\nimport Strike from \"./marks/strike\";\n\n\nexport default [\n    Blockquote,\n    BulletList,\n    CodeBlock,\n    HardBreak,\n    Heading,\n    HorizontalRule,\n    HTMLNode,\n    Image,\n    ListItem,\n    OrderedList,\n    Paragraph,\n    Table,\n    TaskItem,\n    TaskList,\n    Text,\n\n    Bold,\n    Code,\n    HTMLMark,\n    Italic,\n    Link,\n    Strike,\n]\n", "import markdownExtensions from \"../extensions\";\n\n\nexport function getMarkdownSpec(extension) {\n    const markdownSpec = extension.storage?.markdown;\n    const defaultMarkdownSpec = markdownExtensions.find(e => e.name === extension.name)?.storage.markdown;\n\n    if(markdownSpec || defaultMarkdownSpec) {\n        return {\n            ...defaultMarkdownSpec,\n            ...markdownSpec,\n        };\n    }\n\n    return null;\n}\n", "import { MarkdownSerializerState } from './state';\nimport HTMLMark from \"../extensions/marks/html\";\nimport HTMLNode from \"../extensions/nodes/html\";\nimport { getMarkdownSpec } from \"../util/extensions\";\nimport HardBreak from \"../extensions/nodes/hard-break\";\n\n\nexport class MarkdownSerializer {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    editor = null;\n\n    constructor(editor) {\n        this.editor = editor;\n    }\n\n    serialize(content) {\n        const state = new MarkdownSerializerState(this.nodes, this.marks, {\n            hardBreakNodeName: HardBreak.name,\n        });\n\n        state.renderContent(content);\n\n        return state.out;\n    }\n\n    get nodes() {\n        return {\n            ...Object.fromEntries(\n                Object.keys(this.editor.schema.nodes)\n                    .map(name => [name, this.serializeNode(HTMLNode)])\n            ),\n            ...Object.fromEntries(\n                this.editor.extensionManager.extensions\n                    .filter(extension => extension.type === 'node' && this.serializeNode(extension))\n                    .map(extension => [extension.name, this.serializeNode(extension)])\n                ?? []\n            ),\n        };\n    }\n\n    get marks() {\n        return {\n            ...Object.fromEntries(\n                Object.keys(this.editor.schema.marks)\n                    .map(name => [name, this.serializeMark(HTMLMark)])\n            ),\n            ...Object.fromEntries(\n                this.editor.extensionManager.extensions\n                    .filter(extension => extension.type === 'mark' && this.serializeMark(extension))\n                    .map(extension => [extension.name, this.serializeMark(extension)])\n                ?? []\n            ),\n        };\n    }\n\n    serializeNode(node) {\n        return getMarkdownSpec(node)?.serialize?.bind({ editor: this.editor, options: node.options });\n    }\n\n    serializeMark(mark) {\n        const serialize = getMarkdownSpec(mark)?.serialize;\n        return serialize\n            ? {\n                ...serialize,\n                open: typeof serialize.open === 'function' ? serialize.open.bind({ editor: this.editor, options: mark.options }) : serialize.open,\n                close: typeof serialize.close === 'function' ? serialize.close.bind({ editor: this.editor, options: mark.options }) : serialize.close,\n            }\n            : null\n    }\n}\n\n", "import markdownit from \"markdown-it\";\nimport { elementFromString, extractElement, unwrapElement } from \"../util/dom\";\nimport { getMarkdownSpec } from \"../util/extensions\";\n\nexport class MarkdownParser {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    editor = null;\n    /**\n     * @type {markdownit}\n     */\n    md = null;\n\n    constructor(editor, { html, linkify, breaks }) {\n        this.editor = editor;\n        this.md = this.withPatchedRenderer(markdownit({\n            html,\n            linkify,\n            breaks,\n        }));\n    }\n\n    parse(content, { inline } = {}) {\n        if(typeof content === 'string') {\n            this.editor.extensionManager.extensions.forEach(extension =>\n                getMarkdownSpec(extension)?.parse?.setup?.call({ editor:this.editor, options:extension.options }, this.md)\n            );\n\n            const renderedHTML = this.md.render(content);\n            const element = elementFromString(renderedHTML);\n\n            this.editor.extensionManager.extensions.forEach(extension =>\n                getMarkdownSpec(extension)?.parse?.updateDOM?.call({ editor:this.editor, options:extension.options }, element)\n            );\n\n            this.normalizeDOM(element, { inline, content });\n\n            return element.innerHTML;\n        }\n\n        return content;\n    }\n\n    normalizeDOM(node, { inline, content }) {\n        this.normalizeBlocks(node);\n\n        // remove all \\n appended by markdown-it\n        node.querySelectorAll('*').forEach(el => {\n            if(el.nextSibling?.nodeType === Node.TEXT_NODE && !el.closest('pre')) {\n                el.nextSibling.textContent = el.nextSibling.textContent.replace(/^\\n/, '');\n            }\n        });\n\n        if(inline) {\n            this.normalizeInline(node, content);\n        }\n\n        return node;\n    }\n\n    normalizeBlocks(node) {\n        const blocks = Object.values(this.editor.schema.nodes)\n            .filter(node => node.isBlock);\n\n        const selector = blocks\n            .map(block => block.spec.parseDOM?.map(spec => spec.tag))\n            .flat()\n            .filter(Boolean)\n            .join(',');\n\n        if(!selector) {\n            return;\n        }\n\n        [...node.querySelectorAll(selector)].forEach(el => {\n            if(el.parentElement.matches('p')) {\n                extractElement(el);\n            }\n        });\n    }\n\n    normalizeInline(node, content) {\n        if(node.firstElementChild?.matches('p')) {\n            const firstParagraph = node.firstElementChild;\n            const { nextElementSibling } = firstParagraph;\n            const startSpaces = content.match(/^\\s+/)?.[0] ?? '';\n            const endSpaces = !nextElementSibling\n                ? content.match(/\\s+$/)?.[0] ?? ''\n                : '';\n\n            if(content.match(/^\\n\\n/)) {\n                firstParagraph.innerHTML = `${firstParagraph.innerHTML}${endSpaces}`;\n                return;\n            }\n\n            unwrapElement(firstParagraph);\n\n            node.innerHTML = `${startSpaces}${node.innerHTML}${endSpaces}`;\n        }\n    }\n\n    /**\n     * @param {markdownit} md\n     */\n    withPatchedRenderer(md) {\n        const withoutNewLine = (renderer) => (...args) => {\n            const rendered = renderer(...args);\n            if(rendered === '\\n') {\n                return rendered; // keep soft breaks\n            }\n            if(rendered[rendered.length - 1] === '\\n') {\n                return rendered.slice(0, -1);\n            }\n            return rendered;\n        }\n\n        md.renderer.rules.hardbreak = withoutNewLine(md.renderer.rules.hardbreak);\n        md.renderer.rules.softbreak = withoutNewLine(md.renderer.rules.softbreak);\n        md.renderer.rules.fence = withoutNewLine(md.renderer.rules.fence);\n        md.renderer.rules.code_block = withoutNewLine(md.renderer.rules.code_block);\n        md.renderer.renderToken = withoutNewLine(md.renderer.renderToken.bind(md.renderer));\n\n        return md;\n    }\n}\n\n", "import { Extension } from \"@tiptap/core\";\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';\nimport { DOMParser } from '@tiptap/pm/model';\nimport { elementFromString } from \"../../util/dom\";\n\nexport const MarkdownClipboard = Extension.create({\n    name: 'markdownClipboard',\n    addOptions() {\n        return {\n            transformPastedText: false,\n            transformCopiedText: false,\n        }\n    },\n    addProseMirrorPlugins() {\n        return [\n            new Plugin({\n                key: new PluginKey('markdownClipboard'),\n                props: {\n                    clipboardTextParser: (text, context, plainText) => {\n                        if(plainText || !this.options.transformPastedText) {\n                            return null; // pasting with shift key prevents formatting\n                        }\n                        const parsed = this.editor.storage.markdown.parser.parse(text, { inline: true });\n                        return DOMParser.fromSchema(this.editor.schema)\n                            .parseSlice(elementFromString(parsed), {\n                                preserveWhitespace: true,\n                                context,\n                            });\n                    },\n                    /**\n                     * @param {import('prosemirror-model').Slice} slice\n                     */\n                    clipboardTextSerializer: (slice) => {\n                        if(!this.options.transformCopiedText) {\n                            return null;\n                        }\n                        return this.editor.storage.markdown.serializer.serialize(slice.content);\n                    },\n                },\n            })\n        ]\n    }\n})\n", "import { Extension, extensions } from '@tiptap/core';\nimport { MarkdownTightLists } from \"./extensions/tiptap/tight-lists\";\nimport { MarkdownSerializer } from \"./serialize/MarkdownSerializer\";\nimport { MarkdownParser } from \"./parse/MarkdownParser\";\nimport { MarkdownClipboard } from \"./extensions/tiptap/clipboard\";\n\nexport const Markdown = Extension.create({\n    name: 'markdown',\n    priority: 50,\n    addOptions() {\n        return {\n            html: true,\n            tightLists: true,\n            tightListClass: 'tight',\n            bulletListMarker: '-',\n            linkify: false,\n            breaks: false,\n            transformPastedText: false,\n            transformCopiedText: false,\n        }\n    },\n    addCommands() {\n        const commands = extensions.Commands.config.addCommands();\n        return {\n            setContent: (content, emitUpdate, parseOptions) => (props) => {\n                return commands.setContent(\n                    props.editor.storage.markdown.parser.parse(content),\n                    emitUpdate,\n                    parseOptions\n                )(props);\n            },\n            insertContentAt: (range, content, options) => (props) => {\n                return commands.insertContentAt(\n                    range,\n                    props.editor.storage.markdown.parser.parse(content, { inline: true }),\n                    options\n                )(props);\n            },\n        }\n    },\n    onBeforeCreate() {\n        this.editor.storage.markdown = {\n            options: { ...this.options },\n            parser: new MarkdownParser(this.editor, this.options),\n            serializer: new MarkdownSerializer(this.editor),\n            getMarkdown: () => {\n                return this.editor.storage.markdown.serializer.serialize(this.editor.state.doc);\n            },\n        }\n        this.editor.options.initialContent = this.editor.options.content;\n        this.editor.options.content = this.editor.storage.markdown.parser.parse(this.editor.options.content);\n    },\n    onCreate() {\n        this.editor.options.content = this.editor.options.initialContent;\n        delete this.editor.options.initialContent;\n    },\n    addStorage() {\n        return {\n            /// storage will be defined in onBeforeCreate() to prevent initial object overriding\n        }\n    },\n    addExtensions() {\n        return [\n            MarkdownTightLists.configure({\n                tight: this.options.tightLists,\n                tightClass: this.options.tightListClass,\n            }),\n            MarkdownClipboard.configure({\n                transformPastedText: this.options.transformPastedText,\n                transformCopiedText: this.options.transformCopiedText,\n            }),\n        ]\n    },\n});\n"], "names": ["MarkdownTightLists", "Extension", "create", "name", "addOptions", "tight", "tightClass", "listTypes", "addGlobalAttributes", "types", "options", "attributes", "default", "parseHTML", "element", "getAttribute", "querySelector", "renderHTML", "class", "addCommands", "_this", "toggleTight", "arguments", "length", "undefined", "_ref", "editor", "commands", "isActive", "attrs", "getAttributes", "updateAttributes", "some", "md", "markdownit", "scanDelims", "text", "pos", "inline", "State", "prototype", "call", "src", "posMax", "state", "<PERSON><PERSON><PERSON><PERSON>", "delim", "start", "offset", "res", "substring", "trimStart", "from", "to", "can_open", "trimEnd", "can_close", "trimInline", "MarkdownSerializerState", "BaseMarkdownSerializerState", "constructor", "nodes", "marks", "inTable", "inlines", "render", "node", "parent", "index", "top", "end", "delimiter", "normalizeInline", "out", "pop", "markString", "mark", "open", "info", "type", "expelEnclosingWhitespace", "push", "char<PERSON>t", "match", "<PERSON>", "addStorage", "markdown", "serialize", "_getMarkTags$", "_getMarkTags", "storage", "html", "console", "warn", "getMarkTags", "close", "_getMarkTags$2", "_getMarkTags2", "parse", "schema", "getHTMLFromFragment", "Fragment", "elementFromString", "value", "wrappedValue", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "body", "escapeHTML", "replace", "extractElement", "parentElement", "prepend", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "childNodes", "insertBefore", "remove", "unwrapElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Node", "write", "serializeHTML", "isBlock", "closeBlock", "topNodeType", "formatBlock", "dom", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "innerHTML", "trim", "outerHTML", "Blockquote", "extend", "defaultMarkdownSerializer", "blockquote", "BulletList", "renderList", "bullet<PERSON>ist<PERSON><PERSON><PERSON>", "CodeBlock", "language", "textContent", "ensureNewLine", "setup", "_this$options$languag", "set", "langPrefix", "languageClassPrefix", "updateDOM", "HardBreak", "i", "childCount", "child", "HTMLNode", "Heading", "heading", "HorizontalRule", "horizontal_rule", "Image", "image", "ListItem", "list_item", "OrderedList", "findIndexOfAdjacentNode", "maxW", "String", "space", "repeat", "adjacentIndex", "separator", "nStr", "Paragraph", "paragraph", "_node$content$content", "_node$content", "content", "Table", "isMarkdownSerializable", "for<PERSON>ach", "row", "p", "col", "j", "cellContent", "renderInline", "delimiterRow", "Array", "map", "join", "hasSpan", "colspan", "rowspan", "rows", "firstRow", "bodyRows", "slice", "cell", "TaskItem", "check", "checked", "renderContent", "querySelectorAll", "item", "input", "setAttribute", "TaskList", "use", "taskListPlugin", "list", "Text", "Bold", "strong", "Code", "code", "Italic", "em", "Link", "link", "Strike", "HTMLMark", "getMarkdownSpec", "extension", "_extension$storage", "_markdownExtensions$f", "markdownSpec", "defaultMarkdownSpec", "markdownExtensions", "find", "e", "MarkdownSerializer", "hardBreakNodeName", "_this$editor$extensio", "Object", "fromEntries", "keys", "serializeNode", "extensionManager", "extensions", "filter", "_this$editor$extensio2", "serializeMark", "_getMarkdownSpec", "bind", "_getMarkdownSpec2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "linkify", "breaks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderedHTML", "normalizeDOM", "_ref2", "normalizeBlocks", "el", "_el$nextSibling", "nextS<PERSON>ling", "nodeType", "TEXT_NODE", "closest", "blocks", "values", "selector", "block", "_block$spec$parseDOM", "spec", "parseDOM", "tag", "flat", "Boolean", "matches", "_node$firstElementChi", "_content$match$", "_content$match", "_content$match$2", "_content$match2", "firstParagraph", "nextElement<PERSON><PERSON>ling", "startSpaces", "endSpaces", "withoutNewLine", "renderer", "rendered", "rules", "hardbreak", "softbreak", "fence", "code_block", "renderToken", "MarkdownClipboard", "transformPastedText", "transformCopiedText", "addProseMirrorPlugins", "Plugin", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "clipboardTextParser", "context", "plainText", "parsed", "parser", "fromSchema", "parseSlice", "preserveWhitespace", "clipboardTextSerializer", "serializer", "<PERSON><PERSON>", "priority", "tightLists", "tightListClass", "Commands", "config", "<PERSON><PERSON><PERSON><PERSON>", "emitUpdate", "parseOptions", "insertContentAt", "range", "onBeforeCreate", "getMarkdown", "doc", "initialContent", "onCreate", "addExtensions", "configure"], "mappings": ";;;;;;;;;;AAEO,QAAMA,qBAAqBC,KAAS,UAACC,OAAO;AAAA,IAC/CC,MAAM;AAAA,IACNC,YAAYA,OAAO;AAAA,MACfC,OAAO;AAAA,MACPC,YAAY;AAAA,MACZC,WAAW,CACP,cACA,aAAa;AAAA,IAErB;AAAA,IACAC,sBAAsB;AAClB,aAAO,CACH;AAAA,QACIC,OAAO,KAAKC,QAAQH;AAAAA,QACpBI,YAAY;AAAA,UACRN,OAAO;AAAA,YACHO,SAAS,KAAKF,QAAQL;AAAAA,YACtBQ,WAAWC,aACPA,QAAQC,aAAa,YAAY,MAAM,UAAU,CAACD,QAAQE,cAAc,GAAG;AAAA,YAC/EC,YAAYN,iBAAe;AAAA,cACvBO,OAAOP,WAAWN,QAAQ,KAAKK,QAAQJ,aAAa;AAAA,cACpD,cAAcK,WAAWN,QAAQ,SAAS;AAAA;UAElD;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IAER;AAAA,IACDc,cAAc;AAAA,UAAAC,QAAA;AACV,aAAO;AAAA,QACHC,aAAa,WAAA;AAAA,cAAChB,QAAKiB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAI,iBAAKG,UAA0B;AAAA,gBAAzB;AAAA,cAAEC;AAAAA,cAAQC;AAAAA,YAAU,IAAAF;AAChD,qBAASJ,YAAYlB,MAAM;AACvB,kBAAG,CAACuB,OAAOE,SAASzB,IAAI,GAAG;AACvB,uBAAO;AAAA,cACX;AACA,oBAAM0B,QAAQH,OAAOI,cAAc3B,IAAI;AACvC,qBAAOwB,SAASI,iBAAiB5B,MAAM;AAAA,gBACnCE,OAAOA,UAAAA,QAAAA,mBAAAA,QAAS,EAACwB,UAAAA,QAAAA,UAAAA,UAAAA,MAAOxB;AAAAA,cAC5B,CAAC;AAAA,YACL;AACA,mBAAOe,MAAKV,QAAQH,UACfyB,KAAK7B,UAAQkB,YAAYlB,IAAI,CAAC;AAAA;QACtC;AAAA;IAET;AAAA,EACJ,CAAC;AC7CD,QAAM8B,KAAKC,WAAU;AAErB,WAASC,WAAWC,MAAMC,KAAK;AAC3BJ,OAAGK,OAAOC,MAAMC,UAAUL,WAAWM,KAAK;AAAA,MAAEC,KAAKN;AAAAA,MAAMO,QAAQP,KAAKb;AAAAA,IAAO,CAAC;AAC5E,UAAMqB,SAAQ,IAAKX,GAAGK,OAAOC,MAAOH,MAAM,MAAM,MAAM,CAAA,CAAE;AACxD,WAAOQ,OAAMT,WAAWE,KAAK,IAAI;AAAA,EACrC;AAEO,WAASQ,WAAWT,MAAMU,OAAOC,OAAOC,QAAQ;AACnD,QAAIC,MAAMb,KAAKc,UAAU,GAAGH,KAAK,IAAIX,KAAKc,UAAUH,QAAQD,MAAMvB,MAAM;AACxE0B,UAAMA,IAAIC,UAAU,GAAGH,QAAQC,MAAM,IAAIF,QAAQG,IAAIC,UAAUH,QAAQC,MAAM;AAC7E,WAAOC;AAAAA,EACX;AAEA,WAASE,UAAUf,MAAMU,OAAOM,MAAMC,IAAI;AACtC,QAAIhB,MAAMe,MAAMH,MAAMb;AACtB,WAAMC,MAAMgB,IAAI;AACZ,UAAGlB,WAAWc,KAAKZ,GAAG,EAAEiB,UAAU;AAC9B;AAAA,MACJ;AACAL,YAAMJ,WAAWI,KAAKH,OAAOT,KAAK,CAAC;AACnCA;AAAAA,IACJ;AACA,WAAO;AAAA,MAAED,MAAMa;AAAAA,MAAKG,MAAMf;AAAAA,MAAKgB;AAAAA;EACnC;AAEA,WAASE,QAAQnB,MAAMU,OAAOM,MAAMC,IAAI;AACpC,QAAIhB,MAAMgB,IAAIJ,MAAMb;AACpB,WAAMC,MAAMe,MAAM;AACd,UAAGjB,WAAWc,KAAKZ,GAAG,EAAEmB,WAAW;AAC/B;AAAA,MACJ;AACAP,YAAMJ,WAAWI,KAAKH,OAAOT,KAAK,EAAE;AACpCA;AAAAA,IACJ;AACA,WAAO;AAAA,MAAED,MAAMa;AAAAA,MAAKG;AAAAA,MAAMC,IAAIhB;AAAAA;EAClC;AAEO,WAASoB,WAAWrB,MAAMU,OAAOM,MAAMC,IAAI;AAC9C,QAAIT,SAAQ;AAAA,MACRR;AAAAA,MACAgB;AAAAA,MACAC;AAAAA;AAGJT,IAAAA,SAAQO,UAAUP,OAAMR,MAAMU,OAAOF,OAAMQ,MAAMR,OAAMS,EAAE;AACzDT,IAAAA,SAAQW,QAAQX,OAAMR,MAAMU,OAAOF,OAAMQ,MAAMR,OAAMS,EAAE;AAEvD,QAAGT,OAAMS,KAAKT,OAAMQ,OAAON,MAAMvB,SAAS,GAAG;AACzCqB,MAAAA,OAAMR,OAAOQ,OAAMR,KAAKc,UAAU,GAAGN,OAAMQ,IAAI,IAAIR,OAAMR,KAAKc,UAAUN,OAAMS,KAAKP,MAAMvB,MAAM;AAAA,IACnG;AAEA,WAAOqB,OAAMR;AAAAA,EACjB;AAAA,EC/CO,MAAMsB,gCAAgCC,oBAAAA,wBAA4B;AAAA,IAIrEC,YAAYC,OAAOC,OAAOpD,SAAS;AAC/B,YAAMmD,OAAOC,OAAOpD,YAAAA,QAAAA,YAAAA,SAAAA,UAAW,CAAA,CAAE;AAHrCqD,qCAAU;AAIN,WAAKC,UAAU;IACnB;AAAA,IAEAC,OAAOC,MAAMC,QAAQC,OAAO;AACxB,YAAMH,OAAOC,MAAMC,QAAQC,KAAK;AAChC,YAAMC,MAAM,KAAKL,QAAQ,KAAKA,QAAQzC,SAAS,CAAC;AAChD,UAAG8C,QAAG,QAAHA,QAAG,UAAHA,IAAKtB,SAASsB,gBAAAA,QAAG,UAAHA,IAAKC,KAAK;AACvB,cAAM;AAAA,UAAEC;AAAAA,UAAWxB;AAAAA,UAAOuB;AAAAA,QAAI,IAAI,KAAKE,gBAAgBH,GAAG;AAC1D,aAAKI,MAAMhB,WAAW,KAAKgB,KAAKF,WAAWxB,OAAOuB,GAAG;AACrD,aAAKN,QAAQU;MACjB;AAAA,IACJ;AAAA,IAEAC,WAAWC,MAAMC,MAAMV,QAAQC,OAAO;AAClC,YAAMU,OAAO,KAAKhB,MAAMc,KAAKG,KAAK5E,IAAI;AACtC,UAAG2E,KAAKE,0BAA0B;AAC9B,YAAGH,MAAM;AACL,eAAKb,QAAQiB,KAAK;AAAA,YACdlC,OAAO,KAAK0B,IAAIlD;AAAAA,YAChBgD,WAAWO,KAAKD;AAAAA,UACpB,CAAC;AAAA,QACL,OAAO;AACH,gBAAMR,MAAM,KAAKL,QAAQU,IAAG;AAC5B,eAAKV,QAAQiB,KAAK;AAAA,YACd,GAAGZ;AAAAA,YACHC,KAAK,KAAKG,IAAIlD;AAAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO,MAAMoD,WAAWC,MAAMC,MAAMV,QAAQC,KAAK;AAAA,IACrD;AAAA,IAEAI,gBAAgBlC,QAAQ;AACpB,UAAI;AAAA,QAAES;AAAAA,QAAOuB;AAAAA,MAAK,IAAGhC;AACrB,aAAM,KAAKmC,IAAIS,OAAOnC,KAAK,EAAEoC,MAAM,IAAI,GAAG;AACtCpC;AAAAA,MACJ;AACA,aAAO;AAAA,QACH,GAAGT;AAAAA,QACHS;AAAAA;IAER;AAAA,EACJ;ACpDeqC,QAAAA,WAAAA,KAAAA,KAAKlF,OAAO;AAAA,IACvBC,MAAM;AAAA;AAAA;AAAA;AAAA,IAINkF,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAW;AAAA,YACPV,KAAKjC,QAAOgC,MAAO;AAAA,kBAAAY,eAAAC;AACf,kBAAG,CAAC,KAAK/D,OAAOgE,QAAQJ,SAAS5E,QAAQiF,MAAM;AAC3CC,wBAAQC,KAAM,qBAAoBjB,KAAKG,KAAK5E,IAAK,uCAAsC;AACvF,uBAAO;AAAA,cACX;AACA,sBAAAqF,iBAAAC,eAAOK,YAAYlB,IAAI,OAACa,QAAAA,mCAAjBA,aAAoB,CAAC,OAAC,QAAAD,kBAAAA,SAAAA,gBAAI;AAAA,YACpC;AAAA,YACDO,MAAMnD,QAAOgC,MAAM;AAAA,kBAAAoB,gBAAAC;AACf,kBAAG,CAAC,KAAKvE,OAAOgE,QAAQJ,SAAS5E,QAAQiF,MAAM;AAC3C,uBAAO;AAAA,cACX;AACA,sBAAAK,kBAAAC,gBAAOH,YAAYlB,IAAI,OAACqB,QAAAA,oCAAjBA,cAAoB,CAAC,OAAC,QAAAD,mBAAAA,SAAAA,iBAAI;AAAA,YACrC;AAAA,UACH;AAAA,UACDE,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AAED,WAASJ,YAAYlB,MAAM;AACvB,UAAMuB,SAASvB,KAAKG,KAAKoB;AACzB,UAAMjC,OAAOiC,OAAO/D,KAAK,KAAK,CAACwC,IAAI,CAAC;AACpC,UAAMe,OAAOS,KAAAA,oBAAoBC,MAAQ,SAACjD,KAAKc,IAAI,GAAGiC,MAAM;AAC5D,UAAMhB,QAAQQ,KAAKR,MAAM,qBAAqB;AAC9C,WAAOA,QAAQ,CAACA,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,IAAI;AAAA,EAC1C;ACvCO,WAASmB,kBAAkBC,OAAO;AAErC,UAAMC,eAAgB,SAAQD,KAAM;AAEpC,WAAO,IAAIE,OAAOC,UAAW,EAACC,gBAAgBH,cAAc,WAAW,EAAEI;AAAAA,EAC7E;AAEO,WAASC,WAAWN,OAAO;AAC9B,WAAOA,UAAAA,QAAAA,4BAAAA,MACDO,QAAQ,MAAM,MAAM,EACrBA,QAAQ,MAAM,MAAM;AAAA,EAC7B;AAEO,WAASC,eAAe7C,MAAM;AACjC,UAAMC,SAASD,KAAK8C;AACpB,UAAMC,UAAU9C,OAAO+C;AAEvB,WAAM/C,OAAOgD,cAAchD,OAAOgD,eAAejD,MAAM;AACnD+C,cAAQG,YAAYjD,OAAOgD,UAAU;AAAA,IACzC;AAEA,QAAGF,QAAQI,WAAW9F,SAAS,GAAG;AAC9B4C,aAAO6C,cAAcM,aAAaL,SAAS9C,MAAM;AAAA,IACrD;AACAA,WAAO6C,cAAcM,aAAapD,MAAMC,MAAM;AAC9C,QAAGA,OAAOkD,WAAW9F,WAAW,GAAG;AAC/B4C,aAAOoD,OAAM;AAAA,IACjB;AAAA,EACJ;AAEO,WAASC,cAActD,MAAM;AAChC,UAAMC,SAASD,KAAKuD;AAEpB,WAAOvD,KAAKiD;AAAYhD,aAAOmD,aAAapD,KAAKiD,YAAYjD,IAAI;AAEjEC,WAAOuD,YAAYxD,IAAI;AAAA,EAC3B;ACjCeyD,QAAAA,WAAAA,KAAAA,KAAKzH,OAAO;AAAA,IACvBC,MAAM;AAAA,IACNkF,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAMC,QAAQ;AAC3B,gBAAG,KAAKzC,OAAOgE,QAAQJ,SAAS5E,QAAQiF,MAAM;AAC1C/C,cAAAA,OAAMgF,MAAMC,cAAc3D,MAAMC,MAAM,CAAC;AAAA,YAC3C,OAAO;AACHyB,sBAAQC,KAAM,qBAAoB3B,KAAKa,KAAK5E,IAAK,uCAAsC;AACvFyC,cAAAA,OAAMgF,MAAO,IAAG1D,KAAKa,KAAK5E,IAAK,GAAE;AAAA,YACrC;AACA,gBAAG+D,KAAK4D,SAAS;AACblF,cAAAA,OAAMmF,WAAW7D,IAAI;AAAA,YACzB;AAAA,UACH;AAAA,UACDgC,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AAED,WAAS2B,cAAc3D,MAAMC,QAAQ;AACjC,UAAMgC,SAASjC,KAAKa,KAAKoB;AACzB,UAAMR,OAAOS,KAAAA,oBAAoBC,MAAQ,SAACjD,KAAKc,IAAI,GAAGiC,MAAM;AAE5D,QAAGjC,KAAK4D,YAAY3D,kBAAkBkC,kBAAYlC,OAAOY,KAAK5E,SAASgG,OAAO6B,YAAY7H,OAAO;AAC7F,aAAO8H,YAAYtC,IAAI;AAAA,IAC3B;AAEA,WAAOA;AAAAA,EACX;AAKA,WAASsC,YAAYtC,MAAM;AACvB,UAAMuC,MAAM5B,kBAAkBX,IAAI;AAClC,UAAM7E,UAAUoH,IAAIC;AAEpBrH,YAAQsH,YAAYtH,QAAQsH,UAAUC,KAAI,IACnC;AAAA,EAAIvH,QAAQsH,SAAU;AAAA,IACtB;AAAA;AAEP,WAAOtH,QAAQwH;AAAAA,EACnB;AChDA,QAAMC,aAAaZ,KAAI,KAACzH,OAAO;AAAA,IAC3BC,MAAM;AAAA,EACV,CAAC;AAEcoI,QAAAA,eAAAA,WAAWC,OAAO;AAAA;AAAA;AAAA;AAAA,IAI7BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B5E,MAAM6E;AAAAA,UAC3CxC,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACnBD,QAAMyC,aAAahB,KAAI,KAACzH,OAAO;AAAA,IAC3BC,MAAM;AAAA,EACV,CAAC;AAEcwI,QAAAA,eAAAA,WAAWH,OAAO;AAAA;AAAA;AAAA;AAAA,IAI7BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAM;AACnB,mBAAOtB,OAAMgG,WAAW1E,MAAM,MAAM,OAAO,KAAKxC,OAAOgE,QAAQJ,SAAS5E,QAAQmI,oBAAoB,OAAO,GAAG;AAAA,UACjH;AAAA,UACD3C,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACpBD,QAAM4C,YAAYnB,KAAI,KAACzH,OAAO;AAAA,IAC1BC,MAAM;AAAA,EACV,CAAC;AAEc2I,QAAAA,cAAAA,UAAUN,OAAO;AAAA;AAAA;AAAA;AAAA,IAI5BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAM;AACnBtB,YAAAA,OAAMgF,MAAM,SAAS1D,KAAKrC,MAAMkH,YAAY,MAAM,IAAI;AACtDnG,YAAAA,OAAMR,KAAK8B,KAAK8E,aAAa,KAAK;AAClCpG,YAAAA,OAAMqG,cAAa;AACnBrG,YAAAA,OAAMgF,MAAM,KAAK;AACjBhF,YAAAA,OAAMmF,WAAW7D,IAAI;AAAA,UACxB;AAAA,UACDgC,OAAO;AAAA,YACHgD,MAAMhH,aAAY;AAAA,kBAAAiH;AACdjH,cAAAA,YAAWkH,IAAI;AAAA,gBACXC,aAAUF,wBAAE,KAAKzI,QAAQ4I,yBAAmBH,QAAAA,0BAAAA,SAAAA,wBAAI;AAAA,cACpD,CAAC;AAAA,YACJ;AAAA,YACDI,UAAUzI,SAAS;AACfA,sBAAQsH,YAAYtH,QAAQsH,UAAUtB,QAAQ,sBAAsB,eAAe;AAAA,YACvF;AAAA,UACJ;AAAA,QACJ;AAAA;IAER;AAAA,EACJ,CAAC;AC/BD,QAAM0C,YAAY7B,KAAI,KAACzH,OAAO;AAAA,IAC1BC,MAAM;AAAA,EACV,CAAC;AAEcqJ,QAAAA,cAAAA,UAAUhB,OAAO;AAAA;AAAA;AAAA;AAAA,IAI5BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAMC,QAAQC,OAAO;AAClC,qBAASqF,IAAIrF,QAAQ,GAAGqF,IAAItF,OAAOuF,YAAYD;AAC3C,kBAAItF,OAAOwF,MAAMF,CAAC,EAAE1E,QAAQb,KAAKa,MAAM;AACnCnC,gBAAAA,OAAMgF,MACFhF,OAAMmB,UACA6F,SAASlE,QAAQJ,SAASC,UAAU9C,KAAK,MAAMG,QAAOsB,MAAMC,MAAM,IAClE,MACV;AACA;AAAA,cACJ;AAAA,UACP;AAAA,UACD+B,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AC3BD,QAAM2D,UAAUlC,KAAI,KAACzH,OAAO;AAAA,IACxBC,MAAM;AAAA,EACV,CAAC;AAEc0J,QAAAA,YAAAA,QAAQrB,OAAO;AAAA;AAAA;AAAA;AAAA,IAI1BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B5E,MAAMiG;AAAAA,UAC3C5D,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AClBD,QAAM6D,iBAAiBpC,KAAI,KAACzH,OAAO;AAAA,IAC/BC,MAAM;AAAA,EACV,CAAC;AAEc4J,QAAAA,mBAAAA,eAAevB,OAAO;AAAA;AAAA;AAAA;AAAA,IAIjCnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B5E,MAAMmG;AAAAA,UAC3C9D,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AClBD,QAAM+D,QAAQtC,KAAI,KAACzH,OAAO;AAAA,IACtBC,MAAM;AAAA,EACV,CAAC;AAEc8J,QAAAA,UAAAA,MAAMzB,OAAO;AAAA;AAAA;AAAA;AAAA,IAIxBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B5E,MAAMqG;AAAAA,UAC3ChE,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AClBD,QAAMiE,WAAWxC,KAAI,KAACzH,OAAO;AAAA,IACzBC,MAAM;AAAA,EACV,CAAC;AAEcgK,QAAAA,aAAAA,SAAS3B,OAAO;AAAA;AAAA;AAAA;AAAA,IAI3BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B5E,MAAMuG;AAAAA,UAC3ClE,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACnBD,QAAMmE,cAAc1C,KAAI,KAACzH,OAAO;AAAA,IAC5BC,MAAM;AAAA,EACV,CAAC;AAED,WAASmK,wBAAwBpG,MAAMC,QAAQC,OAAO;AAClD,QAAIqF,IAAI;AACR,WAAOrF,QAAQqF,IAAI,GAAGA,KAAK;AACvB,UAAItF,OAAOwF,MAAMvF,QAAQqF,IAAI,CAAC,EAAE1E,KAAK5E,SAAS+D,KAAKa,KAAK5E,MAAM;AAC1D;AAAA,MACJ;AAAA,IACJ;AACA,WAAOsJ;AAAAA,EACX;AAEeY,QAAAA,gBAAAA,YAAY7B,OAAO;AAAA;AAAA;AAAA;AAAA,IAI9BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAMC,QAAQC,OAAO;AAClC,kBAAMrB,QAAQmB,KAAKrC,MAAMkB,SAAS;AAClC,kBAAMwH,OAAOC,OAAOzH,QAAQmB,KAAKwF,aAAa,CAAC,EAAEnI;AACjD,kBAAMkJ,QAAQ7H,OAAM8H,OAAO,KAAKH,OAAO,CAAC;AACxC,kBAAMI,gBAAgBL,wBAAwBpG,MAAMC,QAAQC,KAAK;AACjE,kBAAMwG,YAAYD,gBAAgB,IAAI,OAAO;AAC7C/H,YAAAA,OAAMgG,WAAW1E,MAAMuG,OAAOhB,OAAK;AAC/B,oBAAMoB,OAAOL,OAAOzH,QAAQ0G,CAAC;AAC7B,qBAAO7G,OAAM8H,OAAO,KAAKH,OAAOM,KAAKtJ,MAAM,IAAIsJ,OAAOD;AAAAA,YAC1D,CAAC;AAAA,UACJ;AAAA,UACD1E,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACrCD,QAAM4E,YAAYnD,KAAI,KAACzH,OAAO;AAAA,IAC1BC,MAAM;AAAA,EACV,CAAC;AAEc2K,QAAAA,cAAAA,UAAUtC,OAAO;AAAA;AAAA;AAAA;AAAA,IAI5BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B5E,MAAMkH;AAAAA,UAC3C7E,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACpBM,WAASmB,WAAWnD,MAAM;AAAA,QAAA8G,uBAAAC;AAC7B,YAAAD,wBAAO9G,SAAI,QAAJA,SAAI+G,WAAAA,gBAAJ/G,KAAMgH,qBAAOD,kBAAA,SAAA,SAAbA,cAAeC,qBAAOF,0BAAA,SAAAA,wBAAI;EACrC;ACAA,QAAMG,QAAQxD,KAAI,KAACzH,OAAO;AAAA,IACtBC,MAAM;AAAA,EACV,CAAC;AAEcgL,QAAAA,UAAAA,MAAM3C,OAAO;AAAA;AAAA;AAAA;AAAA,IAIxBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAMC,QAAQ;AAC3B,gBAAG,CAACiH,uBAAuBlH,IAAI,GAAG;AAC9B0F,uBAASlE,QAAQJ,SAASC,UAAU9C,KAAK,MAAMG,QAAOsB,MAAMC,MAAM;AAClE;AAAA,YACJ;AACAvB,YAAAA,OAAMmB,UAAU;AAChBG,iBAAKmH,QAAQ,CAACC,KAAKC,GAAG9B,MAAM;AACxB7G,cAAAA,OAAMgF,MAAM,IAAI;AAChB0D,kBAAID,QAAQ,CAACG,KAAKD,IAAGE,MAAM;AACvB,oBAAGA,GAAG;AACF7I,kBAAAA,OAAMgF,MAAM,KAAK;AAAA,gBACrB;AACA,sBAAM8D,cAAcF,IAAIrE;AACxB,oBAAGuE,YAAY1C,YAAYX,QAAQ;AAC/BzF,kBAAAA,OAAM+I,aAAaD,WAAW;AAAA,gBAClC;AAAA,cACJ,CAAC;AACD9I,cAAAA,OAAMgF,MAAM,IAAI;AAChBhF,cAAAA,OAAMqG,cAAa;AACnB,kBAAG,CAACQ,GAAG;AACH,sBAAMmC,eAAeC,MAAMzI,KAAK;AAAA,kBAAC7B,QAAQ+J,IAAI5B;AAAAA,iBAAW,EAAEoC,IAAI,MAAM,KAAK,EAAEC,KAAK,KAAK;AACrFnJ,gBAAAA,OAAMgF,MAAO,KAAIgE,YAAa,IAAG;AACjChJ,gBAAAA,OAAMqG,cAAa;AAAA,cACvB;AAAA,YACJ,CAAC;AACDrG,YAAAA,OAAMmF,WAAW7D,IAAI;AACrBtB,YAAAA,OAAMmB,UAAU;AAAA,UACnB;AAAA,UACDmC,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AAGD,WAAS8F,QAAQ9H,MAAM;AACnB,WAAOA,KAAKrC,MAAMoK,UAAU,KAAK/H,KAAKrC,MAAMqK,UAAU;AAAA,EAC1D;AAEA,WAASd,uBAAuBlH,MAAM;AAClC,UAAMiI,OAAO9E,WAAWnD,IAAI;AAC5B,UAAMkI,WAAWD,KAAK,CAAC;AACvB,UAAME,WAAWF,KAAKG,MAAM,CAAC;AAE7B,QAAGjF,WAAW+E,QAAQ,EAAEpK,KAAKuK,UAAQA,KAAKxH,KAAK5E,SAAS,iBAAiB6L,QAAQO,IAAI,KAAKA,KAAK7C,aAAa,CAAC,GAAG;AAC5G,aAAO;AAAA,IACX;AAEA,QAAG2C,SAASrK,KAAKsJ,SACbjE,WAAWiE,GAAG,EAAEtJ,KAAKuK,UAAQA,KAAKxH,KAAK5E,SAAS,iBAAiB6L,QAAQO,IAAI,KAAKA,KAAK7C,aAAa,CAAC,CACzG,GAAG;AACC,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;ACrEA,QAAM8C,WAAW7E,KAAI,KAACzH,OAAO;AAAA,IACzBC,MAAM;AAAA,EACV,CAAC;AAEcqM,QAAAA,aAAAA,SAAShE,OAAO;AAAA;AAAA;AAAA;AAAA,IAI3BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAM;AACnB,kBAAMuI,QAAQvI,KAAKrC,MAAM6K,UAAU,QAAQ;AAC3C9J,YAAAA,OAAMgF,MAAO,GAAE6E,KAAM,GAAE;AACvB7J,YAAAA,OAAM+J,cAAczI,IAAI;AAAA,UAC3B;AAAA,UACDgC,OAAO;AAAA,YACHqD,UAAUzI,SAAS;AACf,eAAC,GAAGA,QAAQ8L,iBAAiB,iBAAiB,CAAC,EAC1CvB,QAAQwB,UAAQ;AACb,sBAAMC,QAAQD,KAAK7L,cAAc,OAAO;AACxC6L,qBAAKE,aAAa,aAAa,UAAU;AACzC,oBAAGD,OAAO;AACND,uBAAKE,aAAa,gBAAgBD,MAAMJ,OAAO;AAC/CI,wBAAMvF,OAAM;AAAA,gBAChB;AAAA,cACJ,CAAC;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA;IAER;AAAA,EACJ,CAAC;AC9BD,QAAMyF,WAAWrF,KAAI,KAACzH,OAAO;AAAA,IACzBC,MAAM;AAAA,EACV,CAAC;AAEc6M,QAAAA,aAAAA,SAASxE,OAAO;AAAA;AAAA;AAAA;AAAA,IAI3BnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWoD,aAAWjD,QAAQJ,SAASC;AAAAA,UACvCW,OAAO;AAAA,YACHgD,MAAMhH,aAAY;AACdA,cAAAA,YAAW+K,IAAIC,cAAc;AAAA,YAChC;AAAA,YACD3D,UAAUzI,SAAS;AACf,eAAC,GAAGA,QAAQ8L,iBAAiB,qBAAqB,CAAC,EAC9CvB,QAAQ8B,UAAQ;AACbA,qBAAKJ,aAAa,aAAa,UAAU;AAAA,cAC7C,CAAC;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA;IAER;AAAA,EACJ,CAAC;AC3BD,QAAMK,OAAOzF,KAAI,KAACzH,OAAO;AAAA,IACrBC,MAAM;AAAA,EACV,CAAC;AAEciN,QAAAA,SAAAA,KAAK5E,OAAO;AAAA;AAAA;AAAA;AAAA,IAIvBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,UAAU3C,QAAOsB,MAAM;AACnBtB,YAAAA,OAAMR,KAAKyE,WAAW3C,KAAK9B,IAAI,CAAC;AAAA,UACnC;AAAA,UACD8D,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACpBD,QAAMmH,OAAOjI,KAAI,KAAClF,OAAO;AAAA,IACrBC,MAAM;AAAA,EACV,CAAC;AAEckN,QAAAA,SAAAA,KAAK7E,OAAO;AAAA;AAAA;AAAA;AAAA,IAIvBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B3E,MAAMwJ;AAAAA,UAC3CpH,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AClBD,QAAMqH,OAAOnI,KAAI,KAAClF,OAAO;AAAA,IACrBC,MAAM;AAAA,EACV,CAAC;AAEcoN,QAAAA,SAAAA,KAAK/E,OAAO;AAAA;AAAA;AAAA;AAAA,IAIvBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B3E,MAAM0J;AAAAA,UAC3CtH,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AClBD,QAAMuH,SAASrI,KAAI,KAAClF,OAAO;AAAA,IACvBC,MAAM;AAAA,EACV,CAAC;AAEcsN,QAAAA,WAAAA,OAAOjF,OAAO;AAAA;AAAA;AAAA;AAAA,IAIzBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B3E,MAAM4J;AAAAA,UAC3CxH,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;AClBD,QAAMyH,OAAOvI,KAAI,KAAClF,OAAO;AAAA,IACrBC,MAAM;AAAA,EACV,CAAC;AAEcwN,QAAAA,SAAAA,KAAKnF,OAAO;AAAA;AAAA;AAAA;AAAA,IAIvBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAWkD,oBAAAA,0BAA0B3E,MAAM8J;AAAAA,UAC3C1H,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACnBD,QAAM2H,SAASzI,KAAI,KAAClF,OAAO;AAAA,IACvBC,MAAM;AAAA,EACV,CAAC;AAEc0N,QAAAA,WAAAA,OAAOrF,OAAO;AAAA;AAAA;AAAA;AAAA,IAIzBnD,aAAa;AACT,aAAO;AAAA,QACHC,UAAU;AAAA,UACNC,WAAW;AAAA,YAACV,MAAM;AAAA,YAAMkB,OAAO;AAAA,YAAMf,0BAA0B;AAAA,UAAK;AAAA,UACpEkB,OAAO;AAAA;AAAA,UACH;AAAA,QAER;AAAA;IAER;AAAA,EACJ,CAAC;ACGD,QAAA,qBAAe,CACXqC,cACAI,cACAG,aACAU,aACAK,WACAE,kBACAH,UACAK,SACAE,YACAE,eACAS,aACAK,SACAqB,YACAQ,YACAI,QAEAC,QACAE,QACAO,UACAL,UACAE,QACAE,QAAM;AC3CH,WAASE,gBAAgBC,WAAW;AAAA,QAAAC,oBAAAC;AACvC,UAAMC,gBAAYF,qBAAGD,UAAUtI,aAAO,QAAAuI,uBAAA,SAAA,SAAjBA,mBAAmB3I;AACxC,UAAM8I,uBAAmBF,wBAAGG,mBAAmBC,KAAKC,OAAKA,EAAEpO,SAAS6N,UAAU7N,IAAI,OAAC,QAAA+N,0BAAA,SAAA,SAAvDA,sBAAyDxI,QAAQJ;AAE7F,QAAG6I,gBAAgBC,qBAAqB;AACpC,aAAO;AAAA,QACH,GAAGA;AAAAA,QACH,GAAGD;AAAAA;IAEX;AAEA,WAAO;AAAA,EACX;AAAA,ECRO,MAAMK,mBAAmB;AAAA,IAM5B5K,YAAYlC,QAAQ;AAFpBA;AAAAA;AAAAA;AAAAA,oCAAS;AAGL,WAAKA,SAASA;AAAAA,IAClB;AAAA,IAEA6D,UAAU2F,SAAS;AACf,YAAMtI,SAAQ,IAAIc,wBAAwB,KAAKG,OAAO,KAAKC,OAAO;AAAA,QAC9D2K,mBAAmBjF,YAAUrJ;AAAAA,MACjC,CAAC;AAEDyC,MAAAA,OAAM+J,cAAczB,OAAO;AAE3B,aAAOtI,OAAM6B;AAAAA,IACjB;AAAA,IAEA,IAAIZ,QAAQ;AAAA,UAAA6K;AACR,aAAO;AAAA,QACH,GAAGC,OAAOC,YACND,OAAOE,KAAK,KAAKnN,OAAOyE,OAAOtC,KAAK,EAC/BiI,IAAI3L,UAAQ,CAACA,MAAM,KAAK2O,cAAclF,QAAQ,CAAC,CAAC,CACzD;AAAA,QACA,GAAG+E,OAAOC,aAAWF,wBACjB,KAAKhN,OAAOqN,iBAAiBC,WACxBC,OAAOjB,eAAaA,UAAUjJ,SAAS,UAAU,KAAK+J,cAAcd,SAAS,CAAC,EAC9ElC,IAAIkC,eAAa,CAACA,UAAU7N,MAAM,KAAK2O,cAAcd,SAAS,CAAC,CAAC,OAAC,QAAAU,0BAAA,SAAAA,wBACnE,EACP;AAAA;IAER;AAAA,IAEA,IAAI5K,QAAQ;AAAA,UAAAoL;AACR,aAAO;AAAA,QACH,GAAGP,OAAOC,YACND,OAAOE,KAAK,KAAKnN,OAAOyE,OAAOrC,KAAK,EAC/BgI,IAAI3L,UAAQ,CAACA,MAAM,KAAKgP,cAAcrB,QAAQ,CAAC,CAAC,CACzD;AAAA,QACA,GAAGa,OAAOC,aAAWM,yBACjB,KAAKxN,OAAOqN,iBAAiBC,WACxBC,OAAOjB,eAAaA,UAAUjJ,SAAS,UAAU,KAAKoK,cAAcnB,SAAS,CAAC,EAC9ElC,IAAIkC,eAAa,CAACA,UAAU7N,MAAM,KAAKgP,cAAcnB,SAAS,CAAC,CAAC,OAAC,QAAAkB,2BAAA,SAAAA,yBACnE,EACP;AAAA;IAER;AAAA,IAEAJ,cAAc5K,MAAM;AAAA,UAAAkL;AAChB,cAAAA,mBAAOrB,gBAAgB7J,IAAI,OAAC,QAAAkL,qBAAAA,WAAAA,mBAArBA,iBAAuB7J,eAAS6J,QAAAA,uCAAhCA,iBAAkCC,KAAK;AAAA,QAAE3N,QAAQ,KAAKA;AAAAA,QAAQhB,SAASwD,KAAKxD;AAAAA,MAAQ,CAAC;AAAA,IAChG;AAAA,IAEAyO,cAAcvK,MAAM;AAAA,UAAA0K;AAChB,YAAM/J,aAAS+J,oBAAGvB,gBAAgBnJ,IAAI,OAAC0K,QAAAA,sBAArBA,SAAAA,SAAAA,kBAAuB/J;AACzC,aAAOA,YACD;AAAA,QACE,GAAGA;AAAAA,QACHV,MAAM,OAAOU,UAAUV,SAAS,aAAaU,UAAUV,KAAKwK,KAAK;AAAA,UAAE3N,QAAQ,KAAKA;AAAAA,UAAQhB,SAASkE,KAAKlE;AAAAA,QAAQ,CAAC,IAAI6E,UAAUV;AAAAA,QAC7HkB,OAAO,OAAOR,UAAUQ,UAAU,aAAaR,UAAUQ,MAAMsJ,KAAK;AAAA,UAAE3N,QAAQ,KAAKA;AAAAA,UAAQhB,SAASkE,KAAKlE;AAAAA,SAAS,IAAI6E,UAAUQ;AAAAA,MACnI,IACC;AAAA,IACV;AAAA,EACJ;AAAA,ECnEO,MAAMwJ,eAAe;AAAA,IAUxB3L,YAAYlC,QAAMD,MAA6B;AAN/CC;AAAAA;AAAAA;AAAAA,oCAAS;AAITO;AAAAA;AAAAA;AAAAA,gCAAK;AAE0C,UAA3B;AAAA,QAAE0D;AAAAA,QAAM6J;AAAAA,QAASC;AAAAA,MAAQ,IAAAhO;AACzC,WAAKC,SAASA;AACd,WAAKO,KAAK,KAAKyN,oBAAoBxN,WAAW;AAAA,QAC1CyD;AAAAA,QACA6J;AAAAA,QACAC;AAAAA,MACH,CAAA,CAAC;AAAA,IACN;AAAA,IAEAvJ,MAAMgF,SAA0B;AAAA,UAAjB;AAAA,QAAE5I;AAAAA,MAAQ,IAAAhB,UAAAC,SAAAD,KAAAA,UAAAE,CAAAA,MAAAA,SAAAF,UAAG,CAAA,IAAA;AACxB,UAAG,OAAO4J,YAAY,UAAU;AAC5B,aAAKxJ,OAAOqN,iBAAiBC,WAAW3D,QAAQ2C,eAAS;AAAA,cAAAoB;AAAA,kBAAAA,mBACrDrB,gBAAgBC,SAAS,OAACoB,QAAAA,qBAAAA,WAAAA,mBAA1BA,iBAA4BlJ,WAAKkJ,QAAAA,qBAAAA,WAAAA,mBAAjCA,iBAAmClG,WAAKkG,QAAAA,qBAAxCA,SAAAA,SAAAA,iBAA0C3M,KAAK;AAAA,YAAEf,QAAO,KAAKA;AAAAA,YAAQhB,SAAQsN,UAAUtN;AAAAA,UAAQ,GAAG,KAAKuB,EAAE;AAAA,QAAC,CAC9G;AAEA,cAAM0N,eAAe,KAAK1N,GAAGgC,OAAOiH,OAAO;AAC3C,cAAMpK,UAAUwF,kBAAkBqJ,YAAY;AAE9C,aAAKjO,OAAOqN,iBAAiBC,WAAW3D,QAAQ2C,eAAS;AAAA,cAAAsB;AAAA,kBAAAA,oBACrDvB,gBAAgBC,SAAS,OAACsB,QAAAA,sBAAAA,WAAAA,oBAA1BA,kBAA4BpJ,WAAKoJ,QAAAA,sBAAAA,WAAAA,oBAAjCA,kBAAmC/F,eAAS+F,QAAAA,sBAA5CA,SAAAA,SAAAA,kBAA8C7M,KAAK;AAAA,YAAEf,QAAO,KAAKA;AAAAA,YAAQhB,SAAQsN,UAAUtN;AAAAA,UAAS,GAAEI,OAAO;AAAA,QAAC,CAClH;AAEA,aAAK8O,aAAa9O,SAAS;AAAA,UAAEwB;AAAAA,UAAQ4I;AAAAA,QAAQ,CAAC;AAE9C,eAAOpK,QAAQsH;AAAAA,MACnB;AAEA,aAAO8C;AAAAA,IACX;AAAA,IAEA0E,aAAa1L,MAAI2L,OAAuB;AAAA,UAArB;AAAA,QAAEvN;AAAAA,QAAQ4I;AAAAA,MAAS,IAAA2E;AAClC,WAAKC,gBAAgB5L,IAAI;AAGzBA,WAAK0I,iBAAiB,GAAG,EAAEvB,QAAQ0E,QAAM;AAAA,YAAAC;AACrC,cAAGA,kBAAAD,GAAGE,iBAAWD,QAAAA,oBAAdA,SAAAA,SAAAA,gBAAgBE,cAAavI,KAAKwI,aAAa,CAACJ,GAAGK,QAAQ,KAAK,GAAG;AAClEL,aAAGE,YAAYjH,cAAc+G,GAAGE,YAAYjH,YAAYlC,QAAQ,OAAO,EAAE;AAAA,QAC7E;AAAA,MACJ,CAAC;AAED,UAAGxE,QAAQ;AACP,aAAKkC,gBAAgBN,MAAMgH,OAAO;AAAA,MACtC;AAEA,aAAOhH;AAAAA,IACX;AAAA,IAEA4L,gBAAgB5L,MAAM;AAClB,YAAMmM,SAAS1B,OAAO2B,OAAO,KAAK5O,OAAOyE,OAAOtC,KAAK,EAChDoL,OAAO/K,CAAAA,UAAQA,MAAK4D,OAAO;AAEhC,YAAMyI,WAAWF,OACZvE,IAAI0E,WAAK;AAAA,YAAAC;AAAA,gBAAAA,uBAAID,MAAME,KAAKC,cAAQF,QAAAA,2CAAnBA,qBAAqB3E,IAAI4E,UAAQA,KAAKE,GAAG;AAAA,MAAC,CAAA,EACvDC,KAAI,EACJ5B,OAAO6B,OAAO,EACd/E,KAAK,GAAG;AAEb,UAAG,CAACwE,UAAU;AACV;AAAA,MACJ;AAEA,OAAC,GAAGrM,KAAK0I,iBAAiB2D,QAAQ,CAAC,EAAElF,QAAQ0E,QAAM;AAC/C,YAAGA,GAAG/I,cAAc+J,QAAQ,GAAG,GAAG;AAC9BhK,yBAAegJ,EAAE;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IAEAvL,gBAAgBN,MAAMgH,SAAS;AAAA,UAAA8F;AAC3B,WAAAA,wBAAG9M,KAAKiE,uBAAiB6I,QAAAA,0BAAtBA,UAAAA,sBAAwBD,QAAQ,GAAG,GAAG;AAAA,YAAAE,iBAAAC,gBAAAC,kBAAAC;AACrC,cAAMC,iBAAiBnN,KAAKiE;AAC5B,cAAM;AAAA,UAAEmJ;AAAAA,QAAoB,IAAGD;AAC/B,cAAME,eAAWN,mBAAAC,iBAAGhG,QAAQ/F,MAAM,MAAM,eAAC+L,mBAAA,SAAA,SAArBA,eAAwB,CAAC,eAACD,oBAAA,SAAAA,kBAAI;AAClD,cAAMO,YAAY,CAACF,sBAAkBH,oBAAAC,kBAC/BlG,QAAQ/F,MAAM,MAAM,OAAC,QAAAiM,oBAAA,SAAA,SAArBA,gBAAwB,CAAC,OAACD,QAAAA,qBAAAA,SAAAA,mBAAI,KAC9B;AAEN,YAAGjG,QAAQ/F,MAAM,OAAO,GAAG;AACvBkM,yBAAejJ,YAAa,GAAEiJ,eAAejJ,SAAU,GAAEoJ,SAAU;AACnE;AAAA,QACJ;AAEAhK,sBAAc6J,cAAc;AAE5BnN,aAAKkE,YAAa,GAAEmJ,WAAY,GAAErN,KAAKkE,SAAU,GAAEoJ,SAAU;AAAA,MACjE;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA,IAKA9B,oBAAoBzN,KAAI;AACpB,YAAMwP,iBAAkBC,cAAa,WAAa;AAC9C,cAAMC,WAAWD,SAAS,GAAApQ,SAAO;AACjC,YAAGqQ,aAAa,MAAM;AAClB,iBAAOA;AAAAA,QACX;AACA,YAAGA,SAASA,SAASpQ,SAAS,CAAC,MAAM,MAAM;AACvC,iBAAOoQ,SAASrF,MAAM,GAAG,EAAE;AAAA,QAC/B;AACA,eAAOqF;AAAAA;AAGX1P,MAAAA,IAAGyP,SAASE,MAAMC,YAAYJ,eAAexP,IAAGyP,SAASE,MAAMC,SAAS;AACxE5P,MAAAA,IAAGyP,SAASE,MAAME,YAAYL,eAAexP,IAAGyP,SAASE,MAAME,SAAS;AACxE7P,MAAAA,IAAGyP,SAASE,MAAMG,QAAQN,eAAexP,IAAGyP,SAASE,MAAMG,KAAK;AAChE9P,MAAAA,IAAGyP,SAASE,MAAMI,aAAaP,eAAexP,IAAGyP,SAASE,MAAMI,UAAU;AAC1E/P,MAAAA,IAAGyP,SAASO,cAAcR,eAAexP,IAAGyP,SAASO,YAAY5C,KAAKpN,IAAGyP,QAAQ,CAAC;AAElF,aAAOzP;AAAAA,IACX;AAAA,EACJ;ACxHO,QAAMiQ,oBAAoBjS,KAAS,UAACC,OAAO;AAAA,IAC9CC,MAAM;AAAA,IACNC,aAAa;AACT,aAAO;AAAA,QACH+R,qBAAqB;AAAA,QACrBC,qBAAqB;AAAA;IAE5B;AAAA,IACDC,wBAAwB;AACpB,aAAO,CACH,IAAIC,MAAAA,OAAO;AAAA,QACPC,KAAK,IAAIC,MAAS,UAAC,mBAAmB;AAAA,QACtCC,OAAO;AAAA,UACHC,qBAAqBA,CAACtQ,MAAMuQ,SAASC,cAAc;AAC/C,gBAAGA,aAAa,CAAC,KAAKlS,QAAQyR,qBAAqB;AAC/C,qBAAO;AAAA,YACX;AACA,kBAAMU,SAAS,KAAKnR,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAM9D,MAAM;AAAA,cAAEE,QAAQ;AAAA,YAAK,CAAC;AAC/E,mBAAOoE,MAAS,UAACqM,WAAW,KAAKrR,OAAOyE,MAAM,EACzC6M,WAAW1M,kBAAkBuM,MAAM,GAAG;AAAA,cACnCI,oBAAoB;AAAA,cACpBN;AAAAA,YACJ,CAAC;AAAA,UACR;AAAA;AAAA;AAAA;AAAA,UAIDO,yBAA0B5G,WAAU;AAChC,gBAAG,CAAC,KAAK5L,QAAQ0R,qBAAqB;AAClC,qBAAO;AAAA,YACX;AACA,mBAAO,KAAK1Q,OAAOgE,QAAQJ,SAAS6N,WAAW5N,UAAU+G,MAAMpB,OAAO;AAAA,UAC1E;AAAA,QACJ;AAAA,MACH,CAAA,CAAC;AAAA,IAEV;AAAA,EACJ,CAAC;QCpCYkI,WAAWnT,KAAS,UAACC,OAAO;AAAA,IACrCC,MAAM;AAAA,IACNkT,UAAU;AAAA,IACVjT,aAAa;AACT,aAAO;AAAA,QACHuF,MAAM;AAAA,QACN2N,YAAY;AAAA,QACZC,gBAAgB;AAAA,QAChB1K,kBAAkB;AAAA,QAClB2G,SAAS;AAAA,QACTC,QAAQ;AAAA,QACR0C,qBAAqB;AAAA,QACrBC,qBAAqB;AAAA;IAE5B;AAAA,IACDjR,cAAc;AACV,YAAMQ,WAAWqN,KAAU,WAACwE,SAASC,OAAOtS,YAAW;AACvD,aAAO;AAAA,QACHuS,YAAYA,CAACxI,SAASyI,YAAYC,iBAAkBnB,WAAU;AAC1D,iBAAO9Q,SAAS+R,WACZjB,MAAM/Q,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAMgF,OAAO,GAClDyI,YACAC,YACJ,EAAEnB,KAAK;AAAA,QACV;AAAA,QACDoB,iBAAiBA,CAACC,OAAO5I,SAASxK,YAAa+R,WAAU;AACrD,iBAAO9Q,SAASkS,gBACZC,OACArB,MAAM/Q,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAMgF,SAAS;AAAA,YAAE5I,QAAQ;AAAA,UAAK,CAAC,GACpE5B,OACJ,EAAE+R,KAAK;AAAA,QACX;AAAA;IAEP;AAAA,IACDsB,iBAAiB;AACb,WAAKrS,OAAOgE,QAAQJ,WAAW;AAAA,QAC3B5E,SAAS;AAAA,UAAE,GAAG,KAAKA;AAAAA,QAAS;AAAA,QAC5BoS,QAAQ,IAAIvD,eAAe,KAAK7N,QAAQ,KAAKhB,OAAO;AAAA,QACpDyS,YAAY,IAAI3E,mBAAmB,KAAK9M,MAAM;AAAA,QAC9CsS,aAAaA,MAAM;AACf,iBAAO,KAAKtS,OAAOgE,QAAQJ,SAAS6N,WAAW5N,UAAU,KAAK7D,OAAOkB,MAAMqR,GAAG;AAAA,QAClF;AAAA;AAEJ,WAAKvS,OAAOhB,QAAQwT,iBAAiB,KAAKxS,OAAOhB,QAAQwK;AACzD,WAAKxJ,OAAOhB,QAAQwK,UAAU,KAAKxJ,OAAOgE,QAAQJ,SAASwN,OAAO5M,MAAM,KAAKxE,OAAOhB,QAAQwK,OAAO;AAAA,IACtG;AAAA,IACDiJ,WAAW;AACP,WAAKzS,OAAOhB,QAAQwK,UAAU,KAAKxJ,OAAOhB,QAAQwT;AAClD,aAAO,KAAKxS,OAAOhB,QAAQwT;AAAAA,IAC9B;AAAA,IACD7O,aAAa;AACT,aAAO;AAAA;AAAA;IAGV;AAAA,IACD+O,gBAAgB;AACZ,aAAO,CACHpU,mBAAmBqU,UAAU;AAAA,QACzBhU,OAAO,KAAKK,QAAQ4S;AAAAA,QACpBhT,YAAY,KAAKI,QAAQ6S;AAAAA,MAC7B,CAAC,GACDrB,kBAAkBmC,UAAU;AAAA,QACxBlC,qBAAqB,KAAKzR,QAAQyR;AAAAA,QAClCC,qBAAqB,KAAK1R,QAAQ0R;AAAAA,MACrC,CAAA,CAAC;AAAA,IAEV;AAAA,EACJ,CAAC;;;;"}