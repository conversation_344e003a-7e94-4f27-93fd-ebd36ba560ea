# Tiptap 列表对齐功能修复

## 问题描述

在 Tiptap 编辑器中存在以下列表对齐问题：

1. **双重对齐识别问题**：当对列表应用对齐样式时，编辑器同时识别并应用了两种对齐方式，导致样式冲突
2. **内容强制换行问题**：当从左对齐切换到居中对齐时，列表内容出现不正常的强制换行现象
3. **序号/符号位置问题**：有序列表和无序列表的序号/符号没有跟随文本一起对齐，而是仍然保持在左侧位置

## 根本原因分析

### 1. 双重对齐识别问题
- 原始代码中 `ul { padding: 0 1rem; }` 会应用到所有无序列表
- 然后特定对齐样式又会覆盖部分属性，导致样式冲突和不一致的行为
- CSS 选择器优先级不明确，导致多个规则同时生效

### 2. 内容强制换行问题
- `list-style-position: inside` 与 `text-align: center` 组合时，序号会占用内容空间
- 缺少对 `white-space` 和 `word-wrap` 的控制
- 列表项内的段落元素（`<p>`）默认为块级元素，导致额外的换行
- 容器宽度限制和 flexbox 布局冲突

### 3. 序号/符号位置问题
- 原生 `list-style-position` 在居中和右对齐时表现不佳
- 缺少对不同对齐方式的专门处理

## 修复方案

### 1. 扩展 TextAlign 配置

**文件**: `web/src/utils/tiptap.ts`

将 TextAlign 扩展的 `types` 配置从：
```typescript
types: ['heading', 'paragraph', 'blockquote']
```

修改为：
```typescript
types: ['heading', 'paragraph', 'blockquote', 'listItem', 'bulletList', 'orderedList', 'taskList']
```

这样可以让列表元素也能接收文本对齐属性。

### 2. 重构列表样式系统

**文件**: `web/src/components/tiptap/core/styles/lists.scss`

#### 2.1 解决双重对齐识别问题
- 使用更高优先级的选择器 `ul:not([data-type='taskList'])` 避免与任务列表冲突
- 移除通用的 `ul { padding: 0 1rem; }` 规则，改为在具体对齐规则中设置
- 确保每个对齐方式都有明确的、不冲突的样式规则

#### 2.2 解决内容强制换行问题
```scss
li {
  // 防止内容强制换行
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;

  p {
    // 确保段落不会导致额外换行
    display: inline;
    margin-top: 0.15em;
    margin-bottom: 0.15em;
  }
}
```

#### 2.3 使用伪元素解决序号/符号位置问题
对于居中和右对齐，不再使用 `list-style-position: inside`，而是：
- 设置 `list-style: none` 移除原生样式
- 使用 `::before` 或 `::after` 伪元素创建自定义序号/符号
- 通过绝对定位精确控制序号/符号位置

#### 2.4 无序列表对齐样式（重构后）
```scss
// 默认样式（左对齐或无对齐属性）
ul:not([data-type='taskList']) {
  padding-left: 1rem;
  padding-right: 0;
  list-style-position: outside;
  list-style-type: disc;

  // 居中对齐 - 使用伪元素避免换行问题
  &[style*="text-align: center"] {
    padding-left: 0;
    padding-right: 0;
    text-align: center;
    list-style: none; // 移除默认样式

    li {
      text-align: center;
      position: relative;
      padding-left: 1.2em;

      // 使用伪元素创建居中的项目符号
      &::before {
        content: '•';
        position: absolute;
        left: 0;
        width: 1.2em;
        text-align: center;
        color: inherit;
      }
    }
  }

  // 右对齐 - 使用伪元素避免换行问题
  &[style*="text-align: right"] {
    padding-left: 0;
    padding-right: 1rem;
    text-align: right;
    list-style: none; // 移除默认样式

    li {
      text-align: right;
      position: relative;
      padding-right: 1.2em;

      // 使用伪元素创建右对齐的项目符号
      &::after {
        content: '•';
        position: absolute;
        right: 0;
        width: 1.2em;
        text-align: center;
        color: inherit;
      }
    }
  }
}
```

#### 2.5 有序列表对齐样式（重构后）
```scss
ol {
  // 默认样式
  padding-left: 1.25rem;
  padding-right: 0;
  list-style-position: outside;
  list-style-type: decimal;
  counter-reset: list-counter;

  // 居中对齐 - 使用自定义计数器避免换行
  &[style*="text-align: center"] {
    padding-left: 0;
    padding-right: 0;
    text-align: center;
    list-style: none;
    counter-reset: list-counter;

    li {
      text-align: center;
      position: relative;
      padding-left: 2em;
      counter-increment: list-counter;

      // 使用伪元素创建居中的序号
      &::before {
        content: counter(list-counter) '.';
        position: absolute;
        left: 0;
        width: 2em;
        text-align: center;
        color: inherit;
        font-weight: inherit;
      }
    }
  }

  // 右对齐 - 使用自定义计数器避免换行
  &[style*="text-align: right"] {
    padding-left: 0;
    padding-right: 1.25rem;
    text-align: right;
    list-style: none;
    counter-reset: list-counter;

    li {
      text-align: right;
      position: relative;
      padding-right: 2em;
      counter-increment: list-counter;

      // 使用伪元素创建右对齐的序号
      &::after {
        content: '.' counter(list-counter);
        position: absolute;
        right: 0;
        width: 2em;
        text-align: center;
        color: inherit;
        font-weight: inherit;
      }
    }
  }
}
```

#### 2.6 任务列表对齐样式（重构后）
```scss
ul[data-type='taskList'] {
  list-style: none;
  margin-left: 0;
  padding: 0;

  li {
    align-items: flex-start;
    display: flex;
    // 防止内容强制换行
    white-space: normal;
    word-wrap: break-word;

    > div {
      flex: 1 1 auto;
      min-width: 0; // 防止 flex 子元素溢出
    }
  }

  // 居中对齐
  &[style*="text-align: center"] {
    li {
      justify-content: center;
      text-align: center;

      > div {
        text-align: center;
      }
    }
  }

  // 右对齐
  &[style*="text-align: right"] {
    li {
      justify-content: flex-end;
      text-align: right;

      > div {
        text-align: right;
      }
    }
  }
}
```

## 实现原理

### 关键技术点

1. **CSS 选择器优先级管理**：
   - 使用 `ul:not([data-type='taskList'])` 避免与任务列表样式冲突
   - 明确的选择器优先级，确保每个对齐方式只应用一套样式规则
   - 移除通用样式规则，改为在具体对齐规则中设置

2. **伪元素技术**：
   - 居中和右对齐使用 `::before` 和 `::after` 伪元素创建序号/符号
   - 通过绝对定位精确控制序号/符号位置
   - 避免了 `list-style-position: inside` 导致的换行问题

3. **CSS 计数器**（有序列表）：
   - 使用 `counter-reset` 和 `counter-increment` 创建自定义序号
   - 通过 `content: counter(list-counter) '.'` 生成序号内容
   - 保持序号的连续性和正确性

4. **防换行处理**：
   - `white-space: normal` + `word-wrap: break-word` 控制文本换行行为
   - `display: inline` 确保段落元素不会导致额外换行
   - `min-width: 0` 防止 flex 子元素溢出

5. **Flexbox 布局优化**（任务列表）：
   - 使用 `justify-content` 属性控制任务列表项的对齐方式
   - `min-width: 0` 防止内容溢出容器

## 测试方法

### 1. 使用现有编辑器
在 ArticleModal.vue 中测试（已配置 `:all-extensions="true"`）：
1. 创建有序列表、无序列表、任务列表
2. 选中列表，使用工具栏的对齐按钮
3. 观察序号/符号是否跟随文本对齐
4. 检查是否有强制换行问题

### 2. 使用测试页面
运行测试文件：`web/src/components/tiptap/test/ListAlignmentTest.vue`

### 3. 使用静态测试页面
打开：`web/src/test-list-alignment.html`（已更新，包含更全面的测试用例）

### 4. 复现步骤
1. **双重对齐识别问题复现**：
   - 创建一个列表
   - 快速切换不同对齐方式
   - 观察是否有样式冲突或闪烁

2. **内容强制换行问题复现**：
   - 创建包含较长文本的列表项
   - 从左对齐切换到居中对齐
   - 观察文本是否出现不正常的换行

3. **序号/符号位置问题复现**：
   - 创建有序列表和无序列表
   - 应用居中和右对齐
   - 观察序号/符号是否正确跟随对齐

## 预期效果

### 修复后的正确行为

1. **左对齐（默认）**：
   - 序号/符号在左侧，内容左对齐
   - 保持原有的显示效果
   - 无样式冲突

2. **居中对齐**：
   - 序号/符号和内容都居中显示
   - 无强制换行问题
   - 长文本正常显示在同一行

3. **右对齐**：
   - 序号/符号和内容都右对齐显示
   - 序号/符号位置正确
   - 无内容溢出问题

4. **两端对齐**：
   - 序号/符号在左侧，内容两端对齐
   - 保持良好的可读性

### 解决的问题

- ✅ **双重对齐识别问题**：每个列表元素只应用一种对齐样式，无冲突
- ✅ **内容强制换行问题**：长文本在居中对齐时不会出现不正常换行
- ✅ **序号/符号位置问题**：序号/符号正确跟随文本对齐方式

## 兼容性说明

- ✅ 修改不会影响现有功能
- ✅ 保持了原有的左对齐效果
- ✅ 支持嵌套列表
- ✅ 兼容只读模式
- ✅ 兼容任务列表的自定义 DOM 结构
- ✅ 遵循项目代码规范，未使用 `!important`
- ✅ 支持混合格式文本（粗体、斜体等）
- ✅ 响应式设计友好
