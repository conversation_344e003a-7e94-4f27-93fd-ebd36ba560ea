# Tiptap 列表对齐功能修复

## 问题描述

在 Tiptap 编辑器中，当文本设置为居中对齐或右对齐时，有序列表和无序列表的序号/符号没有跟随文本一起对齐，而是仍然保持在左侧位置。

## 修复方案

### 1. 扩展 TextAlign 配置

**文件**: `web/src/utils/tiptap.ts`

将 TextAlign 扩展的 `types` 配置从：
```typescript
types: ['heading', 'paragraph', 'blockquote']
```

修改为：
```typescript
types: ['heading', 'paragraph', 'blockquote', 'listItem', 'bulletList', 'orderedList', 'taskList']
```

这样可以让列表元素也能接收文本对齐属性。

### 2. 增强列表样式

**文件**: `web/src/components/tiptap/core/styles/lists.scss`

#### 2.1 通用列表对齐样式
为所有列表添加了对齐样式继承：
```scss
// 列表项对齐样式继承
&[style*="text-align: center"] li {
  text-align: center;
}

&[style*="text-align: right"] li {
  text-align: right;
}

&[style*="text-align: justify"] li {
  text-align: justify;
}
```

#### 2.2 无序列表对齐样式
```scss
// 左对齐（默认）
&[style*="text-align: left"],
&:not([style*="text-align"]) {
  padding-left: 1rem;
  padding-right: 0;
}

// 居中对齐
&[style*="text-align: center"] {
  padding-left: 0;
  padding-right: 0;
  list-style-position: inside;
  text-align: center;
}

// 右对齐
&[style*="text-align: right"] {
  padding-left: 0;
  padding-right: 1rem;
  list-style-position: inside;
  text-align: right;
}

// 两端对齐
&[style*="text-align: justify"] {
  padding-left: 1rem;
  padding-right: 0;
  text-align: justify;
}
```

#### 2.3 有序列表对齐样式
```scss
// 左对齐（默认）
&[style*="text-align: left"],
&:not([style*="text-align"]) {
  padding-left: 1.25rem;
  padding-right: 0;
  list-style-position: outside;
}

// 居中对齐
&[style*="text-align: center"] {
  padding-left: 0;
  padding-right: 0;
  list-style-position: inside;
  text-align: center;
}

// 右对齐
&[style*="text-align: right"] {
  padding-left: 0;
  padding-right: 1.25rem;
  list-style-position: inside;
  text-align: right;
}

// 两端对齐
&[style*="text-align: justify"] {
  padding-left: 1.25rem;
  padding-right: 0;
  list-style-position: outside;
  text-align: justify;
}
```

#### 2.4 任务列表对齐样式
```scss
// 任务列表对齐样式
&[style*="text-align: center"] {
  text-align: center;
  
  li {
    justify-content: center;
  }
}

&[style*="text-align: right"] {
  text-align: right;
  
  li {
    justify-content: flex-end;
  }
}

&[style*="text-align: justify"] {
  text-align: justify;
}
```

## 实现原理

### 关键技术点

1. **list-style-position 属性**：
   - `outside`（默认）：序号/符号显示在内容区域外
   - `inside`：序号/符号显示在内容区域内，可以跟随文本对齐

2. **padding 调整**：
   - 左对齐：使用左侧 padding
   - 居中/右对齐：移除左侧 padding，使用 `list-style-position: inside`
   - 右对齐：添加右侧 padding

3. **Flexbox 布局**（任务列表）：
   - 使用 `justify-content` 属性控制任务列表项的对齐方式

## 测试方法

### 1. 使用现有编辑器
在 ArticleModal.vue 中测试（已配置 `:all-extensions="true"`）：
1. 创建有序列表、无序列表、任务列表
2. 选中列表，使用工具栏的对齐按钮
3. 观察序号/符号是否跟随文本对齐

### 2. 使用测试页面
运行测试文件：`web/src/components/tiptap/test/ListAlignmentTest.vue`

### 3. 使用静态测试页面
打开：`web/src/test-list-alignment.html`

## 预期效果

- **左对齐（默认）**：序号/符号在左侧，内容左对齐
- **居中对齐**：序号/符号和内容都居中显示
- **右对齐**：序号/符号和内容都右对齐显示
- **两端对齐**：序号/符号在左侧，内容两端对齐

## 兼容性说明

- 修改不会影响现有功能
- 保持了原有的左对齐效果
- 支持嵌套列表
- 兼容只读模式
- 遵循项目代码规范，未使用 `!important`
