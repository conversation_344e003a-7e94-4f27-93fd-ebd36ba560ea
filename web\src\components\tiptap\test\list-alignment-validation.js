/**
 * Tiptap 列表对齐功能验证脚本
 * 用于自动化测试列表对齐功能是否正常工作
 */

// 测试用例配置
const testCases = [
  {
    name: '有序列表左对齐',
    listType: 'ol',
    alignment: 'left',
    expectedStyles: {
      'padding-left': '1.25rem',
      'list-style-position': 'outside',
      'text-align': 'left'
    }
  },
  {
    name: '有序列表居中对齐',
    listType: 'ol',
    alignment: 'center',
    expectedStyles: {
      'padding-left': '0px',
      'list-style': 'none',
      'text-align': 'center'
    }
  },
  {
    name: '有序列表右对齐',
    listType: 'ol',
    alignment: 'right',
    expectedStyles: {
      'padding-right': '1.25rem',
      'list-style': 'none',
      'text-align': 'right'
    }
  },
  {
    name: '无序列表左对齐',
    listType: 'ul',
    alignment: 'left',
    expectedStyles: {
      'padding-left': '1rem',
      'list-style-position': 'outside',
      'text-align': 'left'
    }
  },
  {
    name: '无序列表居中对齐',
    listType: 'ul',
    alignment: 'center',
    expectedStyles: {
      'padding-left': '0px',
      'list-style': 'none',
      'text-align': 'center'
    }
  },
  {
    name: '无序列表右对齐',
    listType: 'ul',
    alignment: 'right',
    expectedStyles: {
      'padding-right': '1rem',
      'list-style': 'none',
      'text-align': 'right'
    }
  }
]

/**
 * 创建测试列表元素
 */
function createTestList(listType, alignment) {
  const list = document.createElement(listType)
  list.className = 'test-list'
  list.style.textAlign = alignment
  
  // 添加测试项目
  for (let i = 1; i <= 3; i++) {
    const li = document.createElement('li')
    li.textContent = `测试项目 ${i} - 这是一个较长的文本用于测试对齐效果和换行行为`
    list.appendChild(li)
  }
  
  return list
}

/**
 * 获取元素的计算样式
 */
function getComputedStyles(element, properties) {
  const computedStyle = window.getComputedStyle(element)
  const styles = {}
  
  properties.forEach(prop => {
    styles[prop] = computedStyle.getPropertyValue(prop)
  })
  
  return styles
}

/**
 * 验证样式是否符合预期
 */
function validateStyles(element, expectedStyles) {
  const actualStyles = getComputedStyles(element, Object.keys(expectedStyles))
  const results = []
  
  Object.keys(expectedStyles).forEach(prop => {
    const expected = expectedStyles[prop]
    const actual = actualStyles[prop]
    const passed = actual === expected || 
                   (prop === 'list-style' && actual.includes('none')) ||
                   (prop === 'padding-left' && parseFloat(actual) === parseFloat(expected)) ||
                   (prop === 'padding-right' && parseFloat(actual) === parseFloat(expected))
    
    results.push({
      property: prop,
      expected,
      actual,
      passed
    })
  })
  
  return results
}

/**
 * 检查是否有强制换行问题
 */
function checkForceLineBreak(listElement) {
  const listItems = listElement.querySelectorAll('li')
  const issues = []
  
  listItems.forEach((li, index) => {
    const rect = li.getBoundingClientRect()
    const textContent = li.textContent
    
    // 检查文本是否被强制换行（简单检查：如果高度过大可能是换行了）
    if (rect.height > 30) { // 假设正常单行高度不超过30px
      issues.push({
        itemIndex: index,
        height: rect.height,
        text: textContent.substring(0, 50) + '...'
      })
    }
  })
  
  return issues
}

/**
 * 运行单个测试用例
 */
function runTestCase(testCase) {
  console.group(`🧪 测试: ${testCase.name}`)
  
  // 创建测试容器
  const container = document.createElement('div')
  container.className = 'ProseMirror'
  container.style.width = '400px'
  container.style.padding = '20px'
  container.style.border = '1px solid #ccc'
  container.style.margin = '10px'
  document.body.appendChild(container)
  
  // 创建测试列表
  const list = createTestList(testCase.listType, testCase.alignment)
  container.appendChild(list)
  
  // 等待样式应用
  setTimeout(() => {
    // 验证样式
    const styleResults = validateStyles(list, testCase.expectedStyles)
    const lineBreakIssues = checkForceLineBreak(list)
    
    // 输出结果
    console.log('📊 样式验证结果:')
    styleResults.forEach(result => {
      const icon = result.passed ? '✅' : '❌'
      console.log(`${icon} ${result.property}: 期望 "${result.expected}", 实际 "${result.actual}"`)
    })
    
    if (lineBreakIssues.length > 0) {
      console.log('⚠️ 强制换行问题:')
      lineBreakIssues.forEach(issue => {
        console.log(`  项目 ${issue.itemIndex}: 高度 ${issue.height}px, 内容: ${issue.text}`)
      })
    } else {
      console.log('✅ 无强制换行问题')
    }
    
    const allPassed = styleResults.every(r => r.passed) && lineBreakIssues.length === 0
    console.log(`🎯 测试结果: ${allPassed ? '通过' : '失败'}`)
    
    // 清理测试元素
    setTimeout(() => {
      document.body.removeChild(container)
    }, 2000)
    
    console.groupEnd()
  }, 100)
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始 Tiptap 列表对齐功能验证')
  console.log('=' * 50)
  
  testCases.forEach((testCase, index) => {
    setTimeout(() => {
      runTestCase(testCase)
    }, index * 500) // 错开执行时间
  })
  
  setTimeout(() => {
    console.log('=' * 50)
    console.log('✨ 所有测试完成')
  }, testCases.length * 500 + 1000)
}

// 导出函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    runTestCase,
    testCases
  }
} else {
  // 浏览器环境下自动运行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests)
  } else {
    runAllTests()
  }
}
