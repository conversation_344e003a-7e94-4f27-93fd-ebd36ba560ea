{"version": 3, "sources": ["../../@tiptap/extension-heading/src/heading.ts"], "sourcesContent": ["import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\n\n/**\n * The heading level options.\n */\nexport type Level = 1 | 2 | 3 | 4 | 5 | 6\n\nexport interface HeadingOptions {\n  /**\n   * The available heading levels.\n   * @default [1, 2, 3, 4, 5, 6]\n   * @example [1, 2, 3]\n   */\n  levels: Level[],\n\n  /**\n   * The HTML attributes for a heading node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    heading: {\n      /**\n       * Set a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.setHeading({ level: 1 })\n       */\n      setHeading: (attributes: { level: Level }) => ReturnType,\n      /**\n       * Toggle a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.toggleHeading({ level: 1 })\n       */\n      toggleHeading: (attributes: { level: Level }) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nexport const Heading = Node.create<HeadingOptions>({\n  name: 'heading',\n\n  addOptions() {\n    return {\n      levels: [1, 2, 3, 4, 5, 6],\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'inline*',\n\n  group: 'block',\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      level: {\n        default: 1,\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return this.options.levels\n      .map((level: Level) => ({\n        tag: `h${level}`,\n        attrs: { level },\n      }))\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const hasLevel = this.options.levels.includes(node.attrs.level)\n    const level = hasLevel\n      ? node.attrs.level\n      : this.options.levels[0]\n\n    return [`h${level}`, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.setNode(this.name, attributes)\n      },\n      toggleHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.toggleNode(this.name, 'paragraph', attributes)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return this.options.levels.reduce((items, level) => ({\n      ...items,\n      ...{\n        [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n      },\n    }), {})\n  },\n\n  addInputRules() {\n    return this.options.levels.map(level => {\n      return textblockTypeInputRule({\n        find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n        type: this.type,\n        getAttributes: {\n          level,\n        },\n      })\n    })\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8Ca,IAAA,UAAU,KAAK,OAAuB;EACjD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;MACzB,gBAAgB,CAAA;;;EAIpB,SAAS;EAET,OAAO;EAEP,UAAU;EAEV,gBAAa;AACX,WAAO;MACL,OAAO;QACL,SAAS;QACT,UAAU;MACX;;;EAIL,YAAS;AACP,WAAO,KAAK,QAAQ,OACjB,IAAI,CAAC,WAAkB;MACtB,KAAK,IAAI,KAAK;MACd,OAAO,EAAE,MAAK;IACf,EAAC;;EAGN,WAAW,EAAE,MAAM,eAAc,GAAE;AACjC,UAAM,WAAW,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM,KAAK;AAC9D,UAAM,QAAQ,WACV,KAAK,MAAM,QACX,KAAK,QAAQ,OAAO,CAAC;AAEzB,WAAO,CAAC,IAAI,KAAK,IAAI,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGtF,cAAW;AACT,WAAO;MACL,YAAY,gBAAc,CAAC,EAAE,SAAQ,MAAM;AACzC,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;;AAGT,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;;MAE/C,eAAe,gBAAc,CAAC,EAAE,SAAQ,MAAM;AAC5C,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;;AAGT,eAAO,SAAS,WAAW,KAAK,MAAM,aAAa,UAAU;;;;EAKnE,uBAAoB;AAClB,WAAO,KAAK,QAAQ,OAAO,OAAO,CAAC,OAAO,WAAW;MACnD,GAAG;MACH,GAAG;QACD,CAAC,WAAW,KAAK,EAAE,GAAG,MAAM,KAAK,OAAO,SAAS,cAAc,EAAE,MAAK,CAAE;MACzE;QACC,CAAA,CAAE;;EAGR,gBAAa;AACX,WAAO,KAAK,QAAQ,OAAO,IAAI,WAAQ;AACrC,aAAO,uBAAuB;QAC5B,MAAM,IAAI,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,QAAQ;QACzE,MAAM,KAAK;QACX,eAAe;UACb;QACD;MACF,CAAA;IACH,CAAC;;AAEJ,CAAA;", "names": []}