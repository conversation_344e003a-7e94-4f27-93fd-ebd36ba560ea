import { mergeAttributes, Node } from '@tiptap/core'

export interface CustomTaskListOptions {
  /**
   * The node type name for a task item.
   * @default 'taskItem'
   * @example 'myCustomTaskItem'
   */
  itemTypeName: string,

  /**
   * The HTML attributes for a task list node.
   * @default {}
   * @example { class: 'foo' }
   */
  HTMLAttributes: Record<string, any>,

  /**
   * Keep the marks when splitting a list item.
   * @default false
   * @example true
   */
  keepMarks: boolean,

  /**
   * Keep the attributes when splitting a list item.
   * @default false
   * @example true
   */
  keepAttributes: boolean,
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    customTaskList: {
      /**
       * Toggle a task list
       * @example editor.commands.toggleTaskList()
       */
      toggleTaskList: () => ReturnType,
    }
  }
}

/**
 * 自定义任务列表扩展，支持保持属性和标记
 * 基于 @tiptap/extension-task-list 扩展，添加了 keepMarks 和 keepAttributes 支持
 */
export const CustomTaskList = Node.create<CustomTaskListOptions>({
  name: 'taskList',

  addOptions() {
    return {
      itemTypeName: 'taskItem',
      HTMLAttributes: {},
      keepMarks: false,
      keepAttributes: false,
    }
  },

  group: 'block list',

  content() {
    return `${this.options.itemTypeName}+`
  },

  parseHTML() {
    return [
      {
        tag: `ul[data-type="${this.name}"]`,
        priority: 51,
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-type': this.name }), 0]
  },

  addCommands() {
    return {
      toggleTaskList: () => ({ commands, chain, state }) => {
        // 如果启用了 keepAttributes，需要获取当前段落的属性
        if (this.options.keepAttributes) {
          // 获取当前选择的段落节点
          const { selection } = state
          const { $from } = selection

          // 查找当前段落或其父节点的属性
          let currentAttributes = {}

          // 尝试从当前段落获取 textAlign 属性
          const currentNode = $from.node($from.depth)
          if (currentNode && currentNode.attrs && currentNode.attrs.textAlign) {
            currentAttributes.textAlign = currentNode.attrs.textAlign
          }

          // 如果当前节点没有 textAlign，尝试从父节点获取
          if (!currentAttributes.textAlign && $from.depth > 0) {
            const parentNode = $from.node($from.depth - 1)
            if (parentNode && parentNode.attrs && parentNode.attrs.textAlign) {
              currentAttributes.textAlign = parentNode.attrs.textAlign
            }
          }

          // 调用 toggleList 并传递当前属性
          return chain()
            .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks, currentAttributes)
            .run()
        }

        // 标准的 toggleList 调用，传递 keepMarks 参数
        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)
      },
    }
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-9': () => this.editor.commands.toggleTaskList(),
    }
  },

  addInputRules() {
    // 任务列表的输入规则：- [ ] 或 - [x]
    const inputRegex = /^\s*(\[([( |x])?\])\s$/
    
    return [
      // 这里可以添加输入规则，但由于任务列表通常通过工具栏创建，暂时保持简单
    ]
  },
})

export default CustomTaskList
