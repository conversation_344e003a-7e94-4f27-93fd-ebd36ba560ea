{"name": "tippy.js", "version": "6.3.7", "description": "The complete tooltip, popover, dropdown, and menu solution for the web", "main": "dist/tippy.cjs.js", "module": "dist/tippy.esm.js", "unpkg": "dist/tippy-bundle.umd.min.js", "types": "index.d.ts", "sideEffects": ["**/*.css"], "author": "atomiks", "contributors": ["<PERSON>"], "license": "MIT", "bugs": "https://github.com/atomiks/tippyjs/issues", "homepage": "https://atomiks.github.io/tippyjs/", "keywords": ["tooltip", "popover", "popper", "dropdown", "popup", "tippy", "tippy.js"], "files": ["dist/", "animations/", "themes/", "headless/", "index.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/atomiks/tippyjs.git"}, "scripts": {"dev": "cross-env NODE_ENV=dev rollup -c .config/rollup.config.js --watch", "build": "node build/index.js && rollup -c .config/rollup.config.js && bundlesize", "build:visual": "cross-env NODE_ENV=test rollup -c .config/rollup.config.js", "serve": "serve test/visual", "test:dom": "jest unit integration --coverage", "test:functional": "jest functional", "test:types": "tsc && tsd", "test": "yarn test:types && yarn test:dom && yarn test:functional", "lint": "eslint . --ext .ts,.js", "format": "prettier --write \"**/*.{js,ts,json,md,mdx,scss,css}\" --ignore-path .config/.prettierignore", "clean": "rimraf dist/ themes/ animations/ coverage/ .devserver/ .cache/ ./index.d.ts", "prepare": "yarn clean && yarn build"}, "babel": {"extends": "./.config/babel.config"}, "jest": {"preset": "./.config/jest.config"}, "eslintConfig": {"extends": "./.config/eslint.config"}, "prettier": {"singleQuote": true, "bracketSpacing": false, "proseWrap": "always"}, "browserslist": ["> 0.5%", "not dead", "not safari < 8"], "bundlesize": [{"path": "dist/tippy-bundle.umd.min.js", "maxSize": "10 kB"}, {"path": "headless/dist/tippy-headless.umd.min.js", "maxSize": "10 kB"}, {"path": "dist/tippy.umd.min.js", "maxSize": "10 kB"}, {"path": "dist/tippy.css", "maxSize": "5 kB"}], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.ts": ["jest --findRelatedTests", "eslint . --ext .ts,.js", "git add"], "{build,src,test,website/src}/**/*.{ts,js,json,css,scss,md}": ["prettier --write", "git add"]}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/preset-env": "^7.8.3", "@babel/preset-typescript": "^7.13.0", "@testing-library/dom": "^6.11.0", "@types/node": "^12.12.25", "@typescript-eslint/eslint-plugin": "^4.16.1", "@typescript-eslint/parser": "^4.16.1", "autoprefixer": "^9.7.4", "babel-jest": "^25.3.0", "babel-plugin-dev-expression": "^0.2.2", "bundlesize": "^0.18.0", "colorette": "^1.1.0", "core-js": "^3.6.4", "cross-env": "^7.0.0", "cssnano": "^4.1.10", "dotenv": "^8.2.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "husky": "^3.1.0", "jest": "^25.3.0", "jest-environment-jsdom-fourteen": "^1.0.1", "jest-image-snapshot": "^2.12.0", "jest-puppeteer": "^4.4.0", "jest-puppeteer-docker": "^1.3.2", "lint-staged": "^9.5.0", "postcss": "^7.0.26", "poster": "0.0.9", "prettier": "^2.0.1", "promise": "^8.0.3", "puppeteer": "^2.1.1", "rimraf": "^3.0.0", "rollup": "^1.29.1", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-css-only": "^1.0.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-livereload": "^1.0.4", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sass": "^1.2.2", "rollup-plugin-serve": "^1.0.1", "rollup-plugin-terser": "^5.2.0", "sass": "^1.25.0", "serve": "^11.3.0", "tsd": "^0.14.0", "typescript": "^4.2.2"}, "dependencies": {"@popperjs/core": "^2.9.0"}}