{"version": 3, "sources": ["../../@stomp/stompjs/src/index.ts", "../../@stomp/stompjs/src/client.ts", "../../@stomp/stompjs/src/stomp-handler.ts", "../../@stomp/stompjs/src/augment-websocket.ts", "../../@stomp/stompjs/src/byte.ts", "../../@stomp/stompjs/src/frame-impl.ts", "../../@stomp/stompjs/src/parser.ts", "../../@stomp/stompjs/src/ticker.ts", "../../@stomp/stompjs/src/types.ts", "../../@stomp/stompjs/src/versions.ts", "../../@stomp/stompjs/esm6/i-frame.js", "../../@stomp/stompjs/esm6/i-message.js", "../../@stomp/stompjs/src/stomp-config.ts", "../../@stomp/stompjs/src/stomp-headers.ts", "../../@stomp/stompjs/esm6/stomp-subscription.js", "../../@stomp/stompjs/esm6/i-transaction.js", "../../@stomp/stompjs/src/compatibility/compat-client.ts", "../../@stomp/stompjs/src/compatibility/heartbeat-info.ts", "../../@stomp/stompjs/src/compatibility/stomp.ts"], "sourcesContent": ["export * from './client.js';\nexport * from './frame-impl.js';\nexport * from './i-frame.js';\nexport * from './i-message.js';\nexport * from './parser.js';\nexport * from './stomp-config.js';\nexport * from './stomp-headers.js';\nexport * from './stomp-subscription.js';\nexport * from './i-transaction.js';\nexport * from './types.js';\nexport * from './versions.js';\n\n// Compatibility code\nexport * from './compatibility/compat-client.js';\nexport * from './compatibility/stomp.js';\n", "import { ITransaction } from './i-transaction.js';\nimport { StompConfig } from './stomp-config.js';\nimport { <PERSON>om<PERSON><PERSON>and<PERSON> } from './stomp-handler.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { StompSubscription } from './stomp-subscription.js';\nimport {\n  ActivationState,\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  IPublishParams,\n  IStompSocket,\n  messageCallbackType,\n  ReconnectionTimeMode,\n  StompSocketState,\n  TickerStrategy,\n  wsErrorCallbackType,\n} from './types.js';\nimport { Versions } from './versions.js';\n\n/**\n * @internal\n */\ndeclare const WebSocket: {\n  prototype: IStompSocket;\n  new (url: string, protocols?: string | string[]): IStompSocket;\n};\n\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Client {\n  /**\n   * The URL for the STOMP broker to connect to.\n   * Typically like `\"ws://broker.329broker.com:15674/ws\"` or `\"wss://broker.329broker.com:15674/ws\"`.\n   *\n   * Only one of this or [Client#webSocketFactory]{@link Client#webSocketFactory} need to be set.\n   * If both are set, [Client#webSocketFactory]{@link Client#webSocketFactory} will be used.\n   *\n   * If your environment does not support WebSockets natively, please refer to\n   * [Polyfills]{@link https://stomp-js.github.io/guide/stompjs/rx-stomp/ng2-stompjs/pollyfils-for-stompjs-v5.html}.\n   */\n  public brokerURL: string | undefined;\n\n  /**\n   * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n   *\n   * Example:\n   * ```javascript\n   *        // Try only versions 1.1 and 1.0\n   *        client.stompVersions = new Versions(['1.1', '1.0'])\n   * ```\n   */\n  public stompVersions = Versions.default;\n\n  /**\n   * This function should return a WebSocket or a similar (e.g. SockJS) object.\n   * If your environment does not support WebSockets natively, please refer to\n   * [Polyfills]{@link https://stomp-js.github.io/guide/stompjs/rx-stomp/ng2-stompjs/pollyfils-for-stompjs-v5.html}.\n   * If your STOMP Broker supports WebSockets, prefer setting [Client#brokerURL]{@link Client#brokerURL}.\n   *\n   * If both this and [Client#brokerURL]{@link Client#brokerURL} are set, this will be used.\n   *\n   * Example:\n   * ```javascript\n   *        // use a WebSocket\n   *        client.webSocketFactory= function () {\n   *          return new WebSocket(\"wss://broker.329broker.com:15674/ws\");\n   *        };\n   *\n   *        // Typical usage with SockJS\n   *        client.webSocketFactory= function () {\n   *          return new SockJS(\"http://broker.329broker.com/stomp\");\n   *        };\n   * ```\n   */\n  public webSocketFactory: (() => IStompSocket) | undefined;\n\n  /**\n   * Will retry if Stomp connection is not established in specified milliseconds.\n   * Default 0, which switches off automatic reconnection.\n   */\n  public connectionTimeout: number = 0;\n\n  // As per https://stackoverflow.com/questions/45802988/typescript-use-correct-version-of-settimeout-node-vs-window/56239226#56239226\n  private _connectionWatcher: ReturnType<typeof setTimeout> | undefined; // Timer\n\n  /**\n   *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n   */\n  public reconnectDelay: number = 5000;\n\n  /**\n   * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n   * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n   */\n  private _nextReconnectDelay: number = 0;\n\n  /**\n   * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n   * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n   * Set to 0 for no limit on wait time.\n   */\n  public maxReconnectDelay: number = 15 * 60 * 1000; // 15 minutes in ms\n\n  /**\n   * Reconnection wait time mode, either linear (default) or exponential.\n   * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n   *\n   * ```javascript\n   * client.configure({\n   *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n   *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n   *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n   * })\n   * ```\n   */\n  public reconnectTimeMode: ReconnectionTimeMode = ReconnectionTimeMode.LINEAR;\n\n  /**\n   * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n   */\n  public heartbeatIncoming: number = 10000;\n\n  /**\n   * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n   */\n  public heartbeatOutgoing: number = 10000;\n\n  /**\n   * Outgoing heartbeat strategy.\n   * See https://github.com/stomp-js/stompjs/pull/579\n   *\n   * Can be worker or interval strategy, but will always use `interval`\n   * if web workers are unavailable, for example, in a non-browser environment.\n   *\n   * Using Web Workers may work better on long-running pages\n   * and mobile apps, as the browser may suspend Timers in the main page.\n   * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n   *\n   * When used in a JS environment, use 'worker' or 'interval' as valid values.\n   *\n   * Defaults to `interval` strategy.\n   */\n  public heartbeatStrategy: TickerStrategy = TickerStrategy.Interval;\n\n  /**\n   * This switches on a non-standard behavior while sending WebSocket packets.\n   * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n   * Only Java Spring brokers seem to support this mode.\n   *\n   * WebSockets, by itself, split large (text) packets,\n   * so it is not needed with a truly compliant STOMP/WebSocket broker.\n   * Setting it for such a broker will cause large messages to fail.\n   *\n   * `false` by default.\n   *\n   * Binary frames are never split.\n   */\n  public splitLargeFrames: boolean = false;\n\n  /**\n   * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n   * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n   */\n  public maxWebSocketChunkSize: number = 8 * 1024;\n\n  /**\n   * Usually the\n   * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n   * is automatically decided by type of the payload.\n   * Default is `false`, which should work with all compliant brokers.\n   *\n   * Set this flag to force binary frames.\n   */\n  public forceBinaryWSFrames: boolean = false;\n\n  /**\n   * A bug in ReactNative chops a string on occurrence of a NULL.\n   * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n   * This makes incoming WebSocket messages invalid STOMP packets.\n   * Setting this flag attempts to reverse the damage by appending a NULL.\n   * If the broker splits a large message into multiple WebSocket messages,\n   * this flag will cause data loss and abnormal termination of connection.\n   *\n   * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n   */\n  public appendMissingNULLonIncoming: boolean = false;\n\n  /**\n   * Underlying WebSocket instance, READONLY.\n   */\n  get webSocket(): IStompSocket | undefined {\n    return this._stompHandler?._webSocket;\n  }\n\n  /**\n   * Connection headers, important keys - `login`, `passcode`, `host`.\n   * Though STOMP 1.2 standard marks these keys to be present, check your broker documentation for\n   * details specific to your broker.\n   */\n  public connectHeaders: StompHeaders;\n\n  /**\n   * Disconnection headers.\n   */\n  get disconnectHeaders(): StompHeaders {\n    return this._disconnectHeaders;\n  }\n\n  set disconnectHeaders(value: StompHeaders) {\n    this._disconnectHeaders = value;\n    if (this._stompHandler) {\n      this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n    }\n  }\n  private _disconnectHeaders: StompHeaders;\n\n  /**\n   * This function will be called for any unhandled messages.\n   * It is useful for receiving messages sent to RabbitMQ temporary queues.\n   *\n   * It can also get invoked with stray messages while the server is processing\n   * a request to [Client#unsubscribe]{@link Client#unsubscribe}\n   * from an endpoint.\n   *\n   * The actual {@link IMessage} will be passed as parameter to the callback.\n   */\n  public onUnhandledMessage: messageCallbackType;\n\n  /**\n   * STOMP brokers can be requested to notify when an operation is actually completed.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}. See\n   * [Client#watchForReceipt]{@link Client#watchForReceipt} for examples.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onUnhandledReceipt: frameCallbackType;\n\n  /**\n   * Will be invoked if {@link IFrame} of an unknown type is received from the STOMP broker.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onUnhandledFrame: frameCallbackType;\n\n  /**\n   * `true` if there is an active connection to STOMP Broker\n   */\n  get connected(): boolean {\n    return !!this._stompHandler && this._stompHandler.connected;\n  }\n\n  /**\n   * Callback, invoked on before a connection to the STOMP broker.\n   *\n   * You can change options on the client, which will impact the immediate connecting.\n   * It is valid to call [Client#decativate]{@link Client#deactivate} in this callback.\n   *\n   * As of version 5.1, this callback can be\n   * [async](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/async_function)\n   * (i.e., it can return a\n   * [Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)).\n   * In that case, connect will be called only after the Promise is resolved.\n   * This can be used to reliably fetch credentials, access token etc. from some other service\n   * in an asynchronous way.\n   */\n  public beforeConnect: (client: Client) => void | Promise<void>;\n\n  /**\n   * Callback, invoked on every successful connection to the STOMP broker.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   * Sometimes clients will like to use headers from this frame.\n   */\n  public onConnect: frameCallbackType;\n\n  /**\n   * Callback, invoked on every successful disconnection from the STOMP broker. It will not be invoked if\n   * the STOMP broker disconnected due to an error.\n   *\n   * The actual Receipt {@link IFrame} acknowledging the DISCONNECT will be passed as parameter to the callback.\n   *\n   * The way STOMP protocol is designed, the connection may close/terminate without the client\n   * receiving the Receipt {@link IFrame} acknowledging the DISCONNECT.\n   * You might find [Client#onWebSocketClose]{@link Client#onWebSocketClose} more appropriate to watch\n   * STOMP broker disconnects.\n   */\n  public onDisconnect: frameCallbackType;\n\n  /**\n   * Callback, invoked on an ERROR frame received from the STOMP Broker.\n   * A compliant STOMP Broker will close the connection after this type of frame.\n   * Please check broker specific documentation for exact behavior.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   */\n  public onStompError: frameCallbackType;\n\n  /**\n   * Callback, invoked when underlying WebSocket is closed.\n   *\n   * Actual [CloseEvent]{@link https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent}\n   * is passed as parameter to the callback.\n   */\n  public onWebSocketClose: closeEventCallbackType;\n\n  /**\n   * Callback, invoked when underlying WebSocket raises an error.\n   *\n   * Actual [Event]{@link https://developer.mozilla.org/en-US/docs/Web/API/Event}\n   * is passed as parameter to the callback.\n   */\n  public onWebSocketError: wsErrorCallbackType;\n\n  /**\n   * Set it to log the actual raw communication with the broker.\n   * When unset, it logs headers of the parsed frames.\n   *\n   * Changes effect from the next broker reconnect.\n   *\n   * **Caution: this assumes that frames only have valid UTF8 strings.**\n   */\n  public logRawCommunication: boolean;\n\n  /**\n   * By default, debug messages are discarded. To log to `console` following can be used:\n   *\n   * ```javascript\n   *        client.debug = function(str) {\n   *          console.log(str);\n   *        };\n   * ```\n   *\n   * Currently this method does not support levels of log. Be aware that the\n   * output can be quite verbose\n   * and may contain sensitive information (like passwords, tokens etc.).\n   */\n  public debug: debugFnType;\n\n  /**\n   * Browsers do not immediately close WebSockets when `.close` is issued.\n   * This may cause reconnection to take a significantly long time in case\n   *  of some types of failures.\n   * In case of incoming heartbeat failure, this experimental flag instructs\n   * the library to discard the socket immediately\n   * (even before it is actually closed).\n   */\n  public discardWebsocketOnCommFailure: boolean = false;\n\n  /**\n   * version of STOMP protocol negotiated with the server, READONLY\n   */\n  get connectedVersion(): string | undefined {\n    return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n  }\n\n  private _stompHandler: StompHandler | undefined;\n\n  /**\n   * if the client is active (connected or going to reconnect)\n   */\n  get active(): boolean {\n    return this.state === ActivationState.ACTIVE;\n  }\n\n  /**\n   * It will be called on state change.\n   *\n   * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n   */\n  public onChangeState: (state: ActivationState) => void;\n\n  private _changeState(state: ActivationState) {\n    this.state = state;\n    this.onChangeState(state);\n  }\n\n  /**\n   * Activation state.\n   *\n   * It will usually be ACTIVE or INACTIVE.\n   * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n   */\n  public state: ActivationState = ActivationState.INACTIVE;\n\n  private _reconnector: any;\n\n  /**\n   * Create an instance.\n   */\n  constructor(conf: StompConfig = {}) {\n    // No op callbacks\n    const noOp = () => {};\n    this.debug = noOp;\n    this.beforeConnect = noOp;\n    this.onConnect = noOp;\n    this.onDisconnect = noOp;\n    this.onUnhandledMessage = noOp;\n    this.onUnhandledReceipt = noOp;\n    this.onUnhandledFrame = noOp;\n    this.onStompError = noOp;\n    this.onWebSocketClose = noOp;\n    this.onWebSocketError = noOp;\n    this.logRawCommunication = false;\n    this.onChangeState = noOp;\n\n    // These parameters would typically get proper values before connect is called\n    this.connectHeaders = {};\n    this._disconnectHeaders = {};\n\n    // Apply configuration\n    this.configure(conf);\n  }\n\n  /**\n   * Update configuration.\n   */\n  public configure(conf: StompConfig): void {\n    // bulk assign all properties to this\n    (Object as any).assign(this, conf);\n\n    // Warn on incorrect maxReconnectDelay settings\n    if (\n      this.maxReconnectDelay > 0 &&\n      this.maxReconnectDelay < this.reconnectDelay\n    ) {\n      this.debug(\n        `Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`,\n      );\n      this.maxReconnectDelay = this.reconnectDelay;\n    }\n  }\n\n  /**\n   * Initiate the connection with the broker.\n   * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n   * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n   * is set to EXPONENTIAL it will increase the wait time exponentially\n   *\n   * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n   */\n  public activate(): void {\n    const _activate = () => {\n      if (this.active) {\n        this.debug('Already ACTIVE, ignoring request to activate');\n        return;\n      }\n\n      this._changeState(ActivationState.ACTIVE);\n\n      this._nextReconnectDelay = this.reconnectDelay;\n      this._connect();\n    };\n\n    // if it is deactivating, wait for it to complete before activating.\n    if (this.state === ActivationState.DEACTIVATING) {\n      this.debug('Waiting for deactivation to finish before activating');\n      this.deactivate().then(() => {\n        _activate();\n      });\n    } else {\n      _activate();\n    }\n  }\n\n  private async _connect(): Promise<void> {\n    await this.beforeConnect(this);\n\n    if (this._stompHandler) {\n      this.debug(\n        'There is already a stompHandler, skipping the call to connect',\n      );\n      return;\n    }\n\n    if (!this.active) {\n      this.debug(\n        'Client has been marked inactive, will not attempt to connect',\n      );\n      return;\n    }\n\n    // setup connection watcher\n    if (this.connectionTimeout > 0) {\n      // clear first\n      if (this._connectionWatcher) {\n        clearTimeout(this._connectionWatcher);\n      }\n      this._connectionWatcher = setTimeout(() => {\n        if (this.connected) {\n          return;\n        }\n        // Connection not established, close the underlying socket\n        // a reconnection will be attempted\n        this.debug(\n          `Connection not established in ${this.connectionTimeout}ms, closing socket`,\n        );\n        this.forceDisconnect();\n      }, this.connectionTimeout);\n    }\n\n    this.debug('Opening Web Socket...');\n\n    // Get the actual WebSocket (or a similar object)\n    const webSocket = this._createWebSocket();\n\n    this._stompHandler = new StompHandler(this, webSocket, {\n      debug: this.debug,\n      stompVersions: this.stompVersions,\n      connectHeaders: this.connectHeaders,\n      disconnectHeaders: this._disconnectHeaders,\n      heartbeatIncoming: this.heartbeatIncoming,\n      heartbeatOutgoing: this.heartbeatOutgoing,\n      heartbeatStrategy: this.heartbeatStrategy,\n      splitLargeFrames: this.splitLargeFrames,\n      maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n      forceBinaryWSFrames: this.forceBinaryWSFrames,\n      logRawCommunication: this.logRawCommunication,\n      appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n      discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n\n      onConnect: frame => {\n        // Successfully connected, stop the connection watcher\n        if (this._connectionWatcher) {\n          clearTimeout(this._connectionWatcher);\n          this._connectionWatcher = undefined;\n        }\n\n        if (!this.active) {\n          this.debug(\n            'STOMP got connected while deactivate was issued, will disconnect now',\n          );\n          this._disposeStompHandler();\n          return;\n        }\n        this.onConnect(frame);\n      },\n      onDisconnect: frame => {\n        this.onDisconnect(frame);\n      },\n      onStompError: frame => {\n        this.onStompError(frame);\n      },\n      onWebSocketClose: evt => {\n        this._stompHandler = undefined; // a new one will be created in case of a reconnect\n\n        if (this.state === ActivationState.DEACTIVATING) {\n          // Mark deactivation complete\n          this._changeState(ActivationState.INACTIVE);\n        }\n\n        // The callback is called before attempting to reconnect, this would allow the client\n        // to be `deactivated` in the callback.\n        this.onWebSocketClose(evt);\n\n        if (this.active) {\n          this._schedule_reconnect();\n        }\n      },\n      onWebSocketError: evt => {\n        this.onWebSocketError(evt);\n      },\n      onUnhandledMessage: message => {\n        this.onUnhandledMessage(message);\n      },\n      onUnhandledReceipt: frame => {\n        this.onUnhandledReceipt(frame);\n      },\n      onUnhandledFrame: frame => {\n        this.onUnhandledFrame(frame);\n      },\n    });\n\n    this._stompHandler.start();\n  }\n\n  private _createWebSocket(): IStompSocket {\n    let webSocket: IStompSocket;\n\n    if (this.webSocketFactory) {\n      webSocket = this.webSocketFactory();\n    } else if (this.brokerURL) {\n      webSocket = new WebSocket(\n        this.brokerURL,\n        this.stompVersions.protocolVersions(),\n      );\n    } else {\n      throw new Error('Either brokerURL or webSocketFactory must be provided');\n    }\n    webSocket.binaryType = 'arraybuffer';\n    return webSocket;\n  }\n\n  private _schedule_reconnect(): void {\n    if (this._nextReconnectDelay > 0) {\n      this.debug(\n        `STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`,\n      );\n\n      this._reconnector = setTimeout(() => {\n        if (this.reconnectTimeMode === ReconnectionTimeMode.EXPONENTIAL) {\n          this._nextReconnectDelay = this._nextReconnectDelay * 2;\n\n          // Truncated exponential backoff with a set limit unless disabled\n          if (this.maxReconnectDelay !== 0) {\n            this._nextReconnectDelay = Math.min(\n              this._nextReconnectDelay,\n              this.maxReconnectDelay,\n            );\n          }\n        }\n\n        this._connect();\n      }, this._nextReconnectDelay);\n    }\n  }\n\n  /**\n   * Disconnect if connected and stop auto reconnect loop.\n   * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n   *\n   * This call is async. It will resolve immediately if there is no underlying active websocket,\n   * otherwise, it will resolve after the underlying websocket is properly disposed of.\n   *\n   * It is not an error to invoke this method more than once.\n   * Each of those would resolve on completion of deactivation.\n   *\n   * To reactivate, you can call [Client#activate]{@link Client#activate}.\n   *\n   * Experimental: pass `force: true` to immediately discard the underlying connection.\n   * This mode will skip both the STOMP and the Websocket shutdown sequences.\n   * In some cases, browsers take a long time in the Websocket shutdown\n   * if the underlying connection had gone stale.\n   * Using this mode can speed up.\n   * When this mode is used, the actual Websocket may linger for a while\n   * and the broker may not realize that the connection is no longer in use.\n   *\n   * It is possible to invoke this method initially without the `force` option\n   * and subsequently, say after a wait, with the `force` option.\n   */\n  public async deactivate(options: { force?: boolean } = {}): Promise<void> {\n    const force: boolean = options.force || false;\n    const needToDispose = this.active;\n    let retPromise: Promise<void>;\n\n    if (this.state === ActivationState.INACTIVE) {\n      this.debug(`Already INACTIVE, nothing more to do`);\n      return Promise.resolve();\n    }\n\n    this._changeState(ActivationState.DEACTIVATING);\n\n    // Reset reconnection timer just to be safe\n    this._nextReconnectDelay = 0;\n\n    // Clear if a reconnection was scheduled\n    if (this._reconnector) {\n      clearTimeout(this._reconnector);\n      this._reconnector = undefined;\n    }\n\n    if (\n      this._stompHandler &&\n      // @ts-ignore - if there is a _stompHandler, there is the webSocket\n      this.webSocket.readyState !== StompSocketState.CLOSED\n    ) {\n      const origOnWebSocketClose = this._stompHandler.onWebSocketClose;\n      // we need to wait for the underlying websocket to close\n      retPromise = new Promise<void>((resolve, reject) => {\n        // @ts-ignore - there is a _stompHandler\n        this._stompHandler.onWebSocketClose = evt => {\n          origOnWebSocketClose(evt);\n          resolve();\n        };\n      });\n    } else {\n      // indicate that auto reconnect loop should terminate\n      this._changeState(ActivationState.INACTIVE);\n      return Promise.resolve();\n    }\n\n    if (force) {\n      this._stompHandler?.discardWebsocket();\n    } else if (needToDispose) {\n      this._disposeStompHandler();\n    }\n\n    return retPromise;\n  }\n\n  /**\n   * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n   * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n   * After forcing disconnect, automatic reconnect will be attempted.\n   * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n   */\n  public forceDisconnect() {\n    if (this._stompHandler) {\n      this._stompHandler.forceDisconnect();\n    }\n  }\n\n  private _disposeStompHandler() {\n    // Dispose STOMP Handler\n    if (this._stompHandler) {\n      this._stompHandler.dispose();\n    }\n  }\n\n  /**\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations.\n   *\n   * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n   *\n   * `body` must be String.\n   * You will need to covert the payload to string in case it is not string (e.g. JSON).\n   *\n   * To send a binary message body, use `binaryBody` parameter. It should be a\n   * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n   * Sometimes brokers may not support binary frames out of the box.\n   * Please check your broker documentation.\n   *\n   * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n   * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n   * For binary messages, `content-length` header is always added.\n   *\n   * Caution: The broker will, most likely, report an error and disconnect\n   * if the message body has NULL octet(s) and `content-length` header is missing.\n   *\n   * ```javascript\n   *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n   *\n   *        // Only destination is mandatory parameter\n   *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n   *\n   *        // Skip content-length header in the frame to the broker\n   *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n   *\n   *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n   *        // setting content-type header is not mandatory, however a good practice\n   *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n   *                         headers: {'content-type': 'application/octet-stream'}});\n   * ```\n   */\n  public publish(params: IPublishParams) {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.publish(params);\n  }\n\n  private _checkConnection() {\n    if (!this.connected) {\n      throw new TypeError('There is no underlying STOMP connection');\n    }\n  }\n\n  /**\n   * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n   * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n   * The value (say receipt-id) for this header needs to be unique for each use.\n   * Typically, a sequence, a UUID, a random number or a combination may be used.\n   *\n   * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n   * The operation needs to be matched based on the value of the receipt-id.\n   *\n   * This method allows watching for a receipt and invoking the callback\n   *  when the corresponding receipt has been received.\n   *\n   * The actual {@link IFrame} will be passed as parameter to the callback.\n   *\n   * Example:\n   * ```javascript\n   *        // Subscribing with acknowledgement\n   *        let receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *\n   *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n   *\n   *\n   *        // Publishing with acknowledgement\n   *        receiptId = randomText();\n   *\n   *        client.watchForReceipt(receiptId, function() {\n   *          // Will be called after server acknowledges\n   *        });\n   *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n   * ```\n   */\n  public watchForReceipt(receiptId: string, callback: frameCallbackType): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.watchForReceipt(receiptId, callback);\n  }\n\n  /**\n   * Subscribe to a STOMP Broker location. The callback will be invoked for each\n   * received message with the {@link IMessage} as argument.\n   *\n   * Note: The library will generate a unique ID if there is none provided in the headers.\n   *       To use your own ID, pass it using the `headers` argument.\n   *\n   * ```javascript\n   *        callback = function(message) {\n   *        // called when the client receives a STOMP message from the server\n   *          if (message.body) {\n   *            alert(\"got message with body \" + message.body)\n   *          } else {\n   *            alert(\"got empty message\");\n   *          }\n   *        });\n   *\n   *        var subscription = client.subscribe(\"/queue/test\", callback);\n   *\n   *        // Explicit subscription id\n   *        var mySubId = 'my-subscription-id-001';\n   *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n   * ```\n   */\n  public subscribe(\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {},\n  ): StompSubscription {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.subscribe(destination, callback, headers);\n  }\n\n  /**\n   * It is preferable to unsubscribe from a subscription by calling\n   * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n   *\n   * ```javascript\n   *        var subscription = client.subscribe(destination, onmessage);\n   *        // ...\n   *        subscription.unsubscribe();\n   * ```\n   *\n   * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n   */\n  public unsubscribe(id: string, headers: StompHeaders = {}): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.unsubscribe(id, headers);\n  }\n\n  /**\n   * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n   * and [abort]{@link ITransaction#abort}.\n   *\n   * `transactionId` is optional, if not passed the library will generate it internally.\n   */\n  public begin(transactionId?: string): ITransaction {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    return this._stompHandler.begin(transactionId);\n  }\n\n  /**\n   * Commit a transaction.\n   *\n   * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.commit();\n   * ```\n   */\n  public commit(transactionId: string): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.commit(transactionId);\n  }\n\n  /**\n   * Abort a transaction.\n   * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n   * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n   *\n   * ```javascript\n   *        var tx = client.begin(txId);\n   *        //...\n   *        tx.abort();\n   * ```\n   */\n  public abort(transactionId: string): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.abort(transactionId);\n  }\n\n  /**\n   * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // acknowledge it\n   *          message.ack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  public ack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {},\n  ): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.ack(messageId, subscriptionId, headers);\n  }\n\n  /**\n   * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n   * on the {@link IMessage} handled by a subscription callback:\n   *\n   * ```javascript\n   *        var callback = function (message) {\n   *          // process the message\n   *          // an error occurs, nack it\n   *          message.nack();\n   *        };\n   *        client.subscribe(destination, callback, {'ack': 'client'});\n   * ```\n   */\n  public nack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {},\n  ): void {\n    this._checkConnection();\n    // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n    this._stompHandler.nack(messageId, subscriptionId, headers);\n  }\n}\n", "import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { Client } from './client.js';\nimport { FrameImpl } from './frame-impl.js';\nimport type { IMessage } from './i-message.js';\nimport { ITransaction } from './i-transaction.js';\nimport { Parser } from './parser.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { StompSubscription } from './stomp-subscription.js';\nimport { Ticker } from './ticker.js';\nimport {\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  IPublishParams,\n  IStompSocket,\n  IStompSocketMessageEvent,\n  IStomptHandlerConfig,\n  messageCallbackType,\n  StompSocketState,\n  wsErrorCallbackType,\n} from './types.js';\nimport { Versions } from './versions.js';\n\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n  public debug: debugFnType;\n\n  public stompVersions: Versions;\n\n  public connectHeaders: StompHeaders;\n\n  public disconnectHeaders: StompHeaders;\n\n  public heartbeatIncoming: number;\n\n  public heartbeatOutgoing: number;\n\n  public onUnhandledMessage: messageCallbackType;\n\n  public onUnhandledReceipt: frameCallbackType;\n\n  public onUnhandledFrame: frameCallbackType;\n\n  public onConnect: frameCallbackType;\n\n  public onDisconnect: frameCallbackType;\n\n  public onStompError: frameCallbackType;\n\n  public onWebSocketClose: closeEventCallbackType;\n\n  public onWebSocketError: wsErrorCallbackType;\n\n  public logRawCommunication: boolean;\n\n  public splitLargeFrames: boolean;\n\n  public maxWebSocketChunkSize: number;\n\n  public forceBinaryWSFrames: boolean;\n\n  public appendMissingNULLonIncoming: boolean;\n\n  public discardWebsocketOnCommFailure: boolean;\n\n  get connectedVersion(): string | undefined {\n    return this._connectedVersion;\n  }\n  private _connectedVersion: string | undefined;\n\n  get connected(): boolean {\n    return this._connected;\n  }\n\n  private _connected: boolean = false;\n\n  private readonly _subscriptions: { [key: string]: messageCallbackType };\n  private readonly _receiptWatchers: { [key: string]: frameCallbackType };\n  private _partialData: string;\n  private _escapeHeaderValues: boolean;\n  private _counter: number;\n  private _pinger?: Ticker;\n  private _ponger: any;\n  private _lastServerActivityTS: number;\n\n  constructor(\n    private _client: Client,\n    public _webSocket: IStompSocket,\n    config: IStomptHandlerConfig\n  ) {\n    // used to index subscribers\n    this._counter = 0;\n\n    // subscription callbacks indexed by subscriber's ID\n    this._subscriptions = {};\n\n    // receipt-watchers indexed by receipts-ids\n    this._receiptWatchers = {};\n\n    this._partialData = '';\n\n    this._escapeHeaderValues = false;\n\n    this._lastServerActivityTS = Date.now();\n\n    this.debug = config.debug;\n    this.stompVersions = config.stompVersions;\n    this.connectHeaders = config.connectHeaders;\n    this.disconnectHeaders = config.disconnectHeaders;\n    this.heartbeatIncoming = config.heartbeatIncoming;\n    this.heartbeatOutgoing = config.heartbeatOutgoing;\n    this.splitLargeFrames = config.splitLargeFrames;\n    this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n    this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n    this.logRawCommunication = config.logRawCommunication;\n    this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n    this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n    this.onConnect = config.onConnect;\n    this.onDisconnect = config.onDisconnect;\n    this.onStompError = config.onStompError;\n    this.onWebSocketClose = config.onWebSocketClose;\n    this.onWebSocketError = config.onWebSocketError;\n    this.onUnhandledMessage = config.onUnhandledMessage;\n    this.onUnhandledReceipt = config.onUnhandledReceipt;\n    this.onUnhandledFrame = config.onUnhandledFrame;\n  }\n\n  public start(): void {\n    const parser = new Parser(\n      // On Frame\n      rawFrame => {\n        const frame = FrameImpl.fromRawFrame(\n          rawFrame,\n          this._escapeHeaderValues\n        );\n\n        // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n        if (!this.logRawCommunication) {\n          this.debug(`<<< ${frame}`);\n        }\n\n        const serverFrameHandler =\n          this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n        serverFrameHandler(frame);\n      },\n      // On Incoming Ping\n      () => {\n        this.debug('<<< PONG');\n      }\n    );\n\n    this._webSocket.onmessage = (evt: IStompSocketMessageEvent) => {\n      this.debug('Received data');\n      this._lastServerActivityTS = Date.now();\n\n      if (this.logRawCommunication) {\n        const rawChunkAsString =\n          evt.data instanceof ArrayBuffer\n            ? new TextDecoder().decode(evt.data)\n            : evt.data;\n        this.debug(`<<< ${rawChunkAsString}`);\n      }\n\n      parser.parseChunk(\n        evt.data as string | ArrayBuffer,\n        this.appendMissingNULLonIncoming\n      );\n    };\n\n    this._webSocket.onclose = (closeEvent): void => {\n      this.debug(`Connection closed to ${this._webSocket.url}`);\n      this._cleanUp();\n      this.onWebSocketClose(closeEvent);\n    };\n\n    this._webSocket.onerror = (errorEvent): void => {\n      this.onWebSocketError(errorEvent);\n    };\n\n    this._webSocket.onopen = () => {\n      // Clone before updating\n      const connectHeaders = (Object as any).assign({}, this.connectHeaders);\n\n      this.debug('Web Socket Opened...');\n      connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n      connectHeaders['heart-beat'] = [\n        this.heartbeatOutgoing,\n        this.heartbeatIncoming,\n      ].join(',');\n      this._transmit({ command: 'CONNECT', headers: connectHeaders });\n    };\n  }\n\n  private readonly _serverFrameHandlers: {\n    [key: string]: frameCallbackType;\n  } = {\n    // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n    CONNECTED: frame => {\n      this.debug(`connected to server ${frame.headers.server}`);\n      this._connected = true;\n      this._connectedVersion = frame.headers.version;\n      // STOMP version 1.2 needs header values to be escaped\n      if (this._connectedVersion === Versions.V1_2) {\n        this._escapeHeaderValues = true;\n      }\n\n      this._setupHeartbeat(frame.headers);\n      this.onConnect(frame);\n    },\n\n    // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n    MESSAGE: frame => {\n      // the callback is registered when the client calls\n      // `subscribe()`.\n      // If there is no registered subscription for the received message,\n      // the default `onUnhandledMessage` callback is used that the client can set.\n      // This is useful for subscriptions that are automatically created\n      // on the browser side (e.g. [RabbitMQ's temporary\n      // queues](https://www.rabbitmq.com/stomp.html)).\n      const subscription = frame.headers.subscription;\n      const onReceive =\n        this._subscriptions[subscription] || this.onUnhandledMessage;\n\n      // bless the frame to be a Message\n      const message = frame as IMessage;\n\n      const client = this;\n      const messageId =\n        this._connectedVersion === Versions.V1_2\n          ? message.headers.ack\n          : message.headers['message-id'];\n\n      // add `ack()` and `nack()` methods directly to the returned frame\n      // so that a simple call to `message.ack()` can acknowledge the message.\n      message.ack = (headers: StompHeaders = {}): void => {\n        return client.ack(messageId, subscription, headers);\n      };\n      message.nack = (headers: StompHeaders = {}): void => {\n        return client.nack(messageId, subscription, headers);\n      };\n      onReceive(message);\n    },\n\n    // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n    RECEIPT: frame => {\n      const callback = this._receiptWatchers[frame.headers['receipt-id']];\n      if (callback) {\n        callback(frame);\n        // Server will acknowledge only once, remove the callback\n        delete this._receiptWatchers[frame.headers['receipt-id']];\n      } else {\n        this.onUnhandledReceipt(frame);\n      }\n    },\n\n    // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n    ERROR: frame => {\n      this.onStompError(frame);\n    },\n  };\n\n  private _setupHeartbeat(headers: StompHeaders): void {\n    if (\n      headers.version !== Versions.V1_1 &&\n      headers.version !== Versions.V1_2\n    ) {\n      return;\n    }\n\n    // It is valid for the server to not send this header\n    // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n    if (!headers['heart-beat']) {\n      return;\n    }\n\n    // heart-beat header received from the server looks like:\n    //\n    //     heart-beat: sx, sy\n    const [serverOutgoing, serverIncoming] = headers['heart-beat']\n      .split(',')\n      .map((v: string) => parseInt(v, 10));\n\n    if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n      const ttl: number = Math.max(this.heartbeatOutgoing, serverIncoming);\n      this.debug(`send PING every ${ttl}ms`);\n\n      this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n      this._pinger.start(() => {\n        if (this._webSocket.readyState === StompSocketState.OPEN) {\n          this._webSocket.send(BYTE.LF);\n          this.debug('>>> PING');\n        }\n      });\n    }\n\n    if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n      const ttl: number = Math.max(this.heartbeatIncoming, serverOutgoing);\n      this.debug(`check PONG every ${ttl}ms`);\n      this._ponger = setInterval(() => {\n        const delta = Date.now() - this._lastServerActivityTS;\n        // We wait twice the TTL to be flexible on window's setInterval calls\n        if (delta > ttl * 2) {\n          this.debug(`did not receive server activity for the last ${delta}ms`);\n          this._closeOrDiscardWebsocket();\n        }\n      }, ttl);\n    }\n  }\n\n  private _closeOrDiscardWebsocket() {\n    if (this.discardWebsocketOnCommFailure) {\n      this.debug(\n        'Discarding websocket, the underlying socket may linger for a while'\n      );\n      this.discardWebsocket();\n    } else {\n      this.debug('Issuing close on the websocket');\n      this._closeWebsocket();\n    }\n  }\n\n  public forceDisconnect() {\n    if (this._webSocket) {\n      if (\n        this._webSocket.readyState === StompSocketState.CONNECTING ||\n        this._webSocket.readyState === StompSocketState.OPEN\n      ) {\n        this._closeOrDiscardWebsocket();\n      }\n    }\n  }\n\n  public _closeWebsocket() {\n    this._webSocket.onmessage = () => {}; // ignore messages\n    this._webSocket.close();\n  }\n\n  public discardWebsocket() {\n    if (typeof this._webSocket.terminate !== 'function') {\n      augmentWebsocket(this._webSocket, (msg: string) => this.debug(msg));\n    }\n\n    // @ts-ignore - this method will be there at this stage\n    this._webSocket.terminate();\n  }\n\n  private _transmit(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    skipContentLengthHeader?: boolean;\n  }): void {\n    const { command, headers, body, binaryBody, skipContentLengthHeader } =\n      params;\n    const frame = new FrameImpl({\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues: this._escapeHeaderValues,\n      skipContentLengthHeader,\n    });\n\n    let rawChunk = frame.serialize();\n\n    if (this.logRawCommunication) {\n      this.debug(`>>> ${rawChunk}`);\n    } else {\n      this.debug(`>>> ${frame}`);\n    }\n\n    if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n      rawChunk = new TextEncoder().encode(rawChunk);\n    }\n\n    if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n      this._webSocket.send(rawChunk);\n    } else {\n      let out = rawChunk as string;\n      while (out.length > 0) {\n        const chunk = out.substring(0, this.maxWebSocketChunkSize);\n        out = out.substring(this.maxWebSocketChunkSize);\n        this._webSocket.send(chunk);\n        this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n      }\n    }\n  }\n\n  public dispose(): void {\n    if (this.connected) {\n      try {\n        // clone before updating\n        const disconnectHeaders = (Object as any).assign(\n          {},\n          this.disconnectHeaders\n        );\n\n        if (!disconnectHeaders.receipt) {\n          disconnectHeaders.receipt = `close-${this._counter++}`;\n        }\n        this.watchForReceipt(disconnectHeaders.receipt, frame => {\n          this._closeWebsocket();\n          this._cleanUp();\n          this.onDisconnect(frame);\n        });\n        this._transmit({ command: 'DISCONNECT', headers: disconnectHeaders });\n      } catch (error) {\n        this.debug(`Ignoring error during disconnect ${error}`);\n      }\n    } else {\n      if (\n        this._webSocket.readyState === StompSocketState.CONNECTING ||\n        this._webSocket.readyState === StompSocketState.OPEN\n      ) {\n        this._closeWebsocket();\n      }\n    }\n  }\n\n  private _cleanUp() {\n    this._connected = false;\n\n    if (this._pinger) {\n      this._pinger.stop();\n      this._pinger = undefined;\n    }\n    if (this._ponger) {\n      clearInterval(this._ponger);\n      this._ponger = undefined;\n    }\n  }\n\n  public publish(params: IPublishParams): void {\n    const { destination, headers, body, binaryBody, skipContentLengthHeader } =\n      params;\n    const hdrs: StompHeaders = (Object as any).assign({ destination }, headers);\n    this._transmit({\n      command: 'SEND',\n      headers: hdrs,\n      body,\n      binaryBody,\n      skipContentLengthHeader,\n    });\n  }\n\n  public watchForReceipt(receiptId: string, callback: frameCallbackType): void {\n    this._receiptWatchers[receiptId] = callback;\n  }\n\n  public subscribe(\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {}\n  ): StompSubscription {\n    headers = (Object as any).assign({}, headers);\n\n    if (!headers.id) {\n      headers.id = `sub-${this._counter++}`;\n    }\n    headers.destination = destination;\n    this._subscriptions[headers.id] = callback;\n    this._transmit({ command: 'SUBSCRIBE', headers });\n    const client = this;\n    return {\n      id: headers.id,\n\n      unsubscribe(hdrs) {\n        return client.unsubscribe(headers.id, hdrs);\n      },\n    };\n  }\n\n  public unsubscribe(id: string, headers: StompHeaders = {}): void {\n    headers = (Object as any).assign({}, headers);\n\n    delete this._subscriptions[id];\n    headers.id = id;\n    this._transmit({ command: 'UNSUBSCRIBE', headers });\n  }\n\n  public begin(transactionId: string): ITransaction {\n    const txId = transactionId || `tx-${this._counter++}`;\n    this._transmit({\n      command: 'BEGIN',\n      headers: {\n        transaction: txId,\n      },\n    });\n    const client = this;\n    return {\n      id: txId,\n      commit(): void {\n        client.commit(txId);\n      },\n      abort(): void {\n        client.abort(txId);\n      },\n    };\n  }\n\n  public commit(transactionId: string): void {\n    this._transmit({\n      command: 'COMMIT',\n      headers: {\n        transaction: transactionId,\n      },\n    });\n  }\n\n  public abort(transactionId: string): void {\n    this._transmit({\n      command: 'ABORT',\n      headers: {\n        transaction: transactionId,\n      },\n    });\n  }\n\n  public ack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    this._transmit({ command: 'ACK', headers });\n  }\n\n  public nack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    return this._transmit({ command: 'NACK', headers });\n  }\n}\n", "import { IStompSocket } from './types.js';\n\n/**\n * @internal\n */\nexport function augmentWebsocket(\n  webSocket: IStompSocket,\n  debug: (msg: string) => void\n) {\n  webSocket.terminate = function () {\n    const noOp = () => {};\n\n    // set all callbacks to no op\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n\n    const ts = new Date();\n    const id = Math.random().toString().substring(2, 8); // A simulated id\n\n    const origOnClose = this.onclose;\n\n    // Track delay in actual closure of the socket\n    this.onclose = closeEvent => {\n      const delay = new Date().getTime() - ts.getTime();\n      debug(\n        `Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`\n      );\n    };\n\n    this.close();\n\n    origOnClose?.call(webSocket, {\n      code: 4001,\n      reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n      wasClean: false,\n    });\n  };\n}\n", "/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport const BYTE = {\n  // LINEFEED byte (octet 10)\n  LF: '\\x0A',\n  // NULL byte (octet 0)\n  NULL: '\\x00',\n};\n", "import { BYTE } from './byte.js';\nimport type { <PERSON>ram<PERSON> } from './i-frame.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { IRawFrameType } from './types.js';\n\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nexport class FrameImpl implements IFrame {\n  /**\n   * STOMP Command\n   */\n  public command: string;\n\n  /**\n   * Headers, key value pairs.\n   */\n  public headers: StompHeaders;\n\n  /**\n   * Is this frame binary (based on whether body/binaryBody was passed when creating this frame).\n   */\n  public isBinaryBody: boolean;\n\n  /**\n   * body of the frame\n   */\n  get body(): string {\n    if (!this._body && this.isBinaryBody) {\n      this._body = new TextDecoder().decode(this._binaryBody);\n    }\n    return this._body || '';\n  }\n  private _body: string | undefined;\n\n  /**\n   * body as Uint8Array\n   */\n  get binaryBody(): Uint8Array {\n    if (!this._binaryBody && !this.isBinaryBody) {\n      this._binaryBody = new TextEncoder().encode(this._body);\n    }\n    // At this stage it will definitely have a valid value\n    return this._binaryBody as Uint8Array;\n  }\n  private _binaryBody: Uint8Array | undefined;\n\n  private escapeHeaderValues: boolean;\n  private skipContentLengthHeader: boolean;\n\n  /**\n   * Frame constructor. `command`, `headers` and `body` are available as properties.\n   *\n   * @internal\n   */\n  constructor(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues,\n      skipContentLengthHeader,\n    } = params;\n    this.command = command;\n    this.headers = (Object as any).assign({}, headers || {});\n\n    if (binaryBody) {\n      this._binaryBody = binaryBody;\n      this.isBinaryBody = true;\n    } else {\n      this._body = body || '';\n      this.isBinaryBody = false;\n    }\n    this.escapeHeaderValues = escapeHeaderValues || false;\n    this.skipContentLengthHeader = skipContentLengthHeader || false;\n  }\n\n  /**\n   * deserialize a STOMP Frame from raw data.\n   *\n   * @internal\n   */\n  public static fromRawFrame(\n    rawFrame: IRawFrameType,\n    escapeHeaderValues: boolean\n  ): FrameImpl {\n    const headers: StompHeaders = {};\n    const trim = (str: string): string => str.replace(/^\\s+|\\s+$/g, '');\n\n    // In case of repeated headers, as per standards, first value need to be used\n    for (const header of rawFrame.headers.reverse()) {\n      const idx = header.indexOf(':');\n\n      const key = trim(header[0]);\n      let value = trim(header[1]);\n\n      if (\n        escapeHeaderValues &&\n        rawFrame.command !== 'CONNECT' &&\n        rawFrame.command !== 'CONNECTED'\n      ) {\n        value = FrameImpl.hdrValueUnEscape(value);\n      }\n\n      headers[key] = value;\n    }\n\n    return new FrameImpl({\n      command: rawFrame.command as string,\n      headers,\n      binaryBody: rawFrame.binaryBody,\n      escapeHeaderValues,\n    });\n  }\n\n  /**\n   * @internal\n   */\n  public toString(): string {\n    return this.serializeCmdAndHeaders();\n  }\n\n  /**\n   * serialize this Frame in a format suitable to be passed to WebSocket.\n   * If the body is string the output will be string.\n   * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n   *\n   * @internal\n   */\n  public serialize(): string | ArrayBuffer {\n    const cmdAndHeaders = this.serializeCmdAndHeaders();\n\n    if (this.isBinaryBody) {\n      return FrameImpl.toUnit8Array(\n        cmdAndHeaders,\n        this._binaryBody as Uint8Array\n      ).buffer;\n    } else {\n      return cmdAndHeaders + this._body + BYTE.NULL;\n    }\n  }\n\n  private serializeCmdAndHeaders(): string {\n    const lines = [this.command];\n    if (this.skipContentLengthHeader) {\n      delete this.headers['content-length'];\n    }\n\n    for (const name of Object.keys(this.headers || {})) {\n      const value = this.headers[name];\n      if (\n        this.escapeHeaderValues &&\n        this.command !== 'CONNECT' &&\n        this.command !== 'CONNECTED'\n      ) {\n        lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n      } else {\n        lines.push(`${name}:${value}`);\n      }\n    }\n    if (\n      this.isBinaryBody ||\n      (!this.isBodyEmpty() && !this.skipContentLengthHeader)\n    ) {\n      lines.push(`content-length:${this.bodyLength()}`);\n    }\n    return lines.join(BYTE.LF) + BYTE.LF + BYTE.LF;\n  }\n\n  private isBodyEmpty(): boolean {\n    return this.bodyLength() === 0;\n  }\n\n  private bodyLength(): number {\n    const binaryBody = this.binaryBody;\n    return binaryBody ? binaryBody.length : 0;\n  }\n\n  /**\n   * Compute the size of a UTF-8 string by counting its number of bytes\n   * (and not the number of characters composing the string)\n   */\n  private static sizeOfUTF8(s: string): number {\n    return s ? new TextEncoder().encode(s).length : 0;\n  }\n\n  private static toUnit8Array(\n    cmdAndHeaders: string,\n    binaryBody: Uint8Array\n  ): Uint8Array {\n    const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n    const nullTerminator = new Uint8Array([0]);\n    const uint8Frame = new Uint8Array(\n      uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length\n    );\n\n    uint8Frame.set(uint8CmdAndHeaders);\n    uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n    uint8Frame.set(\n      nullTerminator,\n      uint8CmdAndHeaders.length + binaryBody.length\n    );\n\n    return uint8Frame;\n  }\n  /**\n   * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n   *\n   * @internal\n   */\n  public static marshall(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    escapeHeaderValues?: boolean;\n    skipContentLengthHeader?: boolean;\n  }) {\n    const frame = new FrameImpl(params);\n    return frame.serialize();\n  }\n\n  /**\n   *  Escape header values\n   */\n  private static hdrValueEscape(str: string): string {\n    return str\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/:/g, '\\\\c');\n  }\n\n  /**\n   * UnEscape header values\n   */\n  private static hdrValueUnEscape(str: string): string {\n    return str\n      .replace(/\\\\r/g, '\\r')\n      .replace(/\\\\n/g, '\\n')\n      .replace(/\\\\c/g, ':')\n      .replace(/\\\\\\\\/g, '\\\\');\n  }\n}\n", "import { IRawFrameType } from './types.js';\n\n/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class Parser {\n  private readonly _encoder = new TextEncoder();\n  private readonly _decoder = new TextDecoder();\n\n  // @ts-ignore - it always has a value\n  private _results: IRawFrameType;\n\n  private _token: number[] = [];\n  private _headerKey: string | undefined;\n  private _bodyBytesRemaining: number | undefined;\n\n  // @ts-ignore - it always has a value\n  private _onByte: (byte: number) => void;\n\n  public constructor(\n    public onFrame: (rawFrame: IRawFrameType) => void,\n    public onIncomingPing: () => void\n  ) {\n    this._initState();\n  }\n\n  public parseChunk(\n    segment: string | ArrayBuffer,\n    appendMissingNULLonIncoming: boolean = false\n  ) {\n    let chunk: Uint8Array;\n\n    if (typeof segment === 'string') {\n      chunk = this._encoder.encode(segment);\n    } else {\n      chunk = new Uint8Array(segment);\n    }\n\n    // See https://github.com/stomp-js/stompjs/issues/89\n    // Remove when underlying issue is fixed.\n    //\n    // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n    if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n      const chunkWithNull = new Uint8Array(chunk.length + 1);\n      chunkWithNull.set(chunk, 0);\n      chunkWithNull[chunk.length] = 0;\n      chunk = chunkWithNull;\n    }\n\n    // tslint:disable-next-line:prefer-for-of\n    for (let i = 0; i < chunk.length; i++) {\n      const byte = chunk[i];\n      this._onByte(byte);\n    }\n  }\n\n  // The following implements a simple Rec Descent Parser.\n  // The grammar is simple and just one byte tells what should be the next state\n\n  private _collectFrame(byte: number): void {\n    if (byte === NULL) {\n      // Ignore\n      return;\n    }\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      // Incoming Ping\n      this.onIncomingPing();\n      return;\n    }\n\n    this._onByte = this._collectCommand;\n    this._reinjectByte(byte);\n  }\n\n  private _collectCommand(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.command = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaders;\n      return;\n    }\n\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaders(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._setupCollectBody();\n      return;\n    }\n    this._onByte = this._collectHeaderKey;\n    this._reinjectByte(byte);\n  }\n\n  private _reinjectByte(byte: number) {\n    this._onByte(byte);\n  }\n\n  private _collectHeaderKey(byte: number): void {\n    if (byte === COLON) {\n      this._headerKey = this._consumeTokenAsUTF8();\n      this._onByte = this._collectHeaderValue;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectHeaderValue(byte: number): void {\n    if (byte === CR) {\n      // Ignore CR\n      return;\n    }\n    if (byte === LF) {\n      this._results.headers.push([\n        this._headerKey as string,\n        this._consumeTokenAsUTF8(),\n      ]);\n      this._headerKey = undefined;\n      this._onByte = this._collectHeaders;\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _setupCollectBody() {\n    const contentLengthHeader = this._results.headers.filter(\n      (header: [string, string]) => {\n        return header[0] === 'content-length';\n      }\n    )[0];\n\n    if (contentLengthHeader) {\n      this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n      this._onByte = this._collectBodyFixedSize;\n    } else {\n      this._onByte = this._collectBodyNullTerminated;\n    }\n  }\n\n  private _collectBodyNullTerminated(byte: number): void {\n    if (byte === NULL) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _collectBodyFixedSize(byte: number): void {\n    // It is post decrement, so that we discard the trailing NULL octet\n    if ((this._bodyBytesRemaining as number)-- === 0) {\n      this._retrievedBody();\n      return;\n    }\n    this._consumeByte(byte);\n  }\n\n  private _retrievedBody() {\n    this._results.binaryBody = this._consumeTokenAsRaw();\n\n    try {\n      this.onFrame(this._results);\n    } catch (e) {\n      console.log(\n        `Ignoring an exception thrown by a frame handler. Original exception: `,\n        e\n      );\n    }\n\n    this._initState();\n  }\n\n  // Rec Descent Parser helpers\n\n  private _consumeByte(byte: number) {\n    this._token.push(byte);\n  }\n\n  private _consumeTokenAsUTF8() {\n    return this._decoder.decode(this._consumeTokenAsRaw());\n  }\n\n  private _consumeTokenAsRaw() {\n    const rawResult = new Uint8Array(this._token);\n    this._token = [];\n    return rawResult;\n  }\n\n  private _initState() {\n    this._results = {\n      command: undefined,\n      headers: [],\n      binaryBody: undefined,\n    };\n\n    this._token = [];\n    this._headerKey = undefined;\n\n    this._onByte = this._collectFrame;\n  }\n}\n", "import { debugFnType, TickerStrategy } from './types.js';\n\nexport class Ticker {\n  private readonly _workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n\n  private _worker?: Worker;\n  private _timer?: any;\n\n  constructor(\n    private readonly _interval: number,\n    private readonly _strategy = TickerStrategy.Interval,\n    private readonly _debug: debugFnType) {\n  }\n\n  public start(tick: (elapsedTime: number) => void): void {\n    this.stop();\n\n    if (this.shouldUseWorker()) {\n      this.runWorker(tick);\n    } else {\n      this.runInterval(tick);\n    }\n  }\n\n  public stop(): void {\n    this.disposeWorker();\n    this.disposeInterval();\n  }\n\n  private shouldUseWorker(): boolean {\n    return typeof(Worker) !== 'undefined' && this._strategy === TickerStrategy.Worker\n  }\n\n  private runWorker(tick: (elapsedTime: number) => void): void {\n    this._debug('Using runWorker for outgoing pings');\n    if (!this._worker) {\n      this._worker = new Worker(\n        URL.createObjectURL(\n          new Blob([this._workerScript], { type: 'text/javascript' })\n        )\n      );\n      this._worker.onmessage = (message) => tick(message.data);\n    }\n  }\n\n  private runInterval(tick: (elapsedTime: number) => void): void {\n    this._debug('Using runInterval for outgoing pings');\n    if (!this._timer) {\n      const startTime = Date.now();\n      this._timer = setInterval(() => {\n        tick(Date.now() - startTime);\n      }, this._interval);\n    }\n  }\n\n  private disposeWorker(): void {\n    if (this._worker) {\n      this._worker.terminate();\n      delete this._worker;\n      this._debug('Outgoing ping disposeWorker');\n    }\n  }\n\n  private disposeInterval(): void {\n    if (this._timer) {\n      clearInterval(this._timer);\n      delete this._timer;\n      this._debug('Outgoing ping disposeInterval');\n    }\n  }\n}\n", "import type { IFrame } from './i-frame.js';\nimport type { IMessage } from './i-message.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { Versions } from './versions.js';\n\n/**\n * This callback will receive a `string` as a parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type debugFnType = (msg: string) => void;\n\n/**\n * This callback will receive a {@link IMessage} as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type messageCallbackType = (message: IMessage) => void;\n\n/**\n * This callback will receive a {@link IFrame} as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type frameCallbackType = ((frame: IFrame) => void) | (() => void);\n\n/**\n * This callback will receive a [CloseEvent]{@link https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent}\n * as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type closeEventCallbackType<T = any> = (evt: T) => void;\n\n/**\n * This callback will receive an [Event]{@link https://developer.mozilla.org/en-US/docs/Web/API/Event}\n * as parameter.\n *\n * Part of `@stomp/stompjs`.\n */\nexport type wsErrorCallbackType<T = any> = (evt: T) => void;\n\n/**\n * Parameters for [Client#publish]{@link Client#publish}.\n * Aliased as publishParams as well.\n *\n * Part of `@stomp/stompjs`.\n */\nexport interface IPublishParams {\n  /**\n   * destination end point\n   */\n  destination: string;\n  /**\n   * headers (optional)\n   */\n  headers?: StompHeaders;\n  /**\n   * body (optional)\n   */\n  body?: string;\n  /**\n   * binary body (optional)\n   */\n  binaryBody?: Uint8Array;\n  /**\n   * By default, a `content-length` header will be added in the Frame to the broker.\n   * Set it to `true` for the header to be skipped.\n   */\n  skipContentLengthHeader?: boolean;\n}\n\n/**\n * Backward compatibility, switch to {@link IPublishParams}.\n */\nexport type publishParams = IPublishParams;\n\n/**\n * Used in {@link IRawFrameType}\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport type RawHeaderType = [string, string];\n\n/**\n * The parser yield frames in this structure\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport interface IRawFrameType {\n  command: string | undefined;\n  headers: RawHeaderType[];\n  binaryBody: Uint8Array | undefined;\n}\n\n/**\n * @internal\n */\nexport interface IStompSocketMessageEvent {\n  data?: string | ArrayBuffer;\n}\n\n/**\n * Copied from Websocket interface to avoid dom typelib dependency.\n *\n * @internal\n */\nexport interface IStompSocket {\n  url: string;\n  onclose: ((ev?: any) => any) | undefined | null;\n  onerror: ((ev: any) => any) | undefined | null;\n  onmessage: ((ev: IStompSocketMessageEvent) => any) | undefined | null;\n  onopen: ((ev?: any) => any) | undefined | null;\n  terminate?: (() => any) | undefined | null;\n\n  /**\n   * Returns a string that indicates how binary data from the socket is exposed to scripts:\n   * We support only 'arraybuffer'.\n   */\n  binaryType?: string;\n\n  /**\n   * Returns the state of the socket connection. It can have the values of StompSocketState.\n   */\n  readonly readyState: number;\n\n  /**\n   * Closes the connection.\n   */\n  close(): void;\n  /**\n   * Transmits data using the connection. data can be a string or an ArrayBuffer.\n   */\n  send(data: string | ArrayBuffer): void;\n}\n\n/**\n * Possible states for the IStompSocket\n */\nexport enum StompSocketState {\n  CONNECTING,\n  OPEN,\n  CLOSING,\n  CLOSED,\n}\n\n/**\n * Possible activation state\n */\nexport enum ActivationState {\n  ACTIVE,\n  DEACTIVATING,\n  INACTIVE,\n}\n\n/**\n * Possible reconnection wait time modes\n */\nexport enum ReconnectionTimeMode {\n  LINEAR,\n  EXPONENTIAL\n}\n\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nexport enum TickerStrategy {\n  Interval = 'interval',\n  Worker = 'worker'\n}\n\n/**\n * @internal\n */\nexport interface IStomptHandlerConfig {\n  debug: debugFnType;\n  stompVersions: Versions;\n  connectHeaders: StompHeaders;\n  disconnectHeaders: StompHeaders;\n  heartbeatIncoming: number;\n  heartbeatOutgoing: number;\n  heartbeatStrategy: TickerStrategy;\n  splitLargeFrames: boolean;\n  maxWebSocketChunkSize: number;\n  forceBinaryWSFrames: boolean;\n  logRawCommunication: boolean;\n  appendMissingNULLonIncoming: boolean;\n  discardWebsocketOnCommFailure: boolean;\n  onConnect: frameCallbackType;\n  onDisconnect: frameCallbackType;\n  onStompError: frameCallbackType;\n  onWebSocketClose: closeEventCallbackType;\n  onWebSocketError: wsErrorCallbackType;\n  onUnhandledMessage: messageCallbackType;\n  onUnhandledReceipt: frameCallbackType;\n  onUnhandledFrame: frameCallbackType;\n}\n", "/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nexport class Versions {\n  /**\n   * Indicates protocol version 1.0\n   */\n  public static V1_0 = '1.0';\n  /**\n   * Indicates protocol version 1.1\n   */\n  public static V1_1 = '1.1';\n  /**\n   * Indicates protocol version 1.2\n   */\n  public static V1_2 = '1.2';\n\n  /**\n   * @internal\n   */\n  public static default = new Versions([\n    Versions.V1_2,\n    Versions.V1_1,\n    Versions.V1_0,\n  ]);\n\n  /**\n   * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n   *\n   * You will be creating an instance of this class if you want to override\n   * supported versions to be declared during STOMP handshake.\n   */\n  constructor(public versions: string[]) {}\n\n  /**\n   * Used as part of CONNECT STOMP Frame\n   */\n  public supportedVersions() {\n    return this.versions.join(',');\n  }\n\n  /**\n   * Used while creating a WebSocket\n   */\n  public protocolVersions() {\n    return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n  }\n}\n", "export {};\n//# sourceMappingURL=i-frame.js.map", "export {};\n//# sourceMappingURL=i-message.js.map", "import { StompHeaders } from './stomp-headers.js';\nimport {\n  ActivationState,\n  TickerStrategy,\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  messageCallbackType,\n  ReconnectionTimeMode,\n  wsErrorCallbackType,\n} from './types.js';\nimport { Versions } from './versions.js';\nimport { Client } from './client.js';\n\n/**\n * Configuration options for STOMP Client, each key corresponds to\n * field by the same name in {@link Client}. This can be passed to\n * the constructor of {@link Client} or to [Client#configure]{@link Client#configure}.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompConfig {\n  /**\n   * See [Client#brokerURL]{@link Client#brokerURL}.\n   */\n  public brokerURL?: string;\n\n  /**\n   * See [Client#stompVersions]{@link Client#stompVersions}.\n   */\n  public stompVersions?: Versions;\n\n  /**\n   * See [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   */\n  public webSocketFactory?: () => any;\n\n  /**\n   * See [Client#connectionTimeout]{@link Client#connectionTimeout}.\n   */\n  public connectionTimeout?: number;\n\n  /**\n   * See [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   */\n  public reconnectDelay?: number;\n\n  /**\n   * See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay}\n   */\n  public maxReconnectDelay?: number;\n\n  /**\n   * See [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n   */\n  public reconnectTimeMode?: ReconnectionTimeMode;\n\n  /**\n   * See [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}.\n   */\n  public heartbeatIncoming?: number;\n\n  /**\n   * See [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   */\n  public heartbeatOutgoing?: number;\n\n  /**\n   * See [Client#heartbeatStrategy]{@link Client#heartbeatStrategy}.\n   */\n  public heartbeatStrategy?: TickerStrategy;\n\n  /**\n   * See [Client#splitLargeFrames]{@link Client#splitLargeFrames}.\n   */\n  public splitLargeFrames?: boolean;\n\n  /**\n   * See [Client#forceBinaryWSFrames]{@link Client#forceBinaryWSFrames}.\n   */\n  public forceBinaryWSFrames?: boolean;\n\n  /**\n   * See [Client#appendMissingNULLonIncoming]{@link Client#appendMissingNULLonIncoming}.\n   */\n  public appendMissingNULLonIncoming?: boolean;\n\n  /**\n   * See [Client#maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n   */\n  public maxWebSocketChunkSize?: number;\n\n  /**\n   * See [Client#connectHeaders]{@link Client#connectHeaders}.\n   */\n  public connectHeaders?: StompHeaders;\n\n  /**\n   * See [Client#disconnectHeaders]{@link Client#disconnectHeaders}.\n   */\n  public disconnectHeaders?: StompHeaders;\n\n  /**\n   * See [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   */\n  public onUnhandledMessage?: messageCallbackType;\n\n  /**\n   * See [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   */\n  public onUnhandledReceipt?: frameCallbackType;\n\n  /**\n   * See [Client#onUnhandledFrame]{@link Client#onUnhandledFrame}.\n   */\n  public onUnhandledFrame?: frameCallbackType;\n\n  /**\n   * See [Client#beforeConnect]{@link Client#beforeConnect}.\n   */\n  public beforeConnect?: (client: Client) => void | Promise<void>;\n\n  /**\n   * See [Client#onConnect]{@link Client#onConnect}.\n   */\n  public onConnect?: frameCallbackType;\n\n  /**\n   * See [Client#onDisconnect]{@link Client#onDisconnect}.\n   */\n  public onDisconnect?: frameCallbackType;\n\n  /**\n   * See [Client#onStompError]{@link Client#onStompError}.\n   */\n  public onStompError?: frameCallbackType;\n\n  /**\n   * See [Client#onWebSocketClose]{@link Client#onWebSocketClose}.\n   */\n  public onWebSocketClose?: closeEventCallbackType;\n\n  /**\n   * See [Client#onWebSocketError]{@link Client#onWebSocketError}.\n   */\n  public onWebSocketError?: wsErrorCallbackType;\n\n  /**\n   * See [Client#logRawCommunication]{@link Client#logRawCommunication}.\n   */\n  public logRawCommunication?: boolean;\n\n  /**\n   * See [Client#debug]{@link Client#debug}.\n   */\n  public debug?: debugFnType;\n\n  /**\n   * See [Client#discardWebsocketOnCommFailure]{@link Client#discardWebsocketOnCommFailure}.\n   */\n  public discardWebsocketOnCommFailure?: boolean;\n\n  /**\n   * See [Client#onChangeState]{@link Client#onChangeState}.\n   */\n  public onChangeState?: (state: ActivationState) => void;\n}\n", "/**\n * STOMP headers. Many functions calls will accept headers as parameters.\n * The headers sent by <PERSON><PERSON><PERSON> will be available as [IFrame#headers]{@link IFrame#headers}.\n *\n * `key` and `value` must be valid strings.\n * In addition, `key` must not contain `CR`, `LF`, or `:`.\n *\n * Part of `@stomp/stompjs`.\n */\nexport class StompHeaders {\n  [key: string]: string;\n}\n", "export {};\n//# sourceMappingURL=stomp-subscription.js.map", "export {};\n//# sourceMappingURL=i-transaction.js.map", "import { Client } from '../client.js';\nimport { StompHeaders } from '../stomp-headers.js';\nimport { frameCallbackType, messageCallbackType } from '../types.js';\nimport { HeartbeatInfo } from './heartbeat-info.js';\n\n/**\n * Available for backward compatibility, please shift to using {@link Client}.\n *\n * **Deprecated**\n *\n * Part of `@stomp/stompjs`.\n *\n * To upgrade, please follow the [Upgrade Guide](https://stomp-js.github.io/guide/stompjs/upgrading-stompjs.html)\n */\nexport class CompatClient extends Client {\n  /**\n   * It is no op now. No longer needed. Large packets work out of the box.\n   */\n  public maxWebSocketFrameSize: number = 16 * 1024;\n\n  /**\n   * Available for backward compatibility, please shift to using {@link Client}\n   * and [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   *\n   * **Deprecated**\n   *\n   * @internal\n   */\n  constructor(webSocketFactory: () => any) {\n    super();\n    this.reconnect_delay = 0;\n    this.webSocketFactory = webSocketFactory;\n    // Default from previous version\n    this.debug = (...message: any[]) => {\n      console.log(...message);\n    };\n  }\n\n  private _parseConnect(...args: any[]): any {\n    let closeEventCallback;\n    let connectCallback;\n    let errorCallback;\n    let headers: StompHeaders = {};\n    if (args.length < 2) {\n      throw new Error('Connect requires at least 2 arguments');\n    }\n    if (typeof args[1] === 'function') {\n      [headers, connectCallback, errorCallback, closeEventCallback] = args;\n    } else {\n      switch (args.length) {\n        case 6:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n            headers.host,\n          ] = args;\n          break;\n        default:\n          [\n            headers.login,\n            headers.passcode,\n            connectCallback,\n            errorCallback,\n            closeEventCallback,\n          ] = args;\n      }\n    }\n\n    return [headers, connectCallback, errorCallback, closeEventCallback];\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#activate]{@link Client#activate}.\n   *\n   * **Deprecated**\n   *\n   * The `connect` method accepts different number of arguments and types. See the Overloads list. Use the\n   * version with headers to pass your broker specific options.\n   *\n   * overloads:\n   * - connect(headers, connectCallback)\n   * - connect(headers, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback)\n   * - connect(login, passcode, connectCallback, errorCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback)\n   * - connect(login, passcode, connectCallback, errorCallback, closeEventCallback, host)\n   *\n   * params:\n   * - headers, see [Client#connectHeaders]{@link Client#connectHeaders}\n   * - connectCallback, see [Client#onConnect]{@link Client#onConnect}\n   * - errorCallback, see [Client#onStompError]{@link Client#onStompError}\n   * - closeEventCallback, see [Client#onWebSocketClose]{@link Client#onWebSocketClose}\n   * - login [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - passcode [String], [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   * - host [String], see [Client#connectHeaders](../classes/Client.html#connectHeaders)\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public connect(...args: any[]): void {\n    const out = this._parseConnect(...args);\n\n    if (out[0]) {\n      this.connectHeaders = out[0];\n    }\n    if (out[1]) {\n      this.onConnect = out[1];\n    }\n    if (out[2]) {\n      this.onStompError = out[2];\n    }\n    if (out[3]) {\n      this.onWebSocketClose = out[3];\n    }\n\n    super.activate();\n  }\n\n  /**\n   * Available for backward compatibility, please shift to using [Client#deactivate]{@link Client#deactivate}.\n   *\n   * **Deprecated**\n   *\n   * See:\n   * [Client#onDisconnect]{@link Client#onDisconnect}, and\n   * [Client#disconnectHeaders]{@link Client#disconnectHeaders}\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public disconnect(\n    disconnectCallback?: any,\n    headers: StompHeaders = {}\n  ): void {\n    if (disconnectCallback) {\n      this.onDisconnect = disconnectCallback;\n    }\n    this.disconnectHeaders = headers;\n\n    super.deactivate();\n  }\n\n  /**\n   * Available for backward compatibility, use [Client#publish]{@link Client#publish}.\n   *\n   * Send a message to a named destination. Refer to your STOMP broker documentation for types\n   * and naming of destinations. The headers will, typically, be available to the subscriber.\n   * However, there may be special purpose headers corresponding to your STOMP broker.\n   *\n   *  **Deprecated**, use [Client#publish]{@link Client#publish}\n   *\n   * Note: Body must be String. You will need to covert the payload to string in case it is not string (e.g. JSON)\n   *\n   * ```javascript\n   *        client.send(\"/queue/test\", {priority: 9}, \"Hello, STOMP\");\n   *\n   *        // If you want to send a message with a body, you must also pass the headers argument.\n   *        client.send(\"/queue/test\", {}, \"Hello, STOMP\");\n   * ```\n   *\n   * To upgrade, please follow the [Upgrade Guide](../additional-documentation/upgrading.html)\n   */\n  public send(\n    destination: string,\n    headers: { [key: string]: any } = {},\n    body: string = ''\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    const skipContentLengthHeader = headers['content-length'] === false;\n    if (skipContentLengthHeader) {\n      delete headers['content-length'];\n    }\n    this.publish({\n      destination,\n      headers: headers as StompHeaders,\n      body,\n      skipContentLengthHeader,\n    });\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#reconnectDelay]{@link Client#reconnectDelay}.\n   *\n   * **Deprecated**\n   */\n  set reconnect_delay(value: number) {\n    this.reconnectDelay = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#webSocket]{@link Client#webSocket}.\n   *\n   * **Deprecated**\n   */\n  get ws(): any {\n    return this.webSocket;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#connectedVersion]{@link Client#connectedVersion}.\n   *\n   * **Deprecated**\n   */\n  get version() {\n    return this.connectedVersion;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  get onreceive(): messageCallbackType {\n    return this.onUnhandledMessage;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledMessage]{@link Client#onUnhandledMessage}.\n   *\n   * **Deprecated**\n   */\n  set onreceive(value: messageCallbackType) {\n    this.onUnhandledMessage = value;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   * Prefer using [Client#watchForReceipt]{@link Client#watchForReceipt}.\n   *\n   * **Deprecated**\n   */\n  get onreceipt(): frameCallbackType {\n    return this.onUnhandledReceipt;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#onUnhandledReceipt]{@link Client#onUnhandledReceipt}.\n   *\n   * **Deprecated**\n   */\n  set onreceipt(value: frameCallbackType) {\n    this.onUnhandledReceipt = value;\n  }\n\n  private _heartbeatInfo: HeartbeatInfo = new HeartbeatInfo(this);\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  get heartbeat() {\n    return this._heartbeatInfo;\n  }\n\n  /**\n   * Available for backward compatibility, renamed to [Client#heartbeatIncoming]{@link Client#heartbeatIncoming}\n   * [Client#heartbeatOutgoing]{@link Client#heartbeatOutgoing}.\n   *\n   * **Deprecated**\n   */\n  set heartbeat(value: { incoming: number; outgoing: number }) {\n    this.heartbeatIncoming = value.incoming;\n    this.heartbeatOutgoing = value.outgoing;\n  }\n}\n", "import { CompatClient } from './compat-client.js';\n\n/**\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class HeartbeatInfo {\n  constructor(private client: CompatClient) {}\n\n  get outgoing(): number {\n    return this.client.heartbeatOutgoing;\n  }\n\n  set outgoing(value: number) {\n    this.client.heartbeatOutgoing = value;\n  }\n\n  get incoming(): number {\n    return this.client.heartbeatIncoming;\n  }\n\n  set incoming(value: number) {\n    this.client.heartbeatIncoming = value;\n  }\n}\n", "import { Versions } from '../versions.js';\nimport { CompatClient } from './compat-client.js';\nimport { IStompSocket } from '../types.js';\n\n/**\n * @internal\n */\ndeclare const WebSocket: {\n  prototype: IStompSocket;\n  new (url: string, protocols?: string | string[]): IStompSocket;\n};\n\n/**\n * STOMP Class, acts like a factory to create {@link Client}.\n *\n * Part of `@stomp/stompjs`.\n *\n * **Deprecated**\n *\n * It will be removed in next major version. Please switch to {@link Client}.\n */\nexport class Stomp {\n  /**\n   * In case you need to use a non standard class for WebSocket.\n   *\n   * For example when using within NodeJS environment:\n   *\n   * ```javascript\n   *        StompJs = require('../../esm5/');\n   *        Stomp = StompJs.Stomp;\n   *        Stomp.WebSocketClass = require('websocket').w3cwebsocket;\n   * ```\n   *\n   * **Deprecated**\n   *\n   *\n   * It will be removed in next major version. Please switch to {@link Client}\n   * using [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   */\n  // tslint:disable-next-line:variable-name\n  public static WebSocketClass: any = null;\n\n  /**\n   * This method creates a WebSocket client that is connected to\n   * the STOMP server located at the url.\n   *\n   * ```javascript\n   *        var url = \"ws://localhost:61614/stomp\";\n   *        var client = Stomp.client(url);\n   * ```\n   *\n   * **Deprecated**\n   *\n   * It will be removed in next major version. Please switch to {@link Client}\n   * using [Client#brokerURL]{@link Client#brokerURL}.\n   */\n  public static client(url: string, protocols?: string[]): CompatClient {\n    // This is a hack to allow another implementation than the standard\n    // HTML5 WebSocket class.\n    //\n    // It is possible to use another class by calling\n    //\n    //     Stomp.WebSocketClass = MozWebSocket\n    //\n    // *prior* to call `Stomp.client()`.\n    //\n    // This hack is deprecated and `Stomp.over()` method should be used\n    // instead.\n\n    // See remarks on the function Stomp.over\n    if (protocols == null) {\n      protocols = Versions.default.protocolVersions();\n    }\n    const wsFn = () => {\n      const klass = Stomp.WebSocketClass || WebSocket;\n      return new klass(url, protocols);\n    };\n\n    return new CompatClient(wsFn);\n  }\n\n  /**\n   * This method is an alternative to [Stomp#client]{@link Stomp#client} to let the user\n   * specify the WebSocket to use (either a standard HTML5 WebSocket or\n   * a similar object).\n   *\n   * In order to support reconnection, the function Client._connect should be callable more than once.\n   * While reconnecting\n   * a new instance of underlying transport (TCP Socket, WebSocket or SockJS) will be needed. So, this function\n   * alternatively allows passing a function that should return a new instance of the underlying socket.\n   *\n   * ```javascript\n   *        var client = Stomp.over(function(){\n   *          return new WebSocket('ws://localhost:15674/ws')\n   *        });\n   * ```\n   *\n   * **Deprecated**\n   *\n   * It will be removed in next major version. Please switch to {@link Client}\n   * using [Client#webSocketFactory]{@link Client#webSocketFactory}.\n   */\n  public static over(ws: any): CompatClient {\n    let wsFn: () => any;\n\n    if (typeof ws === 'function') {\n      wsFn = ws;\n    } else {\n      console.warn(\n        'Stomp.over did not receive a factory, auto reconnect will not work. ' +\n          'Please see https://stomp-js.github.io/api-docs/latest/classes/Stomp.html#over'\n      );\n      wsFn = () => ws;\n    }\n\n    return new CompatClient(wsFn);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACEA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACFA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACEA;IAAAC,eAAA;IAAAA,eAAA;AAGM,SAAU,iBACd,WACA,OAA4B;AAE5B,YAAU,YAAY,WAAA;AACpB,UAAM,OAAO,MAAK;IAAE;AAGpB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AAEd,UAAM,KAAK,oBAAI,KAAI;AACnB,UAAM,KAAK,KAAK,OAAM,EAAG,SAAQ,EAAG,UAAU,GAAG,CAAC;AAElD,UAAM,cAAc,KAAK;AAGzB,SAAK,UAAU,gBAAa;AAC1B,YAAM,SAAQ,oBAAI,KAAI,GAAG,QAAO,IAAK,GAAG,QAAO;AAC/C,YACE,sBAAsB,EAAE,mBAAmB,KAAK,yBAAyB,WAAW,IAAI,IAAI,WAAW,MAAM,EAAE;IAEnH;AAEA,SAAK,MAAK;AAEV,+CAAa,KAAK,WAAW;MAC3B,MAAM;MACN,QAAQ,6BAA6B,EAAE;MACvC,UAAU;;EAEd;AACF;;;ACtCA,IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;AAOO,IAAM,OAAO;;EAElB,IAAI;;EAEJ,MAAM;;;;ACXR,IAAAC,eAAA;IAAAA,eAAA;IAAAA,eAAA;AAUM,IAAO,YAAP,MAAO,WAAS;;;;EAmBpB,IAAI,OAAI;AACN,QAAI,CAAC,KAAK,SAAS,KAAK,cAAc;AACpC,WAAK,QAAQ,IAAI,YAAW,EAAG,OAAO,KAAK,WAAW;IACxD;AACA,WAAO,KAAK,SAAS;EACvB;;;;EAMA,IAAI,aAAU;AACZ,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,cAAc;AAC3C,WAAK,cAAc,IAAI,YAAW,EAAG,OAAO,KAAK,KAAK;IACxD;AAEA,WAAO,KAAK;EACd;;;;;;EAWA,YAAY,QAOX;AACC,UAAM,EACJ,SACA,SACA,MACA,YACA,oBACA,wBAAuB,IACrB;AACJ,SAAK,UAAU;AACf,SAAK,UAAW,OAAe,OAAO,CAAA,GAAI,WAAW,CAAA,CAAE;AAEvD,QAAI,YAAY;AACd,WAAK,cAAc;AACnB,WAAK,eAAe;IACtB,OAAO;AACL,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe;IACtB;AACA,SAAK,qBAAqB,sBAAsB;AAChD,SAAK,0BAA0B,2BAA2B;EAC5D;;;;;;EAOO,OAAO,aACZ,UACA,oBAA2B;AAE3B,UAAM,UAAwB,CAAA;AAC9B,UAAM,OAAO,CAAC,QAAwB,IAAI,QAAQ,cAAc,EAAE;AAGlE,eAAW,UAAU,SAAS,QAAQ,QAAO,GAAI;AAC/C,YAAM,MAAM,OAAO,QAAQ,GAAG;AAE9B,YAAM,MAAM,KAAK,OAAO,CAAC,CAAC;AAC1B,UAAI,QAAQ,KAAK,OAAO,CAAC,CAAC;AAE1B,UACE,sBACA,SAAS,YAAY,aACrB,SAAS,YAAY,aACrB;AACA,gBAAQ,WAAU,iBAAiB,KAAK;MAC1C;AAEA,cAAQ,GAAG,IAAI;IACjB;AAEA,WAAO,IAAI,WAAU;MACnB,SAAS,SAAS;MAClB;MACA,YAAY,SAAS;MACrB;KACD;EACH;;;;EAKO,WAAQ;AACb,WAAO,KAAK,uBAAsB;EACpC;;;;;;;;EASO,YAAS;AACd,UAAM,gBAAgB,KAAK,uBAAsB;AAEjD,QAAI,KAAK,cAAc;AACrB,aAAO,WAAU,aACf,eACA,KAAK,WAAyB,EAC9B;IACJ,OAAO;AACL,aAAO,gBAAgB,KAAK,QAAQ,KAAK;IAC3C;EACF;EAEQ,yBAAsB;AAC5B,UAAM,QAAQ,CAAC,KAAK,OAAO;AAC3B,QAAI,KAAK,yBAAyB;AAChC,aAAO,KAAK,QAAQ,gBAAgB;IACtC;AAEA,eAAW,QAAQ,OAAO,KAAK,KAAK,WAAW,CAAA,CAAE,GAAG;AAClD,YAAM,QAAQ,KAAK,QAAQ,IAAI;AAC/B,UACE,KAAK,sBACL,KAAK,YAAY,aACjB,KAAK,YAAY,aACjB;AACA,cAAM,KAAK,GAAG,IAAI,IAAI,WAAU,eAAe,GAAG,KAAK,EAAE,CAAC,EAAE;MAC9D,OAAO;AACL,cAAM,KAAK,GAAG,IAAI,IAAI,KAAK,EAAE;MAC/B;IACF;AACA,QACE,KAAK,gBACJ,CAAC,KAAK,YAAW,KAAM,CAAC,KAAK,yBAC9B;AACA,YAAM,KAAK,kBAAkB,KAAK,WAAU,CAAE,EAAE;IAClD;AACA,WAAO,MAAM,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK;EAC9C;EAEQ,cAAW;AACjB,WAAO,KAAK,WAAU,MAAO;EAC/B;EAEQ,aAAU;AAChB,UAAM,aAAa,KAAK;AACxB,WAAO,aAAa,WAAW,SAAS;EAC1C;;;;;EAMQ,OAAO,WAAW,GAAS;AACjC,WAAO,IAAI,IAAI,YAAW,EAAG,OAAO,CAAC,EAAE,SAAS;EAClD;EAEQ,OAAO,aACb,eACA,YAAsB;AAEtB,UAAM,qBAAqB,IAAI,YAAW,EAAG,OAAO,aAAa;AACjE,UAAM,iBAAiB,IAAI,WAAW,CAAC,CAAC,CAAC;AACzC,UAAM,aAAa,IAAI,WACrB,mBAAmB,SAAS,WAAW,SAAS,eAAe,MAAM;AAGvE,eAAW,IAAI,kBAAkB;AACjC,eAAW,IAAI,YAAY,mBAAmB,MAAM;AACpD,eAAW,IACT,gBACA,mBAAmB,SAAS,WAAW,MAAM;AAG/C,WAAO;EACT;;;;;;EAMO,OAAO,SAAS,QAOtB;AACC,UAAM,QAAQ,IAAI,WAAU,MAAM;AAClC,WAAO,MAAM,UAAS;EACxB;;;;EAKQ,OAAO,eAAe,KAAW;AACvC,WAAO,IACJ,QAAQ,OAAO,MAAM,EACrB,QAAQ,OAAO,KAAK,EACpB,QAAQ,OAAO,KAAK,EACpB,QAAQ,MAAM,KAAK;EACxB;;;;EAKQ,OAAO,iBAAiB,KAAW;AACzC,WAAO,IACJ,QAAQ,QAAQ,IAAI,EACpB,QAAQ,QAAQ,IAAI,EACpB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,SAAS,IAAI;EAC1B;;;;AC1PF,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAGA,IAAM,OAAO;AAIb,IAAM,KAAK;AAIX,IAAM,KAAK;AAIX,IAAM,QAAQ;AA4CR,IAAO,SAAP,MAAa;EAcjB,YACS,SACA,gBAA0B;AAD1B,SAAA,UAAA;AACA,SAAA,iBAAA;AAfQ,SAAA,WAAW,IAAI,YAAW;AAC1B,SAAA,WAAW,IAAI,YAAW;AAKnC,SAAA,SAAmB,CAAA;AAWzB,SAAK,WAAU;EACjB;EAEO,WACL,SACA,8BAAuC,OAAK;AAE5C,QAAI;AAEJ,QAAI,OAAO,YAAY,UAAU;AAC/B,cAAQ,KAAK,SAAS,OAAO,OAAO;IACtC,OAAO;AACL,cAAQ,IAAI,WAAW,OAAO;IAChC;AAMA,QAAI,+BAA+B,MAAM,MAAM,SAAS,CAAC,MAAM,GAAG;AAChE,YAAM,gBAAgB,IAAI,WAAW,MAAM,SAAS,CAAC;AACrD,oBAAc,IAAI,OAAO,CAAC;AAC1B,oBAAc,MAAM,MAAM,IAAI;AAC9B,cAAQ;IACV;AAGA,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,OAAO,MAAM,CAAC;AACpB,WAAK,QAAQ,IAAI;IACnB;EACF;;;EAKQ,cAAc,MAAY;AAChC,QAAI,SAAS,MAAM;AAEjB;IACF;AACA,QAAI,SAAS,IAAI;AAEf;IACF;AACA,QAAI,SAAS,IAAI;AAEf,WAAK,eAAc;AACnB;IACF;AAEA,SAAK,UAAU,KAAK;AACpB,SAAK,cAAc,IAAI;EACzB;EAEQ,gBAAgB,MAAY;AAClC,QAAI,SAAS,IAAI;AAEf;IACF;AACA,QAAI,SAAS,IAAI;AACf,WAAK,SAAS,UAAU,KAAK,oBAAmB;AAChD,WAAK,UAAU,KAAK;AACpB;IACF;AAEA,SAAK,aAAa,IAAI;EACxB;EAEQ,gBAAgB,MAAY;AAClC,QAAI,SAAS,IAAI;AAEf;IACF;AACA,QAAI,SAAS,IAAI;AACf,WAAK,kBAAiB;AACtB;IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,cAAc,IAAI;EACzB;EAEQ,cAAc,MAAY;AAChC,SAAK,QAAQ,IAAI;EACnB;EAEQ,kBAAkB,MAAY;AACpC,QAAI,SAAS,OAAO;AAClB,WAAK,aAAa,KAAK,oBAAmB;AAC1C,WAAK,UAAU,KAAK;AACpB;IACF;AACA,SAAK,aAAa,IAAI;EACxB;EAEQ,oBAAoB,MAAY;AACtC,QAAI,SAAS,IAAI;AAEf;IACF;AACA,QAAI,SAAS,IAAI;AACf,WAAK,SAAS,QAAQ,KAAK;QACzB,KAAK;QACL,KAAK,oBAAmB;OACzB;AACD,WAAK,aAAa;AAClB,WAAK,UAAU,KAAK;AACpB;IACF;AACA,SAAK,aAAa,IAAI;EACxB;EAEQ,oBAAiB;AACvB,UAAM,sBAAsB,KAAK,SAAS,QAAQ,OAChD,CAAC,WAA4B;AAC3B,aAAO,OAAO,CAAC,MAAM;IACvB,CAAC,EACD,CAAC;AAEH,QAAI,qBAAqB;AACvB,WAAK,sBAAsB,SAAS,oBAAoB,CAAC,GAAG,EAAE;AAC9D,WAAK,UAAU,KAAK;IACtB,OAAO;AACL,WAAK,UAAU,KAAK;IACtB;EACF;EAEQ,2BAA2B,MAAY;AAC7C,QAAI,SAAS,MAAM;AACjB,WAAK,eAAc;AACnB;IACF;AACA,SAAK,aAAa,IAAI;EACxB;EAEQ,sBAAsB,MAAY;AAExC,QAAK,KAAK,0BAAqC,GAAG;AAChD,WAAK,eAAc;AACnB;IACF;AACA,SAAK,aAAa,IAAI;EACxB;EAEQ,iBAAc;AACpB,SAAK,SAAS,aAAa,KAAK,mBAAkB;AAElD,QAAI;AACF,WAAK,QAAQ,KAAK,QAAQ;IAC5B,SAAS,GAAG;AACV,cAAQ,IACN,yEACA,CAAC;IAEL;AAEA,SAAK,WAAU;EACjB;;EAIQ,aAAa,MAAY;AAC/B,SAAK,OAAO,KAAK,IAAI;EACvB;EAEQ,sBAAmB;AACzB,WAAO,KAAK,SAAS,OAAO,KAAK,mBAAkB,CAAE;EACvD;EAEQ,qBAAkB;AACxB,UAAM,YAAY,IAAI,WAAW,KAAK,MAAM;AAC5C,SAAK,SAAS,CAAA;AACd,WAAO;EACT;EAEQ,aAAU;AAChB,SAAK,WAAW;MACd,SAAS;MACT,SAAS,CAAA;MACT,YAAY;;AAGd,SAAK,SAAS,CAAA;AACd,SAAK,aAAa;AAElB,SAAK,UAAU,KAAK;EACtB;;;;ACzQF,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;AC4IA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAGA,IAAY;CAAZ,SAAYC,mBAAgB;AAC1B,EAAAA,kBAAAA,kBAAA,YAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,MAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,kBAAAA,kBAAA,QAAA,IAAA,CAAA,IAAA;AACF,GALY,qBAAA,mBAAgB,CAAA,EAAA;AAU5B,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAAA,iBAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,iBAAAA,iBAAA,cAAA,IAAA,CAAA,IAAA;AACA,EAAAA,iBAAAA,iBAAA,UAAA,IAAA,CAAA,IAAA;AACF,GAJY,oBAAA,kBAAe,CAAA,EAAA;AAS3B,IAAY;CAAZ,SAAYC,uBAAoB;AAC9B,EAAAA,sBAAAA,sBAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,sBAAAA,sBAAA,aAAA,IAAA,CAAA,IAAA;AACF,GAHY,yBAAA,uBAAoB,CAAA,EAAA;AAQhC,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,UAAA,IAAA;AACA,EAAAA,gBAAA,QAAA,IAAA;AACF,GAHY,mBAAA,iBAAc,CAAA,EAAA;;;ADxKpB,IAAO,SAAP,MAAa;EAWjB,YACmB,WACA,YAAY,eAAe,UAC3B,QAAmB;AAFnB,SAAA,YAAA;AACA,SAAA,YAAA;AACA,SAAA,SAAA;AAbF,SAAA,gBAAgB;;;;SAI1B,KAAK,SAAS;;EAUrB;EAEO,MAAM,MAAmC;AAC9C,SAAK,KAAI;AAET,QAAI,KAAK,gBAAe,GAAI;AAC1B,WAAK,UAAU,IAAI;IACrB,OAAO;AACL,WAAK,YAAY,IAAI;IACvB;EACF;EAEO,OAAI;AACT,SAAK,cAAa;AAClB,SAAK,gBAAe;EACtB;EAEQ,kBAAe;AACrB,WAAO,OAAO,WAAY,eAAe,KAAK,cAAc,eAAe;EAC7E;EAEQ,UAAU,MAAmC;AACnD,SAAK,OAAO,oCAAoC;AAChD,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,IAAI,OACjB,IAAI,gBACF,IAAI,KAAK,CAAC,KAAK,aAAa,GAAG,EAAE,MAAM,kBAAiB,CAAE,CAAC,CAC5D;AAEH,WAAK,QAAQ,YAAY,CAAC,YAAY,KAAK,QAAQ,IAAI;IACzD;EACF;EAEQ,YAAY,MAAmC;AACrD,SAAK,OAAO,sCAAsC;AAClD,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,YAAY,KAAK,IAAG;AAC1B,WAAK,SAAS,YAAY,MAAK;AAC7B,aAAK,KAAK,IAAG,IAAK,SAAS;MAC7B,GAAG,KAAK,SAAS;IACnB;EACF;EAEQ,gBAAa;AACnB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,UAAS;AACtB,aAAO,KAAK;AACZ,WAAK,OAAO,6BAA6B;IAC3C;EACF;EAEQ,kBAAe;AACrB,QAAI,KAAK,QAAQ;AACf,oBAAc,KAAK,MAAM;AACzB,aAAO,KAAK;AACZ,WAAK,OAAO,+BAA+B;IAC7C;EACF;;;;AE1EF,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAKM,IAAO,WAAP,MAAe;;;;;;;EA6BnB,YAAmB,UAAkB;AAAlB,SAAA,WAAA;EAAqB;;;;EAKjC,oBAAiB;AACtB,WAAO,KAAK,SAAS,KAAK,GAAG;EAC/B;;;;EAKO,mBAAgB;AACrB,WAAO,KAAK,SAAS,IAAI,OAAK,IAAI,EAAE,QAAQ,KAAK,EAAE,CAAC,QAAQ;EAC9D;;AAvCc,SAAA,OAAO;AAIP,SAAA,OAAO;AAIP,SAAA,OAAO;AAKP,SAAA,UAAU,IAAI,SAAS;EACnC,SAAS;EACT,SAAS;EACT,SAAS;CACV;;;APKG,IAAO,eAAP,MAAmB;EAyCvB,IAAI,mBAAgB;AAClB,WAAO,KAAK;EACd;EAGA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;EAaA,YACU,SACD,YACP,QAA4B;AAFpB,SAAA,UAAA;AACD,SAAA,aAAA;AAbD,SAAA,aAAsB;AAuHb,SAAA,uBAEb;;MAEF,WAAW,WAAQ;AACjB,aAAK,MAAM,uBAAuB,MAAM,QAAQ,MAAM,EAAE;AACxD,aAAK,aAAa;AAClB,aAAK,oBAAoB,MAAM,QAAQ;AAEvC,YAAI,KAAK,sBAAsB,SAAS,MAAM;AAC5C,eAAK,sBAAsB;QAC7B;AAEA,aAAK,gBAAgB,MAAM,OAAO;AAClC,aAAK,UAAU,KAAK;MACtB;;MAGA,SAAS,WAAQ;AAQf,cAAM,eAAe,MAAM,QAAQ;AACnC,cAAM,YACJ,KAAK,eAAe,YAAY,KAAK,KAAK;AAG5C,cAAM,UAAU;AAEhB,cAAM,SAAS;AACf,cAAM,YACJ,KAAK,sBAAsB,SAAS,OAChC,QAAQ,QAAQ,MAChB,QAAQ,QAAQ,YAAY;AAIlC,gBAAQ,MAAM,CAAC,UAAwB,CAAA,MAAY;AACjD,iBAAO,OAAO,IAAI,WAAW,cAAc,OAAO;QACpD;AACA,gBAAQ,OAAO,CAAC,UAAwB,CAAA,MAAY;AAClD,iBAAO,OAAO,KAAK,WAAW,cAAc,OAAO;QACrD;AACA,kBAAU,OAAO;MACnB;;MAGA,SAAS,WAAQ;AACf,cAAM,WAAW,KAAK,iBAAiB,MAAM,QAAQ,YAAY,CAAC;AAClE,YAAI,UAAU;AACZ,mBAAS,KAAK;AAEd,iBAAO,KAAK,iBAAiB,MAAM,QAAQ,YAAY,CAAC;QAC1D,OAAO;AACL,eAAK,mBAAmB,KAAK;QAC/B;MACF;;MAGA,OAAO,WAAQ;AACb,aAAK,aAAa,KAAK;MACzB;;AAvKA,SAAK,WAAW;AAGhB,SAAK,iBAAiB,CAAA;AAGtB,SAAK,mBAAmB,CAAA;AAExB,SAAK,eAAe;AAEpB,SAAK,sBAAsB;AAE3B,SAAK,wBAAwB,KAAK,IAAG;AAErC,SAAK,QAAQ,OAAO;AACpB,SAAK,gBAAgB,OAAO;AAC5B,SAAK,iBAAiB,OAAO;AAC7B,SAAK,oBAAoB,OAAO;AAChC,SAAK,oBAAoB,OAAO;AAChC,SAAK,oBAAoB,OAAO;AAChC,SAAK,mBAAmB,OAAO;AAC/B,SAAK,wBAAwB,OAAO;AACpC,SAAK,sBAAsB,OAAO;AAClC,SAAK,sBAAsB,OAAO;AAClC,SAAK,8BAA8B,OAAO;AAC1C,SAAK,gCAAgC,OAAO;AAC5C,SAAK,YAAY,OAAO;AACxB,SAAK,eAAe,OAAO;AAC3B,SAAK,eAAe,OAAO;AAC3B,SAAK,mBAAmB,OAAO;AAC/B,SAAK,mBAAmB,OAAO;AAC/B,SAAK,qBAAqB,OAAO;AACjC,SAAK,qBAAqB,OAAO;AACjC,SAAK,mBAAmB,OAAO;EACjC;EAEO,QAAK;AACV,UAAM,SAAS,IAAI;;MAEjB,cAAW;AACT,cAAM,QAAQ,UAAU,aACtB,UACA,KAAK,mBAAmB;AAI1B,YAAI,CAAC,KAAK,qBAAqB;AAC7B,eAAK,MAAM,OAAO,KAAK,EAAE;QAC3B;AAEA,cAAM,qBACJ,KAAK,qBAAqB,MAAM,OAAO,KAAK,KAAK;AACnD,2BAAmB,KAAK;MAC1B;;MAEA,MAAK;AACH,aAAK,MAAM,UAAU;MACvB;IAAC;AAGH,SAAK,WAAW,YAAY,CAAC,QAAiC;AAC5D,WAAK,MAAM,eAAe;AAC1B,WAAK,wBAAwB,KAAK,IAAG;AAErC,UAAI,KAAK,qBAAqB;AAC5B,cAAM,mBACJ,IAAI,gBAAgB,cAChB,IAAI,YAAW,EAAG,OAAO,IAAI,IAAI,IACjC,IAAI;AACV,aAAK,MAAM,OAAO,gBAAgB,EAAE;MACtC;AAEA,aAAO,WACL,IAAI,MACJ,KAAK,2BAA2B;IAEpC;AAEA,SAAK,WAAW,UAAU,CAAC,eAAoB;AAC7C,WAAK,MAAM,wBAAwB,KAAK,WAAW,GAAG,EAAE;AACxD,WAAK,SAAQ;AACb,WAAK,iBAAiB,UAAU;IAClC;AAEA,SAAK,WAAW,UAAU,CAAC,eAAoB;AAC7C,WAAK,iBAAiB,UAAU;IAClC;AAEA,SAAK,WAAW,SAAS,MAAK;AAE5B,YAAM,iBAAkB,OAAe,OAAO,CAAA,GAAI,KAAK,cAAc;AAErE,WAAK,MAAM,sBAAsB;AACjC,qBAAe,gBAAgB,IAAI,KAAK,cAAc,kBAAiB;AACvE,qBAAe,YAAY,IAAI;QAC7B,KAAK;QACL,KAAK;QACL,KAAK,GAAG;AACV,WAAK,UAAU,EAAE,SAAS,WAAW,SAAS,eAAc,CAAE;IAChE;EACF;EAsEQ,gBAAgB,SAAqB;AAC3C,QACE,QAAQ,YAAY,SAAS,QAC7B,QAAQ,YAAY,SAAS,MAC7B;AACA;IACF;AAIA,QAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B;IACF;AAKA,UAAM,CAAC,gBAAgB,cAAc,IAAI,QAAQ,YAAY,EAC1D,MAAM,GAAG,EACT,IAAI,CAAC,MAAc,SAAS,GAAG,EAAE,CAAC;AAErC,QAAI,KAAK,sBAAsB,KAAK,mBAAmB,GAAG;AACxD,YAAM,MAAc,KAAK,IAAI,KAAK,mBAAmB,cAAc;AACnE,WAAK,MAAM,mBAAmB,GAAG,IAAI;AAErC,WAAK,UAAU,IAAI,OAAO,KAAK,KAAK,QAAQ,mBAAmB,KAAK,KAAK;AACzE,WAAK,QAAQ,MAAM,MAAK;AACtB,YAAI,KAAK,WAAW,eAAe,iBAAiB,MAAM;AACxD,eAAK,WAAW,KAAK,KAAK,EAAE;AAC5B,eAAK,MAAM,UAAU;QACvB;MACF,CAAC;IACH;AAEA,QAAI,KAAK,sBAAsB,KAAK,mBAAmB,GAAG;AACxD,YAAM,MAAc,KAAK,IAAI,KAAK,mBAAmB,cAAc;AACnE,WAAK,MAAM,oBAAoB,GAAG,IAAI;AACtC,WAAK,UAAU,YAAY,MAAK;AAC9B,cAAM,QAAQ,KAAK,IAAG,IAAK,KAAK;AAEhC,YAAI,QAAQ,MAAM,GAAG;AACnB,eAAK,MAAM,gDAAgD,KAAK,IAAI;AACpE,eAAK,yBAAwB;QAC/B;MACF,GAAG,GAAG;IACR;EACF;EAEQ,2BAAwB;AAC9B,QAAI,KAAK,+BAA+B;AACtC,WAAK,MACH,oEAAoE;AAEtE,WAAK,iBAAgB;IACvB,OAAO;AACL,WAAK,MAAM,gCAAgC;AAC3C,WAAK,gBAAe;IACtB;EACF;EAEO,kBAAe;AACpB,QAAI,KAAK,YAAY;AACnB,UACE,KAAK,WAAW,eAAe,iBAAiB,cAChD,KAAK,WAAW,eAAe,iBAAiB,MAChD;AACA,aAAK,yBAAwB;MAC/B;IACF;EACF;EAEO,kBAAe;AACpB,SAAK,WAAW,YAAY,MAAK;IAAE;AACnC,SAAK,WAAW,MAAK;EACvB;EAEO,mBAAgB;AACrB,QAAI,OAAO,KAAK,WAAW,cAAc,YAAY;AACnD,uBAAiB,KAAK,YAAY,CAAC,QAAgB,KAAK,MAAM,GAAG,CAAC;IACpE;AAGA,SAAK,WAAW,UAAS;EAC3B;EAEQ,UAAU,QAMjB;AACC,UAAM,EAAE,SAAS,SAAS,MAAM,YAAY,wBAAuB,IACjE;AACF,UAAM,QAAQ,IAAI,UAAU;MAC1B;MACA;MACA;MACA;MACA,oBAAoB,KAAK;MACzB;KACD;AAED,QAAI,WAAW,MAAM,UAAS;AAE9B,QAAI,KAAK,qBAAqB;AAC5B,WAAK,MAAM,OAAO,QAAQ,EAAE;IAC9B,OAAO;AACL,WAAK,MAAM,OAAO,KAAK,EAAE;IAC3B;AAEA,QAAI,KAAK,uBAAuB,OAAO,aAAa,UAAU;AAC5D,iBAAW,IAAI,YAAW,EAAG,OAAO,QAAQ;IAC9C;AAEA,QAAI,OAAO,aAAa,YAAY,CAAC,KAAK,kBAAkB;AAC1D,WAAK,WAAW,KAAK,QAAQ;IAC/B,OAAO;AACL,UAAI,MAAM;AACV,aAAO,IAAI,SAAS,GAAG;AACrB,cAAM,QAAQ,IAAI,UAAU,GAAG,KAAK,qBAAqB;AACzD,cAAM,IAAI,UAAU,KAAK,qBAAqB;AAC9C,aAAK,WAAW,KAAK,KAAK;AAC1B,aAAK,MAAM,gBAAgB,MAAM,MAAM,iBAAiB,IAAI,MAAM,EAAE;MACtE;IACF;EACF;EAEO,UAAO;AACZ,QAAI,KAAK,WAAW;AAClB,UAAI;AAEF,cAAM,oBAAqB,OAAe,OACxC,CAAA,GACA,KAAK,iBAAiB;AAGxB,YAAI,CAAC,kBAAkB,SAAS;AAC9B,4BAAkB,UAAU,SAAS,KAAK,UAAU;QACtD;AACA,aAAK,gBAAgB,kBAAkB,SAAS,WAAQ;AACtD,eAAK,gBAAe;AACpB,eAAK,SAAQ;AACb,eAAK,aAAa,KAAK;QACzB,CAAC;AACD,aAAK,UAAU,EAAE,SAAS,cAAc,SAAS,kBAAiB,CAAE;MACtE,SAAS,OAAO;AACd,aAAK,MAAM,oCAAoC,KAAK,EAAE;MACxD;IACF,OAAO;AACL,UACE,KAAK,WAAW,eAAe,iBAAiB,cAChD,KAAK,WAAW,eAAe,iBAAiB,MAChD;AACA,aAAK,gBAAe;MACtB;IACF;EACF;EAEQ,WAAQ;AACd,SAAK,aAAa;AAElB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,KAAI;AACjB,WAAK,UAAU;IACjB;AACA,QAAI,KAAK,SAAS;AAChB,oBAAc,KAAK,OAAO;AAC1B,WAAK,UAAU;IACjB;EACF;EAEO,QAAQ,QAAsB;AACnC,UAAM,EAAE,aAAa,SAAS,MAAM,YAAY,wBAAuB,IACrE;AACF,UAAM,OAAsB,OAAe,OAAO,EAAE,YAAW,GAAI,OAAO;AAC1E,SAAK,UAAU;MACb,SAAS;MACT,SAAS;MACT;MACA;MACA;KACD;EACH;EAEO,gBAAgB,WAAmB,UAA2B;AACnE,SAAK,iBAAiB,SAAS,IAAI;EACrC;EAEO,UACL,aACA,UACA,UAAwB,CAAA,GAAE;AAE1B,cAAW,OAAe,OAAO,CAAA,GAAI,OAAO;AAE5C,QAAI,CAAC,QAAQ,IAAI;AACf,cAAQ,KAAK,OAAO,KAAK,UAAU;IACrC;AACA,YAAQ,cAAc;AACtB,SAAK,eAAe,QAAQ,EAAE,IAAI;AAClC,SAAK,UAAU,EAAE,SAAS,aAAa,QAAO,CAAE;AAChD,UAAM,SAAS;AACf,WAAO;MACL,IAAI,QAAQ;MAEZ,YAAY,MAAI;AACd,eAAO,OAAO,YAAY,QAAQ,IAAI,IAAI;MAC5C;;EAEJ;EAEO,YAAY,IAAY,UAAwB,CAAA,GAAE;AACvD,cAAW,OAAe,OAAO,CAAA,GAAI,OAAO;AAE5C,WAAO,KAAK,eAAe,EAAE;AAC7B,YAAQ,KAAK;AACb,SAAK,UAAU,EAAE,SAAS,eAAe,QAAO,CAAE;EACpD;EAEO,MAAM,eAAqB;AAChC,UAAM,OAAO,iBAAiB,MAAM,KAAK,UAAU;AACnD,SAAK,UAAU;MACb,SAAS;MACT,SAAS;QACP,aAAa;;KAEhB;AACD,UAAM,SAAS;AACf,WAAO;MACL,IAAI;MACJ,SAAM;AACJ,eAAO,OAAO,IAAI;MACpB;MACA,QAAK;AACH,eAAO,MAAM,IAAI;MACnB;;EAEJ;EAEO,OAAO,eAAqB;AACjC,SAAK,UAAU;MACb,SAAS;MACT,SAAS;QACP,aAAa;;KAEhB;EACH;EAEO,MAAM,eAAqB;AAChC,SAAK,UAAU;MACb,SAAS;MACT,SAAS;QACP,aAAa;;KAEhB;EACH;EAEO,IACL,WACA,gBACA,UAAwB,CAAA,GAAE;AAE1B,cAAW,OAAe,OAAO,CAAA,GAAI,OAAO;AAE5C,QAAI,KAAK,sBAAsB,SAAS,MAAM;AAC5C,cAAQ,KAAK;IACf,OAAO;AACL,cAAQ,YAAY,IAAI;IAC1B;AACA,YAAQ,eAAe;AACvB,SAAK,UAAU,EAAE,SAAS,OAAO,QAAO,CAAE;EAC5C;EAEO,KACL,WACA,gBACA,UAAwB,CAAA,GAAE;AAE1B,cAAW,OAAe,OAAO,CAAA,GAAI,OAAO;AAE5C,QAAI,KAAK,sBAAsB,SAAS,MAAM;AAC5C,cAAQ,KAAK;IACf,OAAO;AACL,cAAQ,YAAY,IAAI;IAC1B;AACA,YAAQ,eAAe;AACvB,WAAO,KAAK,UAAU,EAAE,SAAS,QAAQ,QAAO,CAAE;EACpD;;;;AD3gBI,IAAO,SAAP,MAAa;;;;EAiKjB,IAAI,YAAS;AAhMf;AAiMI,YAAO,UAAK,kBAAL,mBAAoB;EAC7B;;;;EAYA,IAAI,oBAAiB;AACnB,WAAO,KAAK;EACd;EAEA,IAAI,kBAAkB,OAAmB;AACvC,SAAK,qBAAqB;AAC1B,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,oBAAoB,KAAK;IAC9C;EACF;;;;EAkCA,IAAI,YAAS;AACX,WAAO,CAAC,CAAC,KAAK,iBAAiB,KAAK,cAAc;EACpD;;;;EAsGA,IAAI,mBAAgB;AAClB,WAAO,KAAK,gBAAgB,KAAK,cAAc,mBAAmB;EACpE;;;;EAOA,IAAI,SAAM;AACR,WAAO,KAAK,UAAU,gBAAgB;EACxC;EASQ,aAAa,OAAsB;AACzC,SAAK,QAAQ;AACb,SAAK,cAAc,KAAK;EAC1B;;;;EAeA,YAAY,OAAoB,CAAA,GAAE;AAlV3B,SAAA,gBAAgB,SAAS;AA6BzB,SAAA,oBAA4B;AAQ5B,SAAA,iBAAyB;AAMxB,SAAA,sBAA8B;AAO/B,SAAA,oBAA4B,KAAK,KAAK;AActC,SAAA,oBAA0C,qBAAqB;AAK/D,SAAA,oBAA4B;AAK5B,SAAA,oBAA4B;AAiB5B,SAAA,oBAAoC,eAAe;AAenD,SAAA,mBAA4B;AAM5B,SAAA,wBAAgC,IAAI;AAUpC,SAAA,sBAA+B;AAY/B,SAAA,8BAAuC;AAiKvC,SAAA,gCAAyC;AAoCzC,SAAA,QAAyB,gBAAgB;AAS9C,UAAM,OAAO,MAAK;IAAE;AACpB,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB;AAGrB,SAAK,iBAAiB,CAAA;AACtB,SAAK,qBAAqB,CAAA;AAG1B,SAAK,UAAU,IAAI;EACrB;;;;EAKO,UAAU,MAAiB;AAE/B,WAAe,OAAO,MAAM,IAAI;AAGjC,QACE,KAAK,oBAAoB,KACzB,KAAK,oBAAoB,KAAK,gBAC9B;AACA,WAAK,MACH,+BAA+B,KAAK,iBAAiB,oCAAoC,KAAK,cAAc,2DAA2D;AAEzK,WAAK,oBAAoB,KAAK;IAChC;EACF;;;;;;;;;EAUO,WAAQ;AACb,UAAM,YAAY,MAAK;AACrB,UAAI,KAAK,QAAQ;AACf,aAAK,MAAM,8CAA8C;AACzD;MACF;AAEA,WAAK,aAAa,gBAAgB,MAAM;AAExC,WAAK,sBAAsB,KAAK;AAChC,WAAK,SAAQ;IACf;AAGA,QAAI,KAAK,UAAU,gBAAgB,cAAc;AAC/C,WAAK,MAAM,sDAAsD;AACjE,WAAK,WAAU,EAAG,KAAK,MAAK;AAC1B,kBAAS;MACX,CAAC;IACH,OAAO;AACL,gBAAS;IACX;EACF;EAEQ,MAAM,WAAQ;AACpB,UAAM,KAAK,cAAc,IAAI;AAE7B,QAAI,KAAK,eAAe;AACtB,WAAK,MACH,+DAA+D;AAEjE;IACF;AAEA,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,MACH,8DAA8D;AAEhE;IACF;AAGA,QAAI,KAAK,oBAAoB,GAAG;AAE9B,UAAI,KAAK,oBAAoB;AAC3B,qBAAa,KAAK,kBAAkB;MACtC;AACA,WAAK,qBAAqB,WAAW,MAAK;AACxC,YAAI,KAAK,WAAW;AAClB;QACF;AAGA,aAAK,MACH,iCAAiC,KAAK,iBAAiB,oBAAoB;AAE7E,aAAK,gBAAe;MACtB,GAAG,KAAK,iBAAiB;IAC3B;AAEA,SAAK,MAAM,uBAAuB;AAGlC,UAAM,YAAY,KAAK,iBAAgB;AAEvC,SAAK,gBAAgB,IAAI,aAAa,MAAM,WAAW;MACrD,OAAO,KAAK;MACZ,eAAe,KAAK;MACpB,gBAAgB,KAAK;MACrB,mBAAmB,KAAK;MACxB,mBAAmB,KAAK;MACxB,mBAAmB,KAAK;MACxB,mBAAmB,KAAK;MACxB,kBAAkB,KAAK;MACvB,uBAAuB,KAAK;MAC5B,qBAAqB,KAAK;MAC1B,qBAAqB,KAAK;MAC1B,6BAA6B,KAAK;MAClC,+BAA+B,KAAK;MAEpC,WAAW,WAAQ;AAEjB,YAAI,KAAK,oBAAoB;AAC3B,uBAAa,KAAK,kBAAkB;AACpC,eAAK,qBAAqB;QAC5B;AAEA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,MACH,sEAAsE;AAExE,eAAK,qBAAoB;AACzB;QACF;AACA,aAAK,UAAU,KAAK;MACtB;MACA,cAAc,WAAQ;AACpB,aAAK,aAAa,KAAK;MACzB;MACA,cAAc,WAAQ;AACpB,aAAK,aAAa,KAAK;MACzB;MACA,kBAAkB,SAAM;AACtB,aAAK,gBAAgB;AAErB,YAAI,KAAK,UAAU,gBAAgB,cAAc;AAE/C,eAAK,aAAa,gBAAgB,QAAQ;QAC5C;AAIA,aAAK,iBAAiB,GAAG;AAEzB,YAAI,KAAK,QAAQ;AACf,eAAK,oBAAmB;QAC1B;MACF;MACA,kBAAkB,SAAM;AACtB,aAAK,iBAAiB,GAAG;MAC3B;MACA,oBAAoB,aAAU;AAC5B,aAAK,mBAAmB,OAAO;MACjC;MACA,oBAAoB,WAAQ;AAC1B,aAAK,mBAAmB,KAAK;MAC/B;MACA,kBAAkB,WAAQ;AACxB,aAAK,iBAAiB,KAAK;MAC7B;KACD;AAED,SAAK,cAAc,MAAK;EAC1B;EAEQ,mBAAgB;AACtB,QAAI;AAEJ,QAAI,KAAK,kBAAkB;AACzB,kBAAY,KAAK,iBAAgB;IACnC,WAAW,KAAK,WAAW;AACzB,kBAAY,IAAI,UACd,KAAK,WACL,KAAK,cAAc,iBAAgB,CAAE;IAEzC,OAAO;AACL,YAAM,IAAI,MAAM,uDAAuD;IACzE;AACA,cAAU,aAAa;AACvB,WAAO;EACT;EAEQ,sBAAmB;AACzB,QAAI,KAAK,sBAAsB,GAAG;AAChC,WAAK,MACH,qCAAqC,KAAK,mBAAmB,IAAI;AAGnE,WAAK,eAAe,WAAW,MAAK;AAClC,YAAI,KAAK,sBAAsB,qBAAqB,aAAa;AAC/D,eAAK,sBAAsB,KAAK,sBAAsB;AAGtD,cAAI,KAAK,sBAAsB,GAAG;AAChC,iBAAK,sBAAsB,KAAK,IAC9B,KAAK,qBACL,KAAK,iBAAiB;UAE1B;QACF;AAEA,aAAK,SAAQ;MACf,GAAG,KAAK,mBAAmB;IAC7B;EACF;;;;;;;;;;;;;;;;;;;;;;;;EAyBO,MAAM,WAAW,UAA+B,CAAA,GAAE;AAjoB3D;AAkoBI,UAAM,QAAiB,QAAQ,SAAS;AACxC,UAAM,gBAAgB,KAAK;AAC3B,QAAI;AAEJ,QAAI,KAAK,UAAU,gBAAgB,UAAU;AAC3C,WAAK,MAAM,sCAAsC;AACjD,aAAO,QAAQ,QAAO;IACxB;AAEA,SAAK,aAAa,gBAAgB,YAAY;AAG9C,SAAK,sBAAsB;AAG3B,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAC9B,WAAK,eAAe;IACtB;AAEA,QACE,KAAK;IAEL,KAAK,UAAU,eAAe,iBAAiB,QAC/C;AACA,YAAM,uBAAuB,KAAK,cAAc;AAEhD,mBAAa,IAAI,QAAc,CAAC,SAAS,WAAU;AAEjD,aAAK,cAAc,mBAAmB,SAAM;AAC1C,+BAAqB,GAAG;AACxB,kBAAO;QACT;MACF,CAAC;IACH,OAAO;AAEL,WAAK,aAAa,gBAAgB,QAAQ;AAC1C,aAAO,QAAQ,QAAO;IACxB;AAEA,QAAI,OAAO;AACT,iBAAK,kBAAL,mBAAoB;IACtB,WAAW,eAAe;AACxB,WAAK,qBAAoB;IAC3B;AAEA,WAAO;EACT;;;;;;;EAQO,kBAAe;AACpB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,gBAAe;IACpC;EACF;EAEQ,uBAAoB;AAE1B,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAO;IAC5B;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCO,QAAQ,QAAsB;AACnC,SAAK,iBAAgB;AAErB,SAAK,cAAc,QAAQ,MAAM;EACnC;EAEQ,mBAAgB;AACtB,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,IAAI,UAAU,yCAAyC;IAC/D;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAqCO,gBAAgB,WAAmB,UAA2B;AACnE,SAAK,iBAAgB;AAErB,SAAK,cAAc,gBAAgB,WAAW,QAAQ;EACxD;;;;;;;;;;;;;;;;;;;;;;;;;EA0BO,UACL,aACA,UACA,UAAwB,CAAA,GAAE;AAE1B,SAAK,iBAAgB;AAErB,WAAO,KAAK,cAAc,UAAU,aAAa,UAAU,OAAO;EACpE;;;;;;;;;;;;;EAcO,YAAY,IAAY,UAAwB,CAAA,GAAE;AACvD,SAAK,iBAAgB;AAErB,SAAK,cAAc,YAAY,IAAI,OAAO;EAC5C;;;;;;;EAQO,MAAM,eAAsB;AACjC,SAAK,iBAAgB;AAErB,WAAO,KAAK,cAAc,MAAM,aAAa;EAC/C;;;;;;;;;;;;;EAcO,OAAO,eAAqB;AACjC,SAAK,iBAAgB;AAErB,SAAK,cAAc,OAAO,aAAa;EACzC;;;;;;;;;;;;EAaO,MAAM,eAAqB;AAChC,SAAK,iBAAgB;AAErB,SAAK,cAAc,MAAM,aAAa;EACxC;;;;;;;;;;;;;;EAeO,IACL,WACA,gBACA,UAAwB,CAAA,GAAE;AAE1B,SAAK,iBAAgB;AAErB,SAAK,cAAc,IAAI,WAAW,gBAAgB,OAAO;EAC3D;;;;;;;;;;;;;;EAeO,KACL,WACA,gBACA,UAAwB,CAAA,GAAE;AAE1B,SAAK,iBAAgB;AAErB,SAAK,cAAc,KAAK,WAAW,gBAAgB,OAAO;EAC5D;;;;ASh7BF,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACcA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAOM,IAAO,cAAP,MAAkB;;;;ACrBxB,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AASM,IAAO,eAAP,MAAmB;;;;ACTzB,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACEA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAKM,IAAO,gBAAP,MAAoB;EACxB,YAAoB,QAAoB;AAApB,SAAA,SAAA;EAAuB;EAE3C,IAAI,WAAQ;AACV,WAAO,KAAK,OAAO;EACrB;EAEA,IAAI,SAAS,OAAa;AACxB,SAAK,OAAO,oBAAoB;EAClC;EAEA,IAAI,WAAQ;AACV,WAAO,KAAK,OAAO;EACrB;EAEA,IAAI,SAAS,OAAa;AACxB,SAAK,OAAO,oBAAoB;EAClC;;;;ADVI,IAAO,eAAP,cAA4B,OAAM;;;;;;;;;EActC,YAAY,kBAA2B;AACrC,UAAK;AAXA,SAAA,wBAAgC,KAAK;AAoOpC,SAAA,iBAAgC,IAAI,cAAc,IAAI;AAxN5D,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AAExB,SAAK,QAAQ,IAAI,YAAkB;AACjC,cAAQ,IAAI,GAAG,OAAO;IACxB;EACF;EAEQ,iBAAiB,MAAW;AAClC,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,UAAwB,CAAA;AAC5B,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,IAAI,MAAM,uCAAuC;IACzD;AACA,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY;AACjC,OAAC,SAAS,iBAAiB,eAAe,kBAAkB,IAAI;IAClE,OAAO;AACL,cAAQ,KAAK,QAAQ;QACnB,KAAK;AACH;YACE,QAAQ;YACR,QAAQ;YACR;YACA;YACA;YACA,QAAQ;cACN;AACJ;QACF;AACE;YACE,QAAQ;YACR,QAAQ;YACR;YACA;YACA;cACE;MACR;IACF;AAEA,WAAO,CAAC,SAAS,iBAAiB,eAAe,kBAAkB;EACrE;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BO,WAAW,MAAW;AAC3B,UAAM,MAAM,KAAK,cAAc,GAAG,IAAI;AAEtC,QAAI,IAAI,CAAC,GAAG;AACV,WAAK,iBAAiB,IAAI,CAAC;IAC7B;AACA,QAAI,IAAI,CAAC,GAAG;AACV,WAAK,YAAY,IAAI,CAAC;IACxB;AACA,QAAI,IAAI,CAAC,GAAG;AACV,WAAK,eAAe,IAAI,CAAC;IAC3B;AACA,QAAI,IAAI,CAAC,GAAG;AACV,WAAK,mBAAmB,IAAI,CAAC;IAC/B;AAEA,UAAM,SAAQ;EAChB;;;;;;;;;;;;EAaO,WACL,oBACA,UAAwB,CAAA,GAAE;AAE1B,QAAI,oBAAoB;AACtB,WAAK,eAAe;IACtB;AACA,SAAK,oBAAoB;AAEzB,UAAM,WAAU;EAClB;;;;;;;;;;;;;;;;;;;;;EAsBO,KACL,aACA,UAAkC,CAAA,GAClC,OAAe,IAAE;AAEjB,cAAW,OAAe,OAAO,CAAA,GAAI,OAAO;AAE5C,UAAM,0BAA0B,QAAQ,gBAAgB,MAAM;AAC9D,QAAI,yBAAyB;AAC3B,aAAO,QAAQ,gBAAgB;IACjC;AACA,SAAK,QAAQ;MACX;MACA;MACA;MACA;KACD;EACH;;;;;;EAOA,IAAI,gBAAgB,OAAa;AAC/B,SAAK,iBAAiB;EACxB;;;;;;EAOA,IAAI,KAAE;AACJ,WAAO,KAAK;EACd;;;;;;EAOA,IAAI,UAAO;AACT,WAAO,KAAK;EACd;;;;;;EAOA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;;;;;;EAOA,IAAI,UAAU,OAA0B;AACtC,SAAK,qBAAqB;EAC5B;;;;;;;EAQA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;;;;;;EAOA,IAAI,UAAU,OAAwB;AACpC,SAAK,qBAAqB;EAC5B;;;;;;;EAUA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;;;;;;;EAQA,IAAI,UAAU,OAA6C;AACzD,SAAK,oBAAoB,MAAM;AAC/B,SAAK,oBAAoB,MAAM;EACjC;;;;AE3QF,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAqBM,IAAO,QAAP,MAAO,OAAK;;;;;;;;;;;;;;;EAmCT,OAAO,OAAO,KAAa,WAAoB;AAcpD,QAAI,aAAa,MAAM;AACrB,kBAAY,SAAS,QAAQ,iBAAgB;IAC/C;AACA,UAAM,OAAO,MAAK;AAChB,YAAM,QAAQ,OAAM,kBAAkB;AACtC,aAAO,IAAI,MAAM,KAAK,SAAS;IACjC;AAEA,WAAO,IAAI,aAAa,IAAI;EAC9B;;;;;;;;;;;;;;;;;;;;;;EAuBO,OAAO,KAAK,IAAO;AACxB,QAAI;AAEJ,QAAI,OAAO,OAAO,YAAY;AAC5B,aAAO;IACT,OAAO;AACL,cAAQ,KACN,mJACiF;AAEnF,aAAO,MAAM;IACf;AAEA,WAAO,IAAI,aAAa,IAAI;EAC9B;;AA5Ec,MAAA,iBAAsB;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "StompSocketState", "ActivationState", "ReconnectionTimeMode", "TickerStrategy", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist"]}