import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  TextStyle
} from "./chunk-C5OEVUPO.js";
import "./chunk-THYJHTXW.js";
import "./chunk-REYSTJ5T.js";
import "./chunk-AL46U7Q7.js";
import "./chunk-KTHVQD2K.js";
import "./chunk-5PV7NXTD.js";
import "./chunk-O7RF7KNN.js";
import "./chunk-ZMSOBIYE.js";
export {
  TextStyle,
  TextStyle as default
};
